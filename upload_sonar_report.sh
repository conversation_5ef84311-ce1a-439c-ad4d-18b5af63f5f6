#!/bin/bash
stage=${ENV:-dev2}
export REGION=${REGION:-ap-southeast-1}
echo ${REGION}
releaseBranch=${releaseVersionId}
echo "releaseVersionId is ${releaseVersionId}"
echo_ssm_secret()
{
  echo $(aws --region ap-southeast-1 ssm get-parameters --with-decryption --name $1 | jq '.Parameters[0].Value' | sed 's/"//g')
}
echo "Scanning code and uploading reports to SONAR SERVER..."

npx sonar-scanner \
  -Dsonar.host.url=https://sonar-dev.fleetship.com/\
  ${releaseVersionId:+-Dsonar.projectName="paris2-api-new-building-R${releaseVersionId}"} \
  -Dsonar.login=$(echo_ssm_secret "/paris2-sonar-auth-token/${stage}") -X

export FOLDER_NAME=`date "+%F-%H-%M-%S"`
aws s3api put-object --bucket paris2-api-new-building-reporting-code-coverage --key api/${FOLDER_NAME}/
mkdir tmp
find -maxdepth 3 -type d -iname coverage -print | grep -v node_modules | xargs tar cf x.tgz && tar xf x.tgz -C tmp/
cd tmp
find -maxdepth 4 -type f -iname index.html | grep service| grep coverage| grep -v src |cut -c2- > message.txt
sed -i -e "s#^#"https://paris2-api-new-building-reporting-code-coverage.s3.ap-southeast-1.amazonaws.com/api/${FOLDER_NAME}"#" message.txt
cat message.txt
aws s3 sync ../tmp/ s3://paris2-api-new-building-reporting-code-coverage/api/${FOLDER_NAME}