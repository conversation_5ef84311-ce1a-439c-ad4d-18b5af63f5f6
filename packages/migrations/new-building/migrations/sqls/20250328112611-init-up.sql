/* Replace with your SQL commands */ CREATE SCHEMA main;
-- the asset changes has been made as per the ADR call on 27th May 2025 on projects, hulls, drawings and inspections
SET
    search_path TO main,
    public;

-- Table: fuel_type
-- 1 as ACTIVE and 2 as INACTIVE
CREATE TABLE
    main.fuel_type (
        id SERIAL PRIMARY KEY,
        name VA<PERSON>HAR NOT NULL,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by <PERSON><PERSON><PERSON><PERSON>,
        updated_at TIMESTAMP,
        updated_by VARCHA<PERSON>,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

-- Table: project_type
-- 1 as ACTIVE and 2 as INACTIVE
CREATE TABLE
    main.project_type (
        id SERIAL PRIMARY KEY,
        name VARCHAR NOT NULL,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

-- Table: project 
-- 1 as ON_GOING and 2 as CLOSED
CREATE TABLE
    main.project (
        id SERIAL PRIMARY KEY,
        tenant_id INTEGER,
        name VA<PERSON><PERSON>R NOT NULL,
        owner VARCHAR,
        fuel_type_id INTEGER REFERENCES fuel_type (id),
        project_type_id INTEGER REFERENCES project_type (id),
        engine_type VARCHAR NOT NULL,
        ship_type VARCHAR NOT NULL,
        project_description TEXT,
        total_time_taken BIGINT,
        contract_budget BIGINT,
        expenses BIGINT,
        currency VARCHAR,
        completion_date TIMESTAMP,
        asset_name VARCHAR NOT NULL,
        asset_type VARCHAR(255) NOT NULL,
        asset_path VARCHAR,
        is_ship_type_custom BOOLEAN DEFAULT FALSE,
        is_engine_type_custom BOOLEAN DEFAULT FALSE,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );
-- Table: hull 
-- status ->  1 as ON_GOING, 2 as ON_HOLD and 3 as COMPLETED
CREATE TABLE
    main.hull (
        id SERIAL PRIMARY KEY,
        hull_no VARCHAR NOT NULL,
        tenant_id INTEGER,
        ship_name VARCHAR,
        imo INTEGER,
        flag VARCHAR NOT NULL,
        deadweight_gt BIGINT,
        deadweight_nt BIGINT,
        steel_cutting_date DATE NOT NULL,
        keel_laid_date DATE NOT NULL,
        launch_date DATE NOT NULL,
        capacity BIGINT,
        sea_trial_date DATE NOT NULL,
        delivery_date DATE NOT NULL,
        asset_name VARCHAR NOT NULL,
        asset_type VARCHAR(255) NOT NULL,
        asset_path VARCHAR,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2, 3)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

-- Table: hull_class 
CREATE TABLE
    main.hull_class (
        id SERIAL PRIMARY KEY,
        vessel_class VARCHAR NOT NULL,
        hull_id INTEGER NOT NULL REFERENCES hull (id),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );    

-- Table: project_hull
CREATE TABLE
    main.project_hull (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES project (id),
        hull_id INTEGER REFERENCES hull (id),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );


-- inspection_source_file
CREATE TABLE main.inspection_source_file(
     id SERIAL PRIMARY KEY,
     project_id INTEGER REFERENCES project (id),
     hull_id INTEGER REFERENCES hull (id),
     source_file_path VARCHAR,
     created_at TIMESTAMP,
     updated_at TIMESTAMP
);
-- Table: inspection
-- type: 1 for Hull's
-- status: 1 for ONGOING and 2 for CLOSED
CREATE TABLE
    main.inspection (
        id SERIAL PRIMARY KEY,
        tenant_id INTEGER,
        type INTEGER NOT NULL CHECK (type IN (1)),
        hull_id INTEGER REFERENCES hull (id),
        project_id INTEGER REFERENCES project (id),
        inspection_source_file_id INTEGER REFERENCES inspection_source_file (id),
        submission_date DATE,
        due_date DATE,
        time TIME,
        description VARCHAR,
        discipline VARCHAR,
        for_owner BOOLEAN,
        for_class BOOLEAN,
        remark VARCHAR,
        status INTEGER NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

CREATE TABLE
    main.inspection_comment (
        id SERIAL PRIMARY KEY,
        inspection_id INTEGER REFERENCES inspection (id),
        comments TEXT,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

-- Table: observation
CREATE TABLE
    main.inspection_observation (
        id SERIAL PRIMARY KEY,
        asset_name VARCHAR NOT NULL,
        asset_type VARCHAR(255) NOT NULL,
        asset_path VARCHAR,
        version INT,
        inspection_id INTEGER REFERENCES inspection (id),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

    CREATE UNIQUE INDEX IF NOT EXISTS idx_inspections_unique_active
ON inspection(description, project_id, hull_id)
WHERE deleted_at IS NULL;

-- project_drawing_list_resource_file to store the details of source file
CREATE TABLE main.project_drawing_list_source_file(
     id SERIAL PRIMARY KEY,
     project_id INTEGER REFERENCES project (id),
     source_file_path VARCHAR,
     created_at TIMESTAMP,
     updated_at TIMESTAMP
);
-- Table: project_drawings_list
-- status -> 1 as  ONGOING, 2 as COMPLETED
CREATE TABLE
    main.project_drawing_list (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES project (id),
        name VARCHAR,
        tenant_id INTEGER,
        project_drawing_list_source_file_id INTEGER REFERENCES project_drawing_list_source_file (id),
        discipline VARCHAR,
        drawing_no VARCHAR,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );

-- Table: project_drawings
-- status -> 1 as  UNDER REVIEW as PROJECT,  2 as REFERENCE ONLY, 3 as APPROVED
CREATE TABLE main.project_drawing (
  id SERIAL PRIMARY KEY,
  drawing_list_id INTEGER REFERENCES project_drawing_list (id),
  name VARCHAR,
  due_date DATE,
  tenant_id INTEGER,
  asset_name VARCHAR NOT NULL,
  asset_type VARCHAR(255) NOT NULL,
  asset_path VARCHAR,
  version INTEGER DEFAULT 0,
  status INTEGER NOT NULL CHECK (status IN (1, 2, 3)),
  created_at TIMESTAMP,
  created_by VARCHAR,
  updated_at TIMESTAMP,
  updated_by VARCHAR,
  deleted_at TIMESTAMP,
  deleted_by VARCHAR
);

-- Table: comment
-- comments_type: 1 as DRAWING and 2 as INSPECTION
-- status: 1 as OPEN and 2 as CLOSED
CREATE TABLE
    main.project_drawing_list_comment (
        id SERIAL PRIMARY KEY,
        project_drawing_list_id INTEGER REFERENCES project_drawing_list (id),
        comments TEXT,
        status INTEGER DEFAULT 1 NOT NULL CHECK (status IN (1, 2)),
        created_at TIMESTAMP,
        created_by VARCHAR,
        updated_at TIMESTAMP,
        updated_by VARCHAR,
        deleted_at TIMESTAMP,
        deleted_by VARCHAR
    );
    
INSERT INTO main.project_type (
    name,
    status,
    created_by
) VALUES
    ('New Building', 1, '0000'),
    ('Dry Docking', 1, '0000'),
    ('Retrofit', 1, '0000');

INSERT INTO main.fuel_type (
    name,
    status,
    created_by
) VALUES
    ('LNG', 1, '0000'),
    ('Methanol', 1,'0000'),
	('Conventional', 1, '0000'),
    ('Ammonia', 1, '0000');
