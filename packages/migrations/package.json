{"name": "@paris2-api-new-building/migrations", "version": "1.0.0", "description": "Project migrations", "main": "index.js", "scripts": {"db:migrate": "run-s db:migrate:*", "db:migrate-down": "run-s db:migrate-down:*", "db:migrate-reset": "run-s db:migrate-reset:*", "db:migrate:new-building-service": "db-migrate up --config new-building/database.json -m new-building/migrations", "db:migrate-down:new-building-service": "db-migrate down --config new-building/database.json -m new-building/migrations", "db:migrate-reset:new-building-service": "db-migrate reset --config new-building/database.json -m new-building/migrations"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"db-migrate": "^0.11.14", "db-migrate-pg": "^1.5.2", "npm-run-all": "^4.1.5", "pg": "^8.14.1"}}