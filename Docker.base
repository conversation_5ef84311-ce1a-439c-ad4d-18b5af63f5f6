FROM node:22-alpine AS base

WORKDIR /app

# Install dependencies for node-gyp and other build tools
RUN apk add --no-cache python3 make g++ 

# Copy package files
COPY package*.json ./
COPY packages/shared/package*.json ./packages/shared/
COPY services/new-building/package*.json ./services/new-building/

# Copy TypeScript configurations
COPY tsconfig*.json ./
COPY packages/shared/tsconfig.json ./packages/shared/
COPY services/new-building/tsconfig.json ./services/new-building/

# Install dependencies
RUN npm ci

# Copy source code
COPY packages/shared/src ./packages/shared/src
COPY services/new-building/src ./services/new-building/src

# Build packages
RUN npm run build