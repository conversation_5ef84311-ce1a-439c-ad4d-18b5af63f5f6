{"name": "paris2-api-new-building", "version": "1.0.0", "description": "paris2 api new building", "private": true, "workspaces": ["services/*"], "scripts": {"clean": "npm run clean --workspaces", "build": "tsc -b", "dev": "docker compose up --build", "start": "docker compose up", "down": "docker compose down", "logs": "docker compose logs -f", "ps": "docker compose ps", "prettier:fix": "npm run prettier:fix --workspaces", "install-all": "npm install && npm run install-services && npm run install-packages", "install-services": "npm run install-new-building && npm run install-shared", "install-packages": "npm run install-migrations", "install-new-building": "cd services/new-building && npm install", "install-shared": "cd services/shared && npm install", "install-migrations": "cd packages/migrations && npm install", "build-migrations": "cd packages/migrations && npm run build", "type-check": "npm run type-check --workspaces", "prepare": "husky install", "test": "npm run test:all", "test:all": "npm run test:new-building && npm run test:shared", "test:new-building": "cd services/new-building && npm test", "test:shared": "cd services/shared && npm test", "test:coverage": "npm run test:new-building:coverage && npm run test:shared:coverage", "test:new-building:coverage": "cd services/new-building && npm run test:coverage", "test:shared:coverage": "cd services/shared && npm run test:coverage", "docker:push": "sudo docker push $IMAGE_REPO_NAME/$npm_package_name:$npm_package_version", "docker:build:dev2": "DOCKER_BUILDKIT=1  sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE -t $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION .", "docker:push:dev2": "sudo docker push $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION", "docker:build:qa2": "DOCKER_BUILDKIT=1  sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE -t $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION .", "docker:push:qa2": "sudo docker push $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION", "docker:build:uat2": "DOCKER_BUILDKIT=1  sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE -t $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION .", "docker:push:uat2": "sudo docker push $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION", "docker:build:live": "DOCKER_BUILDKIT=1  sudo docker build --build-arg NR_ENABLED=$NR_ENABLED_VALUE -t $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION .", "docker:push:live": "sudo docker push $IMAGE_REPO_NAME:$npm_package_name-$IMAGE_TAG_VERSION"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/chai": "^5.2.1", "@types/mocha": "^10.0.10", "@types/node": "^22.13.13", "@types/sinon": "^17.0.4", "chai": "^5.2.0", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "db-migrate": "^0.11.14", "db-migrate-pg": "^1.5.2", "husky": "^9.1.7", "mocha": "^11.1.0", "nyc": "^17.1.0", "sinon": "^20.0.0", "sonar-scanner": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "dependencies": {"@aws-sdk/client-eventbridge": "^3.821.0"}}