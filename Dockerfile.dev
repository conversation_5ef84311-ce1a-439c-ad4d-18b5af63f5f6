FROM node:22-alpine

WORKDIR /app

# Add development dependencies
RUN apk add --no-cache python3 make g++ bash

# Copy package files for initial install
COPY package*.json ./

# We'll mount the source code as volumes, so we only need 
# to copy package.json files for npm install
COPY services/shared/package.json ./services/shared/
COPY services/new-building/package.json ./services/new-building/

# Install all dependencies (including dev dependencies)
RUN npm install

# The source code will be mounted as volumes
# The command will be specified in docker-compose.yml

# Expose port
EXPOSE 3000

# This keeps the container running
CMD ["tail", "-f", "/dev/null"]