# Paris2 API New Building

A TypeScript-based API service for managing new building projects in the maritime industry.

## Project Overview

This project is structured as a monorepo using npm workspaces, containing the following main components:

- **services/new-building**: Main API service for new building operations
- **services/shared**: Shared utilities and types
- **packages/migrations**: Database migration scripts

## Prerequisites

- Node.js 22+
- <PERSON><PERSON> and Docker Compose
- PostgreSQL 14+
- npm 7+ (for workspace support)

## Getting Started

### Installation

1. Clone the repository
2. Install all dependencies:
    ```bash
    npm run install-all
    ```

### Environment Setup

Create `.env` files in the following locations:

- `.env` in the root directory
- `.env` in `services/new-building`
- `.env` in `services/shared`
- `.env` in `packages/migrations`

### Required Environment Variables

Ensure you have the necessary environment variables specified in your `.env` files.

### Development

Start the development server with hot reload:
    ```bash
    npm run dev
    ```

Other useful commands:
    ```bash
    npm run build       # Build all packages
    npm run clean       # Clean build outputs
    npm run type-check  # Run TypeScript type checking
    ```

### Database Migrations

Run database migrations:
    ```bash
    npm run db:migrate
    ```

Rollback migrations:
    ```bash
    npm run db:migrate-down
    ```

Reset database:
    ```bash
    npm run db:migrate-reset
    ```

### Executing Migrations

1. Navigate to the `packages/migrations` directory:
    ```bash
    cd packages/migrations
    ```
2. Run all migrations against the database specified in the `packages/migrations/.env` file:
    ```bash
    npm run db:migrate
    ```

## Docker Support

### Development Environment

Set up and run your development environment using Docker and Docker Compose.

## Contributing

- Use Commitizen for commit messages
- Follow conventional commits specification
- Reference issue numbers (NBP-XXX) in commit messages
- Run tests, type and git-secrets checks before committing

## License

ISC

## Author

davindersf