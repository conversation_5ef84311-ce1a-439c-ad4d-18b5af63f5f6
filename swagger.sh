#!/bin/bash
stage=${stageName:-dev2}
export REGION=${region:-ap-southeast-1}
echo ${REGION}
echo_swagger_password()
{
  echo $(aws --region ${REGION} ssm get-parameters --with-decryption --name $1 | jq '.Parameters[0].Value' | sed 's/"//g')
}
curl -o openapi.json https://paris2-new-building-api-${stage}.fleetship.com/swagger.json
jq '.components.securitySchemes = {
         keycloak: {
             type: "oauth2",
             description: "keycloak oauth",
             flows: {
                 implicit: {
                     authorizationUrl: "https://auth-'${stage}'.fleetship.com/auth/realms/paris2/protocol/openid-connect/auth",
                     scopes: {}
                 }
             }
         }
     }' openapi.json > temp.json
jq '.servers[0].url = "https://paris2-new-building-api-'${stage}'.fleetship.com"' temp.json > swagger-newbuilding.json
aws s3 cp ./swagger-newbuilding.json s3://paris2-static-${stage}/swagger/${stage}/swagger-newbuilding.json --acl public-read