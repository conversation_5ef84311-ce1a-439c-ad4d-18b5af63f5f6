{"name": "@paris2-api-new-building/shared", "version": "1.0.0", "private": true, "description": "Shared packages", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "tsc", "build:watch": "tsc --watch", "prettier:cli": "prettier", "prettier:check": "npm run prettier:cli -- --check \"src/**/*.ts\"", "prettier:fix": "npm run prettier:cli -- --write \"src/**/*.ts\"", "test": "mocha --r ts-node/register ./src/__tests__/**/*.spec.ts", "test:watch": "mocha --r ts-node/register --watch ./src/__tests__/**/*.spec.ts", "test:coverage": "nyc mocha --r ts-node/register ./src/__tests__/**/*.spec.ts"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"dotenv": "^16.4.7", "joi": "^17.13.3", "winston": "^3.17.0"}, "devDependencies": {"@types/chai": "^5.2.1", "@types/mocha": "^10.0.10", "@types/node": "^18.15.11", "@types/sinon": "^17.0.4", "chai": "^5.2.0", "mocha": "^11.1.0", "nyc": "^17.1.0", "prettier": "^3.5.3", "sinon": "^20.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}