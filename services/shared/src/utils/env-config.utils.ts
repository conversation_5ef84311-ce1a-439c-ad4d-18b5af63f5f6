import * as <PERSON><PERSON> from 'joi';
import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

export interface EnvValidationResult<T> {
  parsed: T;
  errors?: string[];
}

export class EnvironmentValidator {
  private readonly schema: Joi.ObjectSchema;
  private readonly envFilePath: string;
  private readonly defaultEnvPath: string;
  private envConfig: {[key: string]: string} = {};

  constructor(
    schema: Joi.ObjectSchema,
    options: {
      serviceDir: string;
      envFileName?: string;
      defaultEnvFileName?: string;
    },
  ) {
    this.schema = schema;
    this.envFilePath = path.resolve(
      options.serviceDir,
      options.envFileName ?? '.env',
    );
    this.defaultEnvPath = path.resolve(
      options.serviceDir,
      options.defaultEnvFileName ?? '.env.default',
    );
    this.loadEnvFiles();
  }

  private loadEnvFiles(): void {
    try {
      // Load .env.default first as the base configuration
      if (fs.existsSync(this.defaultEnvPath)) {
        const defaultEnv = dotenv.parse(fs.readFileSync(this.defaultEnvPath));
        this.envConfig = {...defaultEnv};
      }

      // Load .env and override default values
      if (fs.existsSync(this.envFilePath)) {
        const env = dotenv.parse(fs.readFileSync(this.envFilePath));
        this.envConfig = {...this.envConfig, ...env};
      }

      // Set environment variables if they don't exist
      Object.entries(this.envConfig).forEach(([key, value]) => {
        if (!(key in process.env)) {
          process.env[key] = value;
        }
      });

      // Validate the loaded environment variables
      this.validate();
    } catch (error) {
      console.error('Error loading environment files:', error);
      process.exit(1);
    }
  }

  public validate<T>(): EnvValidationResult<T> {
    const {error, value} = this.schema.validate(process.env, {
      stripUnknown: true,
      abortEarly: false,
    });

    if (error) {
      throw new Error(error.message);
    }

    return {parsed: value as T};
  }

  public getEffectiveConfig<T>() {
    return {...this.envConfig} as T;
  }
}
