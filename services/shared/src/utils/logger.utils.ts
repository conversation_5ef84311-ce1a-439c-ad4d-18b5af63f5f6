import winston from 'winston';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

/**
 * Creates a replacer function for `JSON.stringify` that removes circular references
 * from objects to prevent errors during serialization.
 *
 * @returns A function that can be used as the second argument to `JSON.stringify`.
 *          It checks for circular references and replaces them with `undefined`.
 *
 * @example
 * ```typescript
 * const obj: any = {};
 * obj.self = obj; // Circular reference
 * const jsonString = JSON.stringify(obj, removeCircularReferences());
 * console.log(jsonString); // Output: "{}"
 * ```
 */
export function removeCircularReferences() {
  const seen = new WeakSet();
  return (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return undefined; // Remove circular reference
      }
      seen.add(value);
    }
    return value;
  };
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
  winston.format.printf(info => {
    // Check if `info.message` is an object or a string
    const message =
      typeof info.message === 'string'
        ? info.message
        : JSON.stringify(info.message, removeCircularReferences());
    // Check for additional metadata and include it in the log
    const meta = info[Symbol.for('splat')]
      ? JSON.stringify(info[Symbol.for('splat')], removeCircularReferences())
      : '';
    return `${info.timestamp} [${info.level.toUpperCase()}]: ${message} ${meta}\n${info.stack}`;
  }),
);

/**
 * Creates a logger instance using Winston with predefined configurations
 * based on the provided environment.
 *
 * @param nodeEnv - The current environment of the application.
 *                  It can be one of the following values:
 *                  - 'production': Sets the logger level to 'error'.
 *                  - 'development': Sets the logger level to 'debug'.
 *                  - 'qa': Sets the logger level to 'debug'.
 *                  - 'staging': Sets the logger level to 'debug'.
 *
 * @returns A configured Winston logger instance.
 *
 * @remarks
 * - The logger uses different levels and formats depending on the environment.
 * - In production, only error-level logs are shown.
 * - In non-production environments, debug-level logs are shown.
 * - The logger includes a console transport by default.
 * - File transports are commented out but can be enabled if needed.
 *
 * @example
 * ```typescript
 * import loggerFactory from './logger.utils';
 *
 * const logger = loggerFactory('development');
 * logger.debug('This is a debug message');
 * logger.error('This is an error message');
 * ```
 */
export function loggerFactory(level: keyof typeof logLevels = 'error') {
  // Create the logger instance
  const logger = winston.createLogger({
    level,
    levels: logLevels,
    format: logFormat,
    transports: [
      new winston.transports.Console({
        level,
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.printf(info => {
            const message =
              typeof info.message === 'string'
                ? info.message
                : JSON.stringify(info.message, removeCircularReferences());
            const meta = info[Symbol.for('splat')]
              ? JSON.stringify(
                  info[Symbol.for('splat')],
                  removeCircularReferences(),
                )
              : '';
            return `${info.timestamp} [${info.level}]: ${message} ${meta}\n${info.stack}`;
          }),
        ),
      }),
    ],
  });

  return logger;
}