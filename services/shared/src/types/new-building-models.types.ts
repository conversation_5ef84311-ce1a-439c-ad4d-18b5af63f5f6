export interface BaseModel {
  id: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by: string;
}

export interface Project extends BaseModel {
  project_number: string;
  name: string;
  description?: string;
  status: 'ACTIVE' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED';
  start_date?: Date;
  expected_delivery_date?: Date;
  metadata: Record<string, any>;
}

export interface Hull extends BaseModel {
  project_id: string;
  hull_number: string;
  ship_type?: string;
  length_overall?: number;
  beam?: number;
  depth?: number;
  draft?: number;
  dead_weight?: number;
  technical_specifications: Record<string, any>;
}
