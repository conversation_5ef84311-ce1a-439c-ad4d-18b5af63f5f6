# Path to sources
sonar.sources=.
sonar.exclusions = **/node_modules/**, **/test/**, commitlint.config.js, **/coverage/**, **/*.json, **/public/**, **/__tests__/**, **/.bin/**, **/application.*s, **/migrate.*s, **/database/**, **/datasources/**

#sonar.inclusions=packages/**/src/**

# Path to tests
sonar.tests=.
#sonar.test.exclusions=
#sonar.test.inclusions=

# Source encoding
sonar.sourceEncoding=UTF-8

#coverage
#sonar.javascript.lcov.reportPaths=

# Exclusions for copy-paste detection
#sonar.cpd.exclusions=

sonar.host.url=https://sonarcloud.io