{"name": "@paris2-api-new-building/new-building", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "db:migrate": "run-s db:migrate:*", "db:migrate-down": "run-s db:migrate-down:*", "db:migrate-reset": "run-s db:migrate-reset:*", "db:migrate:new-building-service": "cd ../../packages/migrations && db-migrate up --config new-building/database.json -m new-building/migrations", "db:migrate-down:new-building-service": "cd ../../packages/migrations && db-migrate down --config new-building/database.json -m new-building/migrations", "db:migrate-reset:new-building-service": "cd ../../packages/migrations && db-migrate reset --config new-building/database.json -m new-building/migrations", "dev": "ts-node-dev --respawn src/index.ts", "prettier:cli": "prettier", "prettier:check": "npm run prettier:cli -- --check \"src/**/*.ts\"", "prettier:fix": "npm run prettier:cli -- --write \"src/**/*.ts\"", "test": "mocha --r ts-node/register ./src/__tests__/**/*.spec.ts", "test:watch": "mocha --r ts-node/register --watch ./src/__tests__/**/*.spec.ts", "test:coverage": "nyc mocha --r ts-node/register ./src/__tests__/**/*.spec.ts"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/client-sns": "^3.817.0", "@aws-sdk/client-sqs": "^3.817.0", "@aws-sdk/s3-presigned-post": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@paris2-api-new-building/shared": "^1.0.0", "@types/heic-convert": "^2.1.0", "@types/joi": "^17.2.3", "axios": "^1.8.4", "content-disposition": "^0.5.4", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dayjs": "^1.11.13", "db-migrate": "^0.11.14", "db-migrate-pg": "^1.5.2", "exceljs": "^4.4.0", "express": "^4.21.2", "heic-convert": "^2.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "npm-run-all": "^4.1.5", "pg": "^8.14.1", "sequelize": "^6.37.7", "sharp": "^0.34.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/chai": "^5.2.1", "@types/content-disposition": "^0.5.8", "@types/csv-parse": "^1.1.12", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mocha": "^10.0.10", "@types/sequelize": "^4.28.20", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "chai": "^5.2.0", "mocha": "^11.1.0", "nyc": "^17.1.0", "prettier": "^3.5.3", "sinon": "^20.0.0", "sonar-scanner": "^3.1.0", "supertest": "^7.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}