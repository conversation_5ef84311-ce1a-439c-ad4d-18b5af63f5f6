import {Models} from '../models';
import BaseRepository from './base.repository';

export function createRepositories(models: Models) {
  return {
    projectRepository: new BaseRepository(models.Project),
    hullRepository: new BaseRepository(models.Hull),
    drawingSourceRepository: new BaseRepository(models.DrawingSource),
    inspectionSourceRepository: new BaseRepository(models.InspectionSource),
    drawingListCommentRepository: new BaseRepository(models.DrawingListComment),
    inspectionSourceFileRepository: new BaseRepository(
      models.InspectionSourceFile,
    ),
    inspectionRepository: new BaseRepository(models.Inspection),
    inspectionCommentRepository: new BaseRepository(models.InspectionComment),
    projectTypeRepository: new BaseRepository(models.ProjectType),
    fuelTypeRepository: new BaseRepository(models.FuelType),
    inspectionObservationRepository: new BaseRepository(
      models.InspectionObservation,
    ),

    drawingListRepository: new BaseRepository(models.DrawingsList),
    projectHullRepository: new BaseRepository(models.ProjectHull),
    hullClassRepository: new BaseRepository(models.HullClass),
    transactionRepository: new BaseRepository(models.Transaction),
    projectDrawingRepository: new BaseRepository(models.ProjectDrawing),
  };
}
