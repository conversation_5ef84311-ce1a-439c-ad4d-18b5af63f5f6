import {
  Attributes,
  BulkCreateOptions,
  col,
  CreateOptions,
  CreationAttributes,
  FindOptions,
  fn,
  Includeable,
  InferAttributes,
  Model,
  ModelStatic,
  Op,
  Order,
  Transaction,
  WhereOptions,
} from 'sequelize';
import {IFindQuery} from '../types';

interface GetOptions extends Omit<FindOptions, 'include'> {
  include?: Includeable[]; // Allow JSON-style include array
}
/**
 * A generic base repository class for performing CRUD operations on a Sequelize model.
 * @template T - The type of the Sequelize model that extends the `Model` class.
 */
export default class BaseRepository<T extends Model> {
  /**
   * Constructs a new instance of the BaseRepository.
   * @param model - The Sequelize model to be used for database operations.
   */
  constructor(protected model: ModelStatic<T>) {}

  /**
   * Retrieves all records from the database for the associated model.
   *
   * @param {string[]} [excludeFields=[]] - Optional array of field names to exclude from the result.
   * @param {boolean} [includeDeleted=false] - Whether to include soft-deleted records in the result.
   * @param {WhereOptions<InferAttributes<T>>} [extraWhere={}] - Optional additional Sequelize `where` conditions to filter the results.
   *
   * @returns {Promise<T[]>} A promise that resolves to an array of records that match the conditions.
   */

  async findAll(
    excludeFields: string[] = [],
    includeDeleted = false,
    extraWhere: WhereOptions<Attributes<T>> = {},
    include?: Includeable[],
    transaction?: Transaction | null,
    order?: Order,
  ) {
    const where: WhereOptions<Attributes<T>> = {
      ...extraWhere,
    };
    if (!includeDeleted) {
      Object.assign(where, {deletedAt: null});
    }
    const options: any = {
      where,
      attributes: {exclude: excludeFields},
      include,
      transaction,
    };
    if (order) {
      options.order = order;
    }
    return this.model.findAll(options);
  }

  /**
   * Retrieves a paginated list of records along with the total count of records
   * that match the specified query criteria.
   *
   * @param data - An object containing query parameters:
   *   - `columns`: An array of column names to include in the result set.
   *   - `limit`: The maximum number of records to retrieve.
   *   - `offset`: The number of records to skip before starting to collect the result set.
   *   - `orderBy`: The sorting criteria for the result set.
   * @returns A promise that resolves to an object containing:
   *   - `rows`: An array of records matching the query criteria.
   *   - `count`: The total number of records matching the query criteria.
   */
  async findAndCountAll(data: IFindQuery, includeDeleted = false) {
    const where: Record<string, any> = {
      ...(data.where || {}),
      ...(includeDeleted ? {} : {deletedAt: null}),
    };
    let query = {
      where: where,
      limit: data.limit,
      offset: data.offset,
      order: data.orderBy,
      include: data.include,
      distinct: data.distinct,
    };

    if (data.attributes?.length) {
      query = Object.assign(query, {attributes: data.attributes});
    }
    return this.model.findAndCountAll(query);
  }

  /**
   * Finds a record by its primary key (ID).
   *
   * @param id - The primary key of the record to find.
   * @param includeRelations - Whether to include related models in the result. Defaults to `false`.
   * @param options - Additional options for the query, such as `include` for specifying related models to include.
   * @returns A promise that resolves to the found record, or `null` if no record is found.
   */
  async findById(
    id: number,
    includeRelations = false,
    options: GetOptions = {},
  ) {
    const mergedWhere: WhereOptions = {
      id,
      deletedAt: null,
    };
    return this.model.findOne({
      where: mergedWhere,
      ...(includeRelations && {
        include: options.include,
      }),
    });
  }

  /**
   * Creates a new record in the database.
   * @param data - The data to create the new record with.
   * @returns A promise that resolves to the newly created record.
   */
  async create(
    data: CreationAttributes<T>,
    options?: CreateOptions<Attributes<T>>,
  ) {
    return this.model.create(data, options);
  }

  /**
   * Updates an existing record in the database.
   * @param id - The primary key of the record to update.
   * @param updates - An object containing the fields to update and their new values.
   * @returns A promise that resolves to the number of affected rows.
   */
  async update(
    id: number,
    updates: Partial<CreationAttributes<T>>,
    transaction?: Transaction | null,
  ) {
    return this.model.update(updates, {
      where: {id} as unknown as WhereOptions<InferAttributes<T>>,
      transaction,
    });
  }

  /**
   * Deletes a record from the database by its primary key.
   * @param id - The primary key of the record to delete.
   * @returns A promise that resolves to the number of affected rows.
   */
  async delete(id: number) {
    return this.model.destroy({
      where: {id} as unknown as WhereOptions<InferAttributes<T>>,
    });
  }

  /**
   * Finds a single record from the database that matches the specified condition.
   *
   * @param {WhereOptions} where - The Sequelize `where` condition used to find the record.
   * @param {string[]} [excludeFields=[]] - An optional array of field names to exclude from the result.
   * @param {boolean} [includeDeleted=false] - Whether to include soft-deleted records in the result.
   * @param {Includeable[]} [include=[]] - Optional Sequelize `include` configuration for eager-loading associated models.
   *
   * @returns {Promise<Model | null>} A promise that resolves to the found record, including any specified associations,
   * or `null` if no match is found.
   */
  async findOne(
    where: WhereOptions,
    excludeFields: string[] = [],
    includeDeleted = false,
    include: Includeable[] = [],
    extraOptions: Partial<FindOptions> = {}, // NEW
  ) {
    const mergedWhere: WhereOptions = {
      ...where,
      ...(includeDeleted ? {} : {deletedAt: null}),
    };
    return this.model.findOne({
      where: mergedWhere,
      attributes: {
        exclude: excludeFields,
      },
      include,
      ...extraOptions,
    });
  }

  /**
   * The `dropTable` function is an asynchronous method that drops a table using the model associated
   * with it.
   * @returns The `dropTable` function is returning a promise that resolves when the table associated
   * with the model is dropped.
   */
  async truncateTableData() {
    return this.model.truncate();
  }

  /**
   * The function `createAll` asynchronously creates multiple records using the `bulkCreate` method of
   * the model.
   * @param {any} data - The `data` parameter in the `createAll` function is an array of objects that
   * you want to insert into the database using the `bulkCreate` method of the `model`.
   * @returns The `createAll` function is returning a Promise that resolves to the result of bulk
   * creating the data using the `bulkCreate` method of the model.
   */
  async createAll(
    data: CreationAttributes<T>[],
    options?: BulkCreateOptions<Attributes<T>> | undefined,
    transaction?: Transaction | null,
  ) {
    return this.model.bulkCreate(data, {...options, transaction: transaction});
  }

  /**
   * Updates multiple records in the database based on the specified conditions.
   *
   * @param updates - An object containing the fields to update and their new values.
   * @param where - The Sequelize `where` condition used to filter the records to update.
   * @param transaction - An optional Sequelize transaction to use for the update operation.
   * @param includeDeleted - Whether to include soft-deleted records in the update operation. Defaults to `false`.
   *
   * @returns A promise that resolves to the number of affected rows.
   */
  async updateAll(
    updates: Partial<CreationAttributes<T>>,
    where: Record<string, any>,
    transaction?: Transaction | null,
  ) {
    return this.model.update(updates, {
      where,
      transaction,
      returning: true,
    });
  }

  /**
   * Retrieves a count of records grouped by their status from the database.
   *
   * @async
   * @function
   * @returns {Promise<Record<string, number>>} A promise that resolves to an object where the keys are the statuses
   * and the values are the counts of records for each status.
   *
   *
   * @throws {Error} Throws an error if the database query fails.
   */
  async getCountByStatus(tenantId: string = '') {
    const where: WhereOptions = {
      deletedAt: null,
    };
    if (tenantId) {
      where['tenantId'] = tenantId;
    }
    const result = (await this.model.findAll({
      attributes: ['status', [fn('COUNT', col('status')), 'count']],
      where,
      group: ['status'],
      raw: true,
    })) as unknown as Array<{status: string | number; count: string}>;

    return result.reduce(
      (acc, item) => {
        acc[item.status] = Number(item.count);
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  /**
   * Fetches unique values for a specified key from the database.
   *
   * @param key - The key for which unique values need to be fetched.
   * @param validKeys - An array of valid keys to validate the input key.
   * @param extraWhere - Additional where conditions to filter the results.
   * @returns A promise that resolves to an array of unique values for the specified key.
   * @throws {Error} If the provided key is invalid or if fetching unique values fails.
   */
  async getOptionsByKey(
    key: string,
    validKeys: string[],
    extraWhere: WhereOptions<Attributes<T>> = {},
  ): Promise<T[]> {
    if (!validKeys.includes(key)) {
      throw new Error(`Invalid key: ${key}`);
    }

    const uniqueValues = await this.model.findAll({
      attributes: [[fn('DISTINCT', col(key)), key]],
      where: {
        ...extraWhere,
        [key]: {
          [Op.ne]: null, // Exclude null values
        },
      },
      raw: true,
    });
    return uniqueValues;
  }
}
