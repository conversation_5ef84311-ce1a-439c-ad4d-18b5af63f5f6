import path from 'path';
import {SwaggerOptions} from 'swagger-ui-express';

export const swaggerOptions: SwaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'New Building API',
      version: '1.0.0',
      description: 'API documentation for the New Building project',
      contact: {
        name: 'New Building Fleet Team',
      },
    },
    servers: [
      {
        url: process.env.SERVER_URL,
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    path.join(__dirname, '../controllers/**/*.{ts,js}'),
    path.join(__dirname, '../routes/**/*.{ts,js}'),
  ],
};
