import {Sequelize, Dialect} from 'sequelize';
import {Models, registerModels} from '../models';

const ssl =
  process.env.PG_SSL_ENABLED === 'true' || process.env.PG_SSL_ENABLED === 'TRUE'
    ? {require: true, rejectUnauthorized: false}
    : undefined;

const tenantDbCache: Record<string, {sequelize: Sequelize; models: Models}> =
  {};

export function getTenantDbConfig(tenantKey: string) {
  // You can fetch these from a config service, env, or a central DB
  // For example, if you use env vars like TENANT1_DB_HOST, etc.
  return {
    host: process.env.DB_HOST,
    database: tenantKey,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: Number(process.env.DB_PORT),
    dialect: 'postgres' as Dialect,
    logging: false,
    dialectOptions: {
      ssl: ssl,
    },
    pool: {
      min: Number(process.env.DB_MIN_CONNECTIONS) || 5,
      max: Number(process.env.DB_MAX_CONNECTIONS) || 20,
      acquire: 60000, // 60 seconds
      idle: 10000, // 10 seconds
      evict: 1000, // 1 second
    },
  };
}

export async function getTenantDb(tenantKey: string) {
  if (!tenantDbCache[tenantKey]) {
    console.log(`Creating new tenant DB connection for key: ${tenantKey}`);
    const config = getTenantDbConfig(tenantKey);
    const sequelize = new Sequelize(config);
    console.log('After sequelize creation');
    await sequelize.authenticate().catch(err => {
      console.error('Unable to connect to the database:', err);
      throw err; // Re-throw the error to ensure it is handled properly
    });
    const models = registerModels(sequelize);
    tenantDbCache[tenantKey] = {sequelize, models};
  }
  console.log(`Using tenant DB for key: ${tenantKey}`);
  return tenantDbCache[tenantKey];
}
