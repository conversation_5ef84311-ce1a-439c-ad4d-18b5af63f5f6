import { Models } from "../models";

export const excludeKeys = [
  'createdBy',
  'updatedBy',
  'deletedAt',
  'deletedBy',
  'createdAt',
  'updatedAt',
];

// Functions that return includes, given the models object
export function includesProjectData(models: Models) {
  return [
    {
      model: models.ProjectType,
      as: 'projectType',
      attributes: ['name'],
      required: false,
    },
    {
      model: models.FuelType,
      as: 'fuelType',
      attributes: ['name'],
      required: false,
    },
  ];
}

export function includesProjectDataExcludedKeys(models: Models) {
  return [
    {
      model: models.FuelType,
      as: 'fuelType',
      attributes: {
        exclude: excludeKeys,
      },
    },
    {
      model: models.ProjectType,
      as: 'projectType',
      attributes: {
        exclude: excludeKeys,
      },
    },
  ];
}

export function includesHullDataWithExcludedKeys(models: Models) {
  return [
    {
      model: models.HullClass,
      as: 'hullClass',
      attributes: {
        exclude: excludeKeys,
      },
      required: false,
      where: {
        deletedAt: null,
      },
    },
  ];
}
