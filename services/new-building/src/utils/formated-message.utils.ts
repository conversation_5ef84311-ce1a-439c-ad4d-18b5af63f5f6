import {User} from '../types';

/**
 * Generates a formatted logger message by associating user-specific tenant information
 * with the provided data.
 *
 * @param user - The user object containing tenant information.
 * @param data - A record of key-value pairs to be included in the logger message.
 * @returns A record where the key is the user's tenant key and the value is an object
 *          containing the tenant ID and the provided data.
 */
export function getFormatedLoggerMessage(
  user: User,
  data: Record<string, any>,
): Record<string, any> {
  return {
    [user.tenantKey as string]: {
      tenantId: user.tenantId,
      ...data,
    },
  };
}

/**
 * Generates a formatted error message object.
 *
 * @param message - The main error message to be included in the response.
 * @param details - Optional additional details about the error, provided as a record of key-value pairs.
 * @returns A record containing the error status, the main error message, and optionally the additional details.
 */
export function getFormatedErrorMessage(
  message: string,
  details?: Record<string, any>,
): Record<string, any> {
  const errorResponse: Record<string, any> = {
    error: true,
    message,
  };

  if (details) {
    errorResponse.details = details;
  }

  return errorResponse;
}

/**
 * Formats an API response object with the provided data, message, and optional pagination.
 *
 * @param data - The main data to include in the response.
 * @param message - A message describing the response (default is 'Success').
 * @param pagination - Optional pagination information to include in the response.
 * @returns A formatted API response object containing the data, message, error status, and optional pagination.
 */
export function getFormatedApiResponse(
  data: Record<string, any>,
  message: string = 'Success',
  pagination?: Record<string, any>,
): Record<string, any> {
  const response: Record<string, any> = {
    data,
    message,
    error: false,
  };

  if (pagination != null) {
    response.pagination = pagination;
  }

  return response;
}
