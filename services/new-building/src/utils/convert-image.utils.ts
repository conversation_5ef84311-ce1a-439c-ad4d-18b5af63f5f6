import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import {Readable} from 'stream';
import sharp from 'sharp';
import convert from 'heic-convert';

/**
 * Converts a HEIC image stored in an S3 bucket to JPEG format, resizes it to 800px width,
 * uploads the converted image back to the same bucket (replacing the `.heic` extension with `.jpeg`),
 * and deletes the original HEIC file from the bucket.
 *
 * @param region - The AWS region where the S3 bucket is located.
 * @param bucket - The name of the S3 bucket containing the image.
 * @param key - The key (path/filename) of the HEIC image in the S3 bucket.
 * @returns A promise that resolves to the key of the newly uploaded JPEG image.
 * @throws If the provided key does not end with `.heic`, or if there are issues with S3 operations.
 */
export async function convertHeicToJpegAndReplace(
  region: string,
  bucket: string,
  key: string,
): Promise<string> {
  if (!key.toLowerCase().endsWith('.heic')) {
    throw new Error('Provided key is not a .heic file');
  }

  const s3 = new S3Client({region});

  // Download the file from S3
  const {Body} = await s3.send(
    new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    }),
  );

  if (!Body || !(Body instanceof Readable)) {
    throw new Error('Invalid S3 object body');
  }

  const buffer = await streamToBuffer(Body);

  const jpegBuffer = await convert({
    buffer: buffer,
    format: 'JPEG',
    quality: 1,
  });

  const processedImage = await sharp(jpegBuffer).resize(800).toBuffer();

  const jpegKey = key.replace(/\.heic$/i, '.jpeg');

  await s3.send(
    new PutObjectCommand({
      Bucket: bucket,
      Key: jpegKey,
      Body: processedImage,
      ContentType: 'image/jpeg',
    }),
  );

  await s3.send(new DeleteObjectCommand({Bucket: bucket, Key: key}));

  return jpegKey;
}

/**
 * Converts a readable stream into a single Buffer containing all of its data.
 *
 * @param stream - The readable stream to be converted into a Buffer.
 * @returns A promise that resolves with a Buffer containing the stream's data.
 * @throws Will reject if the stream emits an error.
 */
async function streamToBuffer(stream: Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on('data', chunk => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
}
