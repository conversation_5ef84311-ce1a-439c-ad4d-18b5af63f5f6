import {Response} from 'express';

/**
 * Represents a custom error with an associated error code.
 * Extends the built-in `Error` class to include a numeric error code.
 */
export class CustomError extends Error {
  /**
   * The numeric code representing the error.
   */
  code: number;

  /**
   * Creates an instance of `CustomError`.
   *
   * @param message - The error message describing the issue.
   * @param code - The numeric code representing the error.
   */
  constructor(message: string, code: number) {
    super(message);
    this.code = code;
  }
}

/**
 * Handles errors and sends appropriate HTTP responses based on the type of error.
 *
 * @param err - The error object, which can be an instance of `CustomError` or `Error`.
 * @param res - The Express `Response` object used to send the HTTP response.
 * @param message - A descriptive message about the context in which the error occurred.
 * @param customMessage - (Optional) A custom error message to override the default error messages.
 *
 * @returns The HTTP response with the appropriate status code and error message.
 *
 * - If `err` is an instance of `CustomError`, the response will have the status code from `err.code`
 *   and the message from `err.message` or `customMessage`.
 * - If `err` is an instance of `Error`, the response will have a status code of 400 and a message
 *   derived from `customMessage` or a default message based on `message`.
 * - For all other cases, the response will have a status code of 500 and a generic "Internal Server Error" message.
 */
export const handleErrors = (
  err: CustomError | Error,
  res: Response,
  message: string,
  customMessage?: string,
) => {
  if (err instanceof CustomError) {
    return res.status(err.code).json({
      status: 'error',
      message: err.message ?? customMessage,
    });
  }

  if (err instanceof Error) {
    return res.status(400).json({
      status: 'error',
      message: customMessage ?? `There is some error when ${message}`,
    });
  }

  res.status(500).json({
    status: 'error',
    message: customMessage ?? 'Internal Server Error',
  });
};
