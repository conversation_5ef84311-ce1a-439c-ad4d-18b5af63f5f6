import sharp from 'sharp';
import {logger} from './logger.utils';

interface ThumbnailSize {
  width: number;
  height: number;
}

interface ThumbnailResult {
  size: string;
  data: Buffer;
  format: string;
  width: number;
  height: number;
}

/**
 * Parses a size string in the format '126px-126px' to extract width and height
 *
 * @param sizeStr - Size string in the format 'widthpx-heightpx'
 * @returns An object containing the width and height as numbers
 * @throws Error if the size string is improperly formatted
 */
export function parseSizeString(sizeStr: string): ThumbnailSize {
  const sizeRegex = /^(\d+)px-(\d+)px$/;
  const match = sizeRegex.exec(sizeStr);
  if (!match) {
    throw new Error(
      `Invalid size format: ${sizeStr}. Expected format: 'widthpx-heightpx'`,
    );
  }

  return {
    width: parseInt(match[1], 10),
    height: parseInt(match[2], 10),
  };
}

/**
 * Generates thumbnails of an image at the specified sizes
 *
 * @param imageBuffer - The buffer containing the original image data
 * @param sizes - Array of size strings in the format 'widthpx-heightpx' (e.g. '126px-126px')
 * @param options - Additional options for thumbnail generation
 * @returns Promise resolving to an array of objects containing the thumbnail data and metadata
 * @throws Error if thumbnail generation fails
 */
export async function generateThumbnails(
  imageBuffer: Buffer,
  sizes: string[],
  fileDetails: Record<string, string | number>,
  options: {
    format?: 'jpeg' | 'png' | 'webp';
    quality?: number;
  } = {},
): Promise<ThumbnailResult[]> {
  try {
    // Set default options
    const format = options.format ?? 'jpeg';
    const quality = options.quality ?? 80;

    // Get image metadata
    const metadata = await sharp(imageBuffer).metadata();

    // Process each size and generate thumbnail
    const thumbnailPromises = sizes.map(async sizeStr => {
      try {
        const {width, height} = parseSizeString(sizeStr);

        // Configure sharp with resizing and format options
        const pipeline = sharp(imageBuffer).resize(width, height, {
          fit: 'cover',
          position: 'center',
        });

        // Set the output format and quality
        let outputBuffer: Buffer;
        switch (format) {
          case 'jpeg':
            outputBuffer = await pipeline.jpeg({quality}).toBuffer();
            break;
          case 'png':
            outputBuffer = await pipeline.png().toBuffer();
            break;
          case 'webp':
            outputBuffer = await pipeline.webp({quality}).toBuffer();
            break;
          default:
            outputBuffer = await pipeline.jpeg({quality}).toBuffer();
            break;
        }

        return {
          size: sizeStr,
          data: outputBuffer,
          format,
          width,
          height,
          metadata,
          fileDetails,
        };
      } catch (error) {
        logger.error(`Error generating thumbnail for size ${sizeStr}`, {
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    });

    return Promise.all(thumbnailPromises);
  } catch (error) {
    logger.error('Error generating thumbnails', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw new Error(
      `Failed to generate thumbnails: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Generates a single thumbnail of the specified size from an image buffer
 *
 * @param imageBuffer - The buffer containing the image data
 * @param size - Size string in the format 'widthpx-heightpx' (e.g. '126px-126px')
 * @param options - Additional options for thumbnail generation
 * @returns Promise resolving to the thumbnail buffer
 * @throws Error if thumbnail generation fails
 */
export async function generateThumbnail(
  imageBuffer: Buffer,
  size: string,
  options: {
    format?: 'jpeg' | 'png' | 'webp';
    quality?: number;
  } = {},
): Promise<Buffer> {
  try {
    const thumbnails = await generateThumbnails(imageBuffer, [size], options);
    return thumbnails[0].data;
  } catch (error) {
    logger.error('Error generating single thumbnail', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw new Error(
      `Failed to generate thumbnail: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
