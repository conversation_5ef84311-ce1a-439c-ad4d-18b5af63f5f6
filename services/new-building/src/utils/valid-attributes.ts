import {Model, ModelStatic} from 'sequelize';

/**
 * Retrieves the valid attribute names of a given Sequelize model.
 *
 * @param model - The Sequelize model instance from which to extract attribute names.
 * @returns An array of strings representing the names of valid attributes in the model.
 */
export function getValidModelAttributes(model: ModelStatic<Model>): string[] {
  const attributes = model.getAttributes();
  return Object.keys(attributes);
}
