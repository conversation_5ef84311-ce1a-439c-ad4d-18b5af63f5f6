import {User} from '../types';

/**
 * Determines if a user has the required permissions based on their roles.
 *
 * @param user - The user object containing role information.
 * @param requiredPermissions - An array of permissions to check against the user's roles.
 * @returns `true` if the user has at least one of the required permissions, otherwise `false`.
 */
export function isUserHasPermission(
  user: User,
  requiredPermissions: string[],
): boolean {
  const userRoles = user?.realm_access?.roles;
  return requiredPermissions.some(permission =>
    userRoles?.includes(permission),
  );
}
