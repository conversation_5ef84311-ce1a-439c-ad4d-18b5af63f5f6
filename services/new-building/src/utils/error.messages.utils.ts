/**
 * Utility class for generating standardized error messages.
 * Provides static methods to create error messages for various scenarios.
 */
class ErrorMessages {
  /**
   * Generates an error message indicating a failure in performing the specified action.
   *
   * @param action - The action that failed to execute.
   * @returns A string containing the error message in the format: "Failed to {action}".
   */
  static failed(action: string) {
    return `Failed to ${action}`;
  }

  /**
   * Generates an error message indicating a failure in retrieving a specific action.
   *
   * @param action - The name or description of the action that failed to be retrieved.
   * @returns A string containing the error message for the specified action.
   */
  static errorFetching(action: string) {
    return `Error retrieving ${action}`;
  }

  /**
   * Generates a "not found" error message for a given entity.
   *
   * @param entity - The name of the entity that was not found.
   * @returns A string indicating that the specified entity was not found for the given ID.
   */
  static notFound(entity: string) {
    return `${entity} not found for the given id`;
  }
}

export default ErrorMessages;
