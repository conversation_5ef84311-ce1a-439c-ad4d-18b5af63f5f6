import * as Joi from 'joi';
import {EnvironmentValidator} from '@paris2-api-new-building/shared';

console.log('Loading environment variables...');

export interface EnvironmentVariables {
  AWS_S3_LOGS_EXPIRY_DAYS: number;
  // Server
  PORT: number;
  NODE_ENV: 'local' | 'dev2' | 'qa2' | 'uat2' | 'live';
  SERVER_URL: string;
  // LOG_LEVEL: string;

  // // Database
  DB_HOST: string;
  DB_PORT: number;
  DB_NAME: string;
  DB_USER: string;
  DB_PASSWORD: string;
  DB_MIN_CONNECTIONS: number;
  DB_MAX_CONNECTIONS: number;

  // // AWS S3
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  AWS_REGION: string;
  AWS_S3_BUCKET_NAME: string;
  AWS_S3_SIGNED_URL_EXPIRY: number; // in seconds

  // PARIS-2
  PARIS_BASE_URL: string;
  PARIS_AUTH_BASE_URL: string;
  OPEN_ID_USERNAME: string;
  OPEN_ID_PASSWORD: string;
  OPEN_ID_CLIENT_SECRET: string;
  OPEN_ID_GRANT_TYPE: string;
  OPEN_ID_CLIENT_ID: string;
}

const envSchema = Joi.object<EnvironmentVariables>({
  // Server
  PORT: Joi.number().port().required(),
  NODE_ENV: Joi.string()
    .valid('local', 'dev2', 'qa2', 'uat2', 'live')
    .required(),
  SERVER_URL: Joi.string().uri().required(),

  // LOG_LEVEL: Joi.string()
  //   .valid("error", "warn", "info", "http", "verbose", "debug", "silly")
  //   .required()
  //   .default("info"),

  // Database
  DB_HOST: Joi.string().required(),
  DB_PORT: Joi.number().port().required().default(5436),
  DB_NAME: Joi.string().required(),
  DB_USER: Joi.string().required(),
  DB_PASSWORD: Joi.string().required(),
  DB_MIN_CONNECTIONS: Joi.number().port().required(),
  DB_MAX_CONNECTIONS: Joi.number().port().required(),

  // AWS S3
  AWS_ACCESS_KEY_ID: Joi.string().required(),
  AWS_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_REGION: Joi.string().required(),
  AWS_S3_BUCKET_NAME: Joi.string().required(),
  AWS_S3_SIGNED_URL_EXPIRY: Joi.number().required(),
});

const env = new EnvironmentValidator(envSchema, {
  serviceDir: __dirname + '/../..',
});
export const envVars = env.getEffectiveConfig<EnvironmentVariables>();
