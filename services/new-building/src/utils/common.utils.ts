import s3HelperService from '../services/s3-helper.service';
import ExcelJS from 'exceljs';
import {parse} from 'csv-parse/sync';
import {CustomError} from './custom-error';
import {Op} from 'sequelize';
import {IFindAllQuery} from '../types';
import {inspectionFieldTypeMap} from '../types/inspection.types';
/**
 * Constructs the destination path for an asset based on tenant ID, project ID, and the asset's original path.
 *
 * @param tenantId - The unique identifier for the tenant.
 * @param projectId - The unique identifier for the project.
 * @param assetPath - The original path of the asset, including its filename.
 * @returns The destination path for the asset in the format:
 *          `<tenantId>/projects/<projectId>/images/<fileName>`.
 */
export function getAssetDestinationPath(
  tenantId: number,
  projectId: number,
  assetPath: string,
) {
  const pathParts = assetPath.split('/');
  const fileName = pathParts.pop();
  const destinationPath = `${tenantId}/projects/${projectId}/images/${fileName}`;
  return destinationPath;
}

type ExcelRecordsType = Record<string, string | number | boolean | null>;
//---key 1 as drawing and key 2 as Inspection-------/
/**
 * Processes an Excel or CSV file by reading its contents, determining its extension,
 * and parsing the file data.
 *
 * @param fileKey - The key or path to the file to be processed.
 * @param key - A numeric identifier used during the parsing process.
 * @returns A promise that resolves to an array of parsed data from the file.
 */
export async function processExcelAndCSV(
  fileKey: string,
  key: number,
): Promise<unknown[]> {
  const buffer = await readFileToBuffer(fileKey);
  const extension = getFileExtension(fileKey);

  return await parseFileData(buffer, extension, key);
}

async function readFileToBuffer(fileKey: string): Promise<Buffer> {
  const chunks: Uint8Array[] = [];
  const stream = await s3HelperService.getTheFileStream(fileKey);

  return new Promise((resolve, reject) => {
    stream.on('data', chunk => chunks.push(chunk));
    stream.on('end', () => resolve(Buffer.concat(chunks)));
    stream.on('error', reject);
  });
}

/**
 * Extracts the file extension from a given file key.
 *
 * @param fileKey - The string representing the file name or path.
 * @returns The file extension in lowercase, or an empty string if no extension is found.
 */
function getFileExtension(fileKey: string): string {
  return fileKey.split('.').pop()?.toLowerCase() ?? '';
}

/**
 * Parses file data from a buffer based on the provided file extension.
 *
 * @param buffer - The file data as a Buffer.
 * @param extension - The file extension indicating the type of file (e.g., 'xlsx', 'xls', 'csv').
 * @param key - A numeric key used for processing specific file types.
 * @returns A promise that resolves to an array of parsed data.
 * @throws CustomError - Throws an error if the file type is unsupported.
 */
async function parseFileData(
  buffer: Buffer,
  extension: string,
  key: number,
): Promise<unknown[]> {
  switch (extension) {
    case 'xlsx':
    case 'xls':
      return await parseExcelFile(buffer, key);
    case 'csv':
      return parseCSVFile(buffer);
    default:
      throw new CustomError('Unsupported file type.', 415);
  }
}

/**
 * Parses an Excel file from a given buffer and validates its headers based on the provided key.
 *
 * @param buffer - The buffer containing the Excel file data.
 * @param key - A numeric key indicating the type of validation to perform:
 *              - `1`: Validates against drawing-related headers.
 *              - Any other value: Validates against inspection-related headers.
 *
 * @returns A promise that resolves to an array of extracted data from the Excel file.
 *
 * @throws {Error} If no worksheet is found in the Excel file.
 * @throws {CustomError} If required headers are missing in the Excel file.
 */
async function parseExcelFile(buffer: Buffer, key: number): Promise<unknown[]> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);
  const worksheet = workbook.worksheets[0];

  if (!worksheet) throw new Error('No worksheet found');

  const headers = extractExcelHeaders(worksheet);
  if (key == 1) {
    const drawingRequiredHeaders = [
      'Drawing Number*',
      'Drawing Name*',
      'Discipline',
    ];

    const missingHeaders = drawingRequiredHeaders.filter(
      h => !headers.includes(h),
    );
    if (missingHeaders.length > 0) {
      const headerNames = missingHeaders.join(', ');
      throw new CustomError(
        `Unsupported File: Missing header(s): ${headerNames}`,
        422,
      );
    }
  } else {
    const inspectionRequiredHeaders = [
      'Hull No',
      'Submission Date',
      'Due Date',
      'Time',
      'Inspection Description',
      'Discipline',
      'Owner',
      'Class',
      'Remark',
    ];
    const missingHeaders = inspectionRequiredHeaders.filter(
      h => !headers.includes(h),
    );
    if (missingHeaders.length > 0) {
      const headerNames = missingHeaders.join(', ');
      throw new CustomError(
        `Unsupported File: Missing header(s): ${headerNames}`,
        422,
      );
    }
  }
  return extractExcelData(worksheet, headers);
}

/**
 * Extracts the headers from the first row of an Excel worksheet.
 *
 * @param worksheet - The ExcelJS worksheet from which to extract headers.
 * @returns An array of strings representing the headers in the first row of the worksheet.
 */
function extractExcelHeaders(worksheet: ExcelJS.Worksheet): string[] {
  const headers: string[] = [];
  const headerRow = worksheet.getRow(1);

  headerRow.eachCell(cell => {
    headers.push(cell.text?.trim() || '');
  });

  return headers;
}

/**
 * Extracts data from an Excel worksheet and maps it to an array of records based on the provided headers.
 *
 * @param {ExcelJS.Worksheet} worksheet - The Excel worksheet to extract data from.
 * @param {string[]} headers - An array of header names that correspond to the columns in the worksheet.
 * @returns {ExcelRecordsType[]} An array of records where each record represents a row in the worksheet,
 * mapped according to the provided headers.
 *
 * @remarks
 * - The first row of the worksheet is assumed to be the header row and is skipped during data extraction.
 * - Each cell value is converted using the `convertCellValue` function, which may depend on additional mappings
 *   such as `inspectionFieldTypeMap`.
 * - If a header is not defined for a column, that column's data will not be included in the resulting record.
 */
function extractExcelData(
  worksheet: ExcelJS.Worksheet,
  headers: string[],
): ExcelRecordsType[] {
  const dataRecords: ExcelRecordsType[] = [];

  worksheet.eachRow((row, rowIndex) => {
    if (rowIndex === 1) return; // Skip header row

    const rowData: Record<string, string | number | boolean | null> = {};
    row.eachCell((cell, colIndex) => {
      const header = headers[colIndex - 1];
      if (header) {
        rowData[header] = convertCellValue(
          cell.value,
          header,
          inspectionFieldTypeMap,
        );
      }
    });

    dataRecords.push(rowData);
  });

  return dataRecords;
}

/**
 * Converts a cell value based on its type and header mapping.
 *
 * @param value - The value to be converted. Can be of type `any`.
 * @param header - The header associated with the value, used to determine its type.
 * @param fieldTypeMap - A mapping of headers to their respective field types (`string`, `date`, or `time`).
 * @returns The converted value, which can be of type `string`, `number`, `boolean`, or `null`.
 *
 * @throws CustomError - Throws an error if the value cannot be converted to a valid `date` or `time` format.
 *
 * - If `value` is `null` or `undefined`, returns `null`.
 * - If `value` is a primitive type (`string`, `number`, or `boolean`) and no `fieldType` is defined, returns the value as is.
 * - If `value` is an object with a `text` property, returns the `text` property.
 * - If `value` is a `Date` instance, returns its ISO string representation.
 * - If `fieldType` is `date`, attempts to parse the value as a date and returns its ISO string representation.
 * - If `fieldType` is `time`, parses the value as a time string (e.g., "HH:mm") and returns its ISO string representation.
 * - Otherwise, converts the value to a string and returns it.
 */
function convertCellValue(
  value: any,
  header: string,
  fieldTypeMap: Record<string, 'string' | 'date' | 'time'>,
): string | number | boolean | null {
  const fieldType = fieldTypeMap[header];

  if (value === null || value === undefined) return null;

  if (
    (typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean') &&
    !fieldType
  ) {
    return value;
  }

  if (typeof value === 'object' && value.text) return value.text;

  if (value instanceof Date) return value.toISOString();

  if (fieldType === 'date') {
    const dateValue = new Date(value);
    if (isNaN(dateValue.getTime())) {
      throw new CustomError(`Invalid date format for ${header}`, 422);
    }
    return dateValue.toISOString();
  }

  if (fieldType === 'time') {
    const [hours, minutes] = value.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) {
      throw new CustomError(`Invalid time format for ${header}`, 422);
    }
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date.toISOString();
  }

  return value.toString();
}

/**
 * Parses a CSV file from a given buffer and returns an array of objects.
 * Each object represents a row in the CSV file, with keys corresponding to column headers.
 *
 * @param buffer - The buffer containing the CSV file data.
 * @returns An array of objects representing the parsed CSV data.
 */
function parseCSVFile(buffer: Buffer): unknown[] {
  return parse(buffer.toString(), {
    columns: true,
    skip_empty_lines: true,
  });
}

//----due date filter is allowed from inspection module only
/**
 * Builds a set of base filters for a search query based on the provided query object and allowed filters.
 *
 * @param query - The search query object containing filter criteria.
 * @param allowedFilters - An array of keys from the query object that are allowed as filters.
 * @param allowDueDateFilter - A boolean indicating whether the `dueDate` filter should be applied. Defaults to `false`.
 * @returns A record containing the constructed filters for the search query.
 *
 * The function processes the following:
 * - Filters specified in `allowedFilters` are added to the result if their values are not objects.
 * - Date filters (`createdAt`, `submissionDate`, and optionally `dueDate`) are applied if their `startDate` or `endDate` properties are present.
 */
export function buildSearchBaseFilters(
  query: IFindAllQuery,
  allowedFilters: (keyof IFindAllQuery)[],
  allowDueDateFilter: boolean = false,
): Record<string, unknown> {
  const where: Record<string, unknown> = {};
  allowedFilters.forEach(filterKey => {
    const value = query[filterKey];
    if (value && typeof value !== 'object') {
      where[filterKey] = value;
    }
  });

  if (
    query.createdAt &&
    (query.createdAt.startDate || query.createdAt.endDate)
  ) {
    applyDateFilter(where, 'createdAt', query.createdAt);
  }

  if (
    query.submissionDate &&
    (query.submissionDate.startDate || query.submissionDate.endDate)
  ) {
    applyDateFilter(where, 'submissionDate', query.submissionDate);
  }

  if (
    allowDueDateFilter &&
    query.dueDate &&
    (query.dueDate.startDate || query.dueDate.endDate)
  ) {
    applyDateFilter(where, 'dueDate', query.dueDate);
  }

  return where;
}

/**
 * Applies a date filter to a given `whereClause` object based on the provided `dateRange`.
 * The filter is applied to the specified `fieldName` using Sequelize operators for greater than or equal to (`Op.gte`)
 * and less than or equal to (`Op.lte`).
 *
 * @param whereClause - The object representing the query conditions to which the date filter will be applied.
 * @param fieldName - The name of the field in the `whereClause` to which the date filter will be applied.
 * @param dateRange - An optional object containing `startDate` and `endDate` strings.
 *                    If `startDate` is provided, the filter will include dates greater than or equal to midnight of the `startDate`.
 *                    If `endDate` is provided, the filter will include dates less than or equal to the end of the day of the `endDate`.
 *
 * @returns void - This function modifies the `whereClause` object in place.
 */
function applyDateFilter(
  whereClause: Record<string, unknown>,
  fieldName: string,
  dateRange?: {startDate?: string; endDate?: string},
): void {
  if (!dateRange?.startDate && !dateRange?.endDate) {
    return;
  }

  const dateFilter: Record<symbol, Date> = {};

  if (dateRange.startDate) {
    const startDate = new Date(dateRange.startDate);
    startDate.setHours(0, 0, 0, 0); // set to midnight
    dateFilter[Op.gte] = startDate;
  }

  if (dateRange.endDate) {
    const endDate = new Date(dateRange.endDate);
    endDate.setHours(23, 59, 59, 999); // end of day
    dateFilter[Op.lte] = endDate;
  }
  whereClause[fieldName] = dateFilter;
}
