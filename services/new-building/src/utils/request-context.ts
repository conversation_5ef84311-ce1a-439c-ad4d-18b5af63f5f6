import { AsyncLocalStorage } from 'async_hooks';
import { Models } from '../models';
import { createRepositories } from '../repositories';
import { Sequelize } from 'sequelize';

export type ContextType = {
  models: Models;
  repositories: ReturnType<typeof createRepositories>;
  sequelize: Sequelize;
  user: any;
};

export const requestContext = new AsyncLocalStorage<ContextType>();

export function getRequestContext(): ContextType {
  const store = requestContext.getStore();
  if (!store) throw new Error('No request context available');
  return store;
}
