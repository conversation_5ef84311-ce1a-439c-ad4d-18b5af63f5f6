import {loggerFactory} from '@paris2-api-new-building/shared';
import winston from 'winston';
import {User} from '../types';
import {getFormatedLoggerMessage} from '.';

/**
 * Logger instance configured with a specified log level.
 * The log level is determined by the `LOG_LEVEL` environment variable, which can be one of:
 * - 'error'
 * - 'warn'
 * - 'info'
 * - 'http'
 * - 'debug'
 * If the `LOG_LEVEL` environment variable is not set, the default log level is 'error'.
 *
 * @constant
 * @type {winston.Logger}
 */
export const logger: winston.Logger = loggerFactory(
  (process.env.LOG_LEVEL as
    | 'error'
    | 'warn'
    | 'info'
    | 'http'
    | 'debug'
    | undefined) ?? 'error',
);

/**
 * Logs an informational message using the provided user and data.
 *
 * @param user - The user object containing information about the user performing the action.
 * @param data - A record containing additional data to be logged.
 */
export function logInfo(user: User, data: Record<string, any>) {
  logger.info(getFormatedLoggerMessage(user, data));
}

/**
 * Logs an error message using the provided user information and data.
 *
 * @param user - The user object containing information about the user.
 * @param data - A record containing additional data to be logged.
 */
export function logError(user: User, data: Record<string, any>) {
  logger.error(getFormatedLoggerMessage(user, data));
}

/**
 * Logs a debug message with formatted user and data information.
 *
 * @param user - The user object containing information about the user.
 * @param data - A record containing key-value pairs of data to be logged.
 */
export function logDebug(user: User, data: Record<string, any>) {
  logger.debug(getFormatedLoggerMessage(user, data));
}
