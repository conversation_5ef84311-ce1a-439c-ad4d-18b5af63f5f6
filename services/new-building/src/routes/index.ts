import {Router} from 'express';
import {
  Projects<PERSON><PERSON>roller,
  FuelTypeController,
  ProjectType<PERSON><PERSON>roller,
  AssetsController,
  CronsController,
  DrawingsController,
  HullController,
  ProjectDrawingsController,
  CommentsController,
  InspectionController,
  ObservationsController,
  VesselMasterDataController,
} from '../controllers';

const router = Router();

// Define all routes here
router.use('/projects', ProjectsController);
router.use('/fuel-type', FuelTypeController);
router.use('/project-type', ProjectTypeController);
router.use('/assets', AssetsController);
router.use('/crons', CronsController);
router.use('/drawing-lists', DrawingsController);
router.use('/hulls', HullController);
router.use('/project-drawings', ProjectDrawingsController);
router.use('/comments', CommentsController);
router.use('/inspections', InspectionController);
router.use('/observations', ObservationsController);
router.use('/vessel', VesselMasterDataController);

// Add more routes as needed
export default router;
