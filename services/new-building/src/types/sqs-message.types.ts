export interface SQSResponse {
  Records: SQSMessage[];
}

export interface SQSMessage {
  eventVersion: string;
  eventSource: string;
  awsRegion: string;
  eventTime: string;
  eventName: string;
  userIdentity: SQSMessageUserIdentity;
  requestParameters: {
    sourceIPAddress: string;
  };
  responseElements: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    'x-amz-request-id': string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    'x-amz-id-2': string;
  };
  s3: SQSResponseS3;
}

interface SQSResponseS3 {
  s3SchemaVersion: string;
  configurationId: string;
  bucket: SQSResponseS3Bucket;
  object: SQSResponseS3Object;
}

interface SQSResponseS3Object {
  key: string;
  size: number;
  eTag: string;
  sequencer: string;
}

interface SQSResponseS3Bucket {
  name: string;
  ownerIdentity: SQSMessageUserIdentity;
  arn: string;
}

interface SQSMessageUserIdentity {
  principalId: string;
}
