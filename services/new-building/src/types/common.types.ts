import {Request} from 'express';

export interface AnyObject {
  [property: string]: any;
}
export interface AuthRequest extends Request {
  user?: AnyObject;
}

export type Paris2MasterItemOptions = {
  id: number;
  value: string;
  updated_at: number;
};

export interface Paris2MasterItems {
  miscCurrencys: Paris2MasterItemOptions[];
  owners: Paris2MasterItemOptions[];
  vesselTypes: Paris2MasterItemOptions[];
  miscEngines: Paris2MasterItemOptions[];
  flags: Paris2MasterItemOptions[];
  vesselClasses: Paris2MasterItemOptions[];
}

export interface ProjectHullMapping {
  projectId: number;
  hullId: number;
  createdBy: string;
}

export interface HullClassMapping {
  hullId: number;
  vesselClass: string;
  deletedAt?: Date;
  createdBy: string;
}

export interface AssetType {
  assetName: string;
  assetType?: string;
  assetPath: string;
  referenceType: number;
  referenceId: number;
  createdBy: string;
}
