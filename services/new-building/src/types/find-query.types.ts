import {Includeable, Order} from 'sequelize'; // ⬅️ Import this

export interface IFindAllQuery {
  page?: number;
  limit?: number;
  fields?: string | string[];
  createdAt?: {
    startDate?: string;
    endDate?: string;
  };
  dueDate?: {
    startDate?: string;
    endDate?: string;
  };
  submissionDate?: {
    startDate?: string;
    endDate?: string;
  };
  sortBy?: string; // e.g., 'createdAt' or 'engineType'
  sortOrder?: 'ASC' | 'DESC'; // optional, default to DESC
  searchParam?: string; // optional, default to undefined

  [key: string]:
    | string
    | number
    | string[]
    | Date
    | {startDate?: string; endDate?: string}
    | undefined;
}

export interface IFindQuery {
  orderBy: Order;
  limit: number;
  offset: number;
  page: number;
  attributes: string[] | undefined;
  where: Record<string, any>;
  include?: Includeable | Includeable[];
  distinct?: boolean; // optional, default to false
}
