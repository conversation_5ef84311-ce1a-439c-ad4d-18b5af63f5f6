import {HullStatus} from '../enums';

export interface IHullAttributes {
  id: number;
  hullNo: string;
  tenantId: number;
  shipName?: string;
  imo?: number;
  flag: string;
  deadWeightGT?: number;
  deadWeightNT?: number;
  steelCuttingDate: Date;
  keelLaidDate: Date;
  launchDate: Date;
  capacity: number;
  seaTrialDate: Date;
  deliveryDate: Date;
  assetName?: string;
  assetType?: string;
  assetPath?: string;
  status: HullStatus;
  createdAt?: Date;
  createdBy?: string | null;
  updatedAt?: Date;
  updatedBy?: string | null;
  deletedAt?: Date | null;
  deletedBy?: string | null;
}
