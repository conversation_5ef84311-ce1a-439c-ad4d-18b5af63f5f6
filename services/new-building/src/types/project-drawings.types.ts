import {ProjectDrawingStatus} from '../enums';

export interface IProjectDrawingsAttributes {
  id: number;
  drawingListId: number;
  name: string;
  dueDate: Date;
  tenantId: number;
  assetType: string;
  assetPath: string;
  assetName: string;
  version: number;
  status: ProjectDrawingStatus;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: Date | null;
  deletedBy?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
}
