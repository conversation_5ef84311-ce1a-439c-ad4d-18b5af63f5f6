export interface User {
  exp: number;
  iat: number;
  auth_time: number;
  jti: string;
  iss: string;
  aud: string;
  sub: string;
  typ: string;
  azp: string;
  nonce: string;
  session_state?: string;
  'allowed-origins'?: string[];
  realm_access?: {
    roles?: string[];
  };
  resource_access?: {
    account: {
      roles: string[];
    };
    [key: string]: {
      roles: string[];
    };
  };
  scope?: string;
  sid?: string;
  user_name_hash?: string;
  email_verified?: boolean;
  user_id?: string;
  name?: string;
  preferred_username?: string;
  given_name?: string;
  financialOwnerReportingAccess?: string;
  tenantId?: string;
  tenantKey?: string;
  email?: string;
  group?: string[];
}
