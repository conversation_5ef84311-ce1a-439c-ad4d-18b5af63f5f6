import {TransactionStatus, TransactionType} from '../enums';

export interface ITransactionAttributes {
  uuid: string;
  type: TransactionType;
  status: TransactionStatus;
  parameters: Record<string, unknown>;
  retry: number;
  result?: Record<string, unknown> | null;
  createdAt?: Date;
  createdBy?: string | null;
  updatedAt?: Date;
  updatedBy?: string | null;
  deletedAt?: Date | null;
  deletedBy?: string | null;
}
