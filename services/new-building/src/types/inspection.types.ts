export interface IInspectionAttributes {
  id: number;
  type: number;
  hullId: number;
  tenantId: number;
  inspectionSourceFileId: number;
  projectId: number;
  submissionDate: Date;
  dueDate: Date;
  time: string;
  description: string;
  discipline: string;
  forOwner: boolean;
  forClass: boolean;
  remark: string;
  status: number;
  createdBy: string;
  updatedBy: string;
  deletedAt: Date | null;
  deletedBy: string | null;
  createdAt: Date;
  updatedAt: Date;
}
export interface InspectionInput {
  'Hull No': string;
  'Submission Date': number;
  'Due Date': number;
  Time: number;
  'Inspection Description': string;
  Discipline: string;
  Owner?: string;
  Class?: string;
  Remark?: string;
}
export interface HullAttributes {
  id: number;
  hullNo: string;
}

export interface ProjectHullAttributes {
  id: number;
  projectId: number;
  hullId: number;
  hull?: HullAttributes;
}

export const inspectionFieldTypeMap: Record<
  string,
  'string' | 'date' | 'time'
> = {
  'Submission Date': 'date',
  'Due Date': 'date',
  Time: 'time',
};
