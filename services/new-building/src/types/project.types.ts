import {ProjectStatus} from '../enums';

export interface IProjectAttributes {
  id: number;
  tenantId: number;
  name: string;
  owner: string;
  shipType: string;
  fuelTypeId: number;
  engineType: string;
  projectTypeId: number;
  projectDescription: string;
  totalTimeTaken: number;
  contractBudget?: number | null;
  expenses?: number | null;
  currency?: string | null;
  completionDate?: Date | null;
  assetName?: string;
  assetType?: string;
  assetPath?: string;
  isShipTypeCustom?: boolean;
  isEngineTypeCustom?: boolean;
  status: ProjectStatus;
  createdAt?: Date;
  createdBy?: string | null;
  updatedAt?: Date;
  updatedBy?: string | null;
  deletedAt?: Date | null;
  deletedBy?: string | null;
}
