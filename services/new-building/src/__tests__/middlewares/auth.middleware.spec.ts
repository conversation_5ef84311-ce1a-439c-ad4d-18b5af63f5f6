import {expect} from 'chai';
import sinon from 'sinon';
import {Request, Response, NextFunction} from 'express';
import jwt from 'jsonwebtoken';
import {verifyToken} from '../../middleware';
import {setupEnv} from '../acceptance/test-helper';
import {ProjectPermissions} from '../../enums';
import * as databaseConfig from '../../configs/database.config';

describe('verifyToken middleware', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: sinon.SinonSpy;

  beforeEach(() => {
    req = {headers: {}};
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    };
    next = sinon.spy();
    setupEnv();

    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  const generateToken = (payload: object, options: jwt.SignOptions = {}) => {
    return jwt.sign(payload, process.env.JWT_SECRET!, {
      issuer: process.env.JWT_ISSUER,
      algorithm: 'HS256',
      expiresIn: '1h',
      ...options,
    });
  };

  it('should return 403 if tenantId and tenantKey are missing', () => {
    const token = generateToken({name: 'noTenant'});
    req.headers = {
      authorization: `Bearer ${token}`,
    };
    const middleware = verifyToken();
    middleware(req as Request, res as Response, next as NextFunction);
    expect((res.status as sinon.SinonStub).calledOnceWith(403)).to.be.true;
    expect(
      (res.json as sinon.SinonStub).calledOnceWith({
        error: 'Tenant Id not provided',
      }),
    ).to.be.true;
    expect(next.called).to.be.false;
  });

  it('should test RS256', () => {
    process.env.JWT_PUBLIC_KEY = 'true';
    const token = generateToken({name: 'noTenant'});
    req.headers = {
      authorization: `Bearer ${token}`,
    };
    const middleware = verifyToken();
    middleware(req as Request, res as Response, next as NextFunction);
    expect((res.status as sinon.SinonStub).calledOnceWith(403)).to.be.true;
    expect(next.called).to.be.false;
  });

  it('should fail for permissions', async () => {
    const user = {
      tenantId: 'abc123',
      tenantKey: 'tenantKey123',
      realm_access: {
        roles: ['user'], // User has basic role but not the required permission
      },
    };
    const token = generateToken(user);
    req.headers = {
      authorization: `Bearer ${token}`,
    };

    const middleware = verifyToken([ProjectPermissions.CREATE]);

    // Wait for the async middleware to complete
    await new Promise<void>(resolve => {
      next = sinon.spy(() => {
        resolve();
      });

      middleware(req as Request, res as Response, next as NextFunction);

      // If next is not called within a reasonable time, resolve anyway
      setTimeout(() => {
        resolve();
      }, 100);
    });

    expect((res.status as sinon.SinonStub).calledOnceWith(403)).to.be.true;
    expect(
      (res.json as sinon.SinonStub).calledOnceWith({
        error:
          'Forbidden: You do not have the required permissions to perform this action.',
      }),
    ).to.be.true;

    expect(next.called).to.be.false;
  });

  it('should pass for valid token without permission requirements', async () => {
    const user = {
      tenantId: 'abc123',
      tenantKey: 'tenantKey123',
      realm_access: {
        roles: ['user'],
      },
    };
    const token = generateToken(user);
    req.headers = {
      authorization: `Bearer ${token}`,
    };

    const middleware = verifyToken(); // No permissions required

    // Wait for the async middleware to complete
    await new Promise<void>(resolve => {
      next = sinon.spy(() => {
        resolve();
      });

      middleware(req as Request, res as Response, next as NextFunction);

      // If next is not called within a reasonable time, resolve anyway
      setTimeout(() => {
        resolve();
      }, 100);
    });

    expect((res.status as sinon.SinonStub).called).to.be.false;
    expect((res.json as sinon.SinonStub).called).to.be.false;
    expect(next.called).to.be.true;
  });

  it('should pass for valid token with correct permissions', async () => {
    const user = {
      tenantId: 'abc123',
      tenantKey: 'tenantKey123',
      realm_access: {
        roles: ['user', ProjectPermissions.CREATE], // User has the required permission
      },
    };
    const token = generateToken(user);
    req.headers = {
      authorization: `Bearer ${token}`,
    };

    const middleware = verifyToken([ProjectPermissions.CREATE]);

    // Wait for the async middleware to complete
    await new Promise<void>(resolve => {
      next = sinon.spy(() => {
        resolve();
      });

      middleware(req as Request, res as Response, next as NextFunction);

      // If next is not called within a reasonable time, resolve anyway
      setTimeout(() => {
        resolve();
      }, 100);
    });

    expect((res.status as sinon.SinonStub).called).to.be.false;
    expect((res.json as sinon.SinonStub).called).to.be.false;
    expect(next.called).to.be.true;
  });

  it('should return 401 if no token provided', () => {
    req.headers = {}; // No authorization header

    const middleware = verifyToken();
    middleware(req as Request, res as Response, next as NextFunction);

    expect((res.status as sinon.SinonStub).calledOnceWith(401)).to.be.true;
    expect(
      (res.json as sinon.SinonStub).calledOnceWith({
        error: 'No token provided',
      }),
    ).to.be.true;
    expect(next.called).to.be.false;
  });

  it('should return 403 for invalid token', () => {
    req.headers = {
      authorization: 'Bearer invalid-token',
    };

    const middleware = verifyToken();
    middleware(req as Request, res as Response, next as NextFunction);

    expect((res.status as sinon.SinonStub).calledOnceWith(403)).to.be.true;
    expect(
      (res.json as sinon.SinonStub).calledOnceWith({
        error: 'Invalid token',
      }),
    ).to.be.true;
    expect(next.called).to.be.false;
  });
});
