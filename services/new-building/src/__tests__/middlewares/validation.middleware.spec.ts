import {expect} from 'chai';
import sinon from 'sinon';
import {Request, Response, NextFunction} from 'express';
import Jo<PERSON> from 'joi';
import validatorMiddleware from '../../middleware/validation.middleware';
import * as Validators from '../../models/dto';

describe('Validation Middleware', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: sinon.SinonStub;

  beforeEach(() => {
    req = {body: {}, query: {}, params: {}};
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    };
    next = sinon.stub();
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should validate and call next if validation passes', async () => {
    // Mock a validator in Validators
    const mockSchema = {
      body: Joi.object({
        name: Joi.string().required(),
      }),
    };
    sinon.stub(Validators, 'findProjectDto').value(mockSchema);

    req.body = {name: 'Test Name'};

    const middleware = validatorMiddleware(
      'findProjectDto' as keyof typeof Validators,
    );
    await middleware(req as Request, res as Response, next as NextFunction);

    expect(next.calledOnce).to.be.true;
    expect(next.firstCall.args).to.be.empty;
    expect(req.body).to.deep.equal({name: 'Test Name'}); // Validated and updated
  });

  it('should return 400 if validation fails', async () => {
    // Mock a validator in Validators
    const mockSchema = {
      body: Joi.object({
        name: Joi.string().required(),
      }),
    };
    sinon.stub(Validators, 'findProjectDto').value(mockSchema);

    req.body = {};

    const middleware = validatorMiddleware(
      'findProjectDto' as keyof typeof Validators,
    );
    await middleware(req as Request, res as Response, next as NextFunction);
    expect(next.called).to.be.false;
  });

  it('should throw an error if the validator does not exist', () => {
    expect(() =>
      validatorMiddleware('NonExistentValidator' as keyof typeof Validators),
    ).to.throw(`'NonExistentValidator' validator does not exist`);
  });

  it('should call next with an error if a non-Joi error occurs', async () => {
    // Mock a validator in Validators
    const mockSchema = {
      body: Joi.object({
        name: Joi.string().required(),
      }),
    };
    sinon.stub(Validators, 'findProjectDto').value(mockSchema);

    const error = new Error('Unexpected error');
    sinon.stub(mockSchema.body, 'validateAsync').rejects(error);

    const middleware = validatorMiddleware(
      'findProjectDto' as keyof typeof Validators,
    );
    await middleware(req as Request, res as Response, next as NextFunction);

    expect(next.calledOnceWith(error)).to.be.true;
  });
});
