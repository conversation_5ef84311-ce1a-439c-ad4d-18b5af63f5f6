import {expect} from 'chai';
import sinon from 'sinon';
import {Transaction as SequelizeTransaction, Model} from 'sequelize';
import * as transactionService from '../../services/transaction.service';
import {TransactionStatus} from '../../enums/transaction-status.enum';
import {TransactionType} from '../../enums/transaction-type.enum';

describe('Transaction Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        transactionRepository: {
          createAll: sandbox.stub(),
          findOne: sandbox.stub(),
        },
      },
      models: {
        Transaction: {
          update: sandbox.stub(),
        },
      },
      logger: {
        info: sandbox.stub(),
      },
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('addImageThumbnailTransaction', () => {
    it('should create transaction records for generating thumbnails', async () => {
      const mockTransaction = {} as SequelizeTransaction;
      const mockAssets = [
        {
          getDataValue: (key: string) =>
            key === 'id'
              ? 1
              : key === 'assetPath'
                ? 'path/to/asset1.jpg'
                : null,
        },
        {
          getDataValue: (key: string) =>
            key === 'id'
              ? 2
              : key === 'assetPath'
                ? 'path/to/asset2.jpg'
                : null,
        },
      ] as unknown as Model[];

      const mockParameters = {projectId: 123, tenantId: 456};

      const expectedPayload = [
        {
          type: TransactionType.IMAGE_THUMBNAIL,
          status: TransactionStatus.PENDING,
          parameters: {
            ...mockParameters,
            assetId: 1,
            assetPath: 'path/to/asset1.jpg',
          },
        },
        {
          type: TransactionType.IMAGE_THUMBNAIL,
          status: TransactionStatus.PENDING,
          parameters: {
            ...mockParameters,
            assetId: 2,
            assetPath: 'path/to/asset2.jpg',
          },
        },
      ];

      const mockResult = [{uuid: 'uuid1'}, {uuid: 'uuid2'}];

      dummyContext.repositories.transactionRepository.createAll.resolves(
        mockResult,
      );

      const result = await transactionService.addImageThumbnailTransaction(
        mockAssets,
        mockParameters,
        mockTransaction,
        dummyContext,
      );

      expect(result).to.deep.equal(mockResult);
      expect(
        dummyContext.repositories.transactionRepository.createAll.calledOnce,
      ).to.be.true;
      expect(
        dummyContext.repositories.transactionRepository.createAll.firstCall
          .args[0],
      ).to.deep.equal(expectedPayload);
      expect(
        dummyContext.repositories.transactionRepository.createAll.firstCall
          .args[1],
      ).to.deep.equal({
        transaction: mockTransaction,
      });
    });

    it('should handle errors when creating transaction records', async () => {
      const mockTransaction = {} as SequelizeTransaction;
      const mockAssets = [] as unknown as Model[];
      const mockParameters = {projectId: 123, tenantId: 456};
      const error = new Error('Database error');
      dummyContext.repositories.transactionRepository.createAll.rejects(error);

      try {
        await transactionService.addImageThumbnailTransaction(
          mockAssets,
          mockParameters,
          mockTransaction,
          dummyContext,
        );
        expect.fail('Expected error was not thrown');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });
  });

  describe('getPendingImageThumbnailTransactions', () => {
    beforeEach(() => {
      // Stub logger to prevent actual logging in tests
      sinon.stub(console, 'info');
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should find and claim a pending transaction successfully', async () => {
      const mockFoundTransaction = {
        get: (key: string) => (key === 'uuid' ? 'test-uuid' : undefined),
        dataValues: {
          uuid: 'test-uuid',
          type: TransactionType.IMAGE_THUMBNAIL,
          status: TransactionStatus.PENDING,
          parameters: {
            projectId: 123,
            tenantId: 456,
            assetId: 789,
            assetPath: 'path/to/asset.jpg',
          },
        },
      };

      const mockUpdatedTransaction = [
        1,
        [
          {
            uuid: 'test-uuid',
            type: TransactionType.IMAGE_THUMBNAIL,
            status: TransactionStatus.IN_PROGRESS,
            parameters: {
              projectId: 123,
              tenantId: 456,
              assetId: 789,
              assetPath: 'path/to/asset.jpg',
            },
          },
        ],
      ];

      dummyContext.repositories.transactionRepository.findOne.resolves(
        mockFoundTransaction,
      );
      dummyContext.models.Transaction.update.resolves(mockUpdatedTransaction);

      const result =
        await transactionService.getPendingImageThumbnailTransactions(
          dummyContext,
        );

      expect(result).to.deep.equal(
        (
          mockUpdatedTransaction[1] as (typeof mockUpdatedTransaction)[1] extends Array<any>
            ? (typeof mockUpdatedTransaction)[1]
            : any[]
        )[0],
      );
      expect(dummyContext.repositories.transactionRepository.findOne.calledOnce)
        .to.be.true;
      expect(
        dummyContext.repositories.transactionRepository.findOne.firstCall
          .args[0],
      ).to.deep.equal({
        status: TransactionStatus.PENDING,
        type: TransactionType.IMAGE_THUMBNAIL,
      });
      expect(dummyContext.models.Transaction.update.calledOnce).to.be.true;
      expect(
        dummyContext.models.Transaction.update.firstCall.args[0],
      ).to.deep.equal({
        status: TransactionStatus.IN_PROGRESS,
      });
      expect(
        dummyContext.models.Transaction.update.firstCall.args[1].where,
      ).to.deep.equal({
        uuid: 'test-uuid',
      });
    });

    it('should return undefined when no pending transactions are found', async () => {
      dummyContext.repositories.transactionRepository.findOne.resolves(null);

      const result =
        await transactionService.getPendingImageThumbnailTransactions(
          dummyContext,
        );

      expect(result).to.be.undefined;
    });

    it('should handle errors when finding pending transactions', async () => {
      const error = new Error('Database error');
      dummyContext.repositories.transactionRepository.findOne.rejects(error);

      try {
        await transactionService.getPendingImageThumbnailTransactions(
          dummyContext,
        );
        expect.fail('Expected error was not thrown');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });

    it('should handle errors when updating transaction status', async () => {
      const mockFoundTransaction = {
        get: (key: string) => (key === 'uuid' ? 'test-uuid' : undefined),
        dataValues: {
          uuid: 'test-uuid',
          status: TransactionStatus.PENDING,
        },
      };

      dummyContext.repositories.transactionRepository.findOne.resolves(
        mockFoundTransaction,
      );
      const error = new Error('Update failed');
      dummyContext.models.Transaction.update.rejects(error);

      try {
        await transactionService.getPendingImageThumbnailTransactions(
          dummyContext,
        );
        expect.fail('Expected error was not thrown');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });
  });
});
