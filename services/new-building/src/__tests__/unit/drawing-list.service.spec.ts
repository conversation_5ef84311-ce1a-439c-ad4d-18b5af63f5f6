import { expect } from 'chai';
import sinon from 'sinon';
import s3HelperService from '../../services/s3-helper.service';
import DrawingsService from '../../services/drawings-list.service';
import { userAllPermission } from '../data/user.data';
import { CustomError } from '../../utils';
import * as fileUtils from '../../utils/common.utils';
import BaseRepository from '../../repositories/base.repository';

describe('DrawingsList Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create base repository stubs
    const baseRepoStub = {
      findAll: sandbox.stub().resolves([]),
      findById: sandbox.stub().resolves(null),
      findOne: sandbox.stub().resolves(null),
      create: sandbox.stub().resolves({}),
      update: sandbox.stub().resolves([1]),
      delete: sandbox.stub().resolves(1),
      findAndCountAll: sandbox.stub().resolves({ count: 0, rows: [] }),
      getOptionsByKey: sandbox.stub().resolves([]),
      truncateTableData: sandbox.stub().resolves(),
      createAll: sandbox.stub().resolves([]),
      updateAll: sandbox.stub().resolves([1]),
    };

    dummyContext = {
      repositories: {
        drawingListRepository: {
          ...baseRepoStub,
        } as unknown as BaseRepository<any>,
        projectRepository: { ...baseRepoStub } as unknown as BaseRepository<any>,
        projectDrawingRepository: {
          ...baseRepoStub,
        } as unknown as BaseRepository<any>,
      },
      models: {
        DrawingsList: {
          bulkCreate: sandbox.stub().resolves([]),
          findAll: sandbox.stub().resolves([]),
          update: sandbox.stub().resolves([1]),
          create: sandbox.stub().resolves({}),
        },
        DrawingSource: {
          create: sandbox.stub().resolves({ dataValues: { id: 1 } }),
        },
        ProjectDrawing: {
          findOne: sandbox.stub().resolves(null),
        },
      },
      user: userAllPermission,
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createDrawingFromExcel', () => {
    it('should process an Excel file and create drawings successfully', async () => {
      const mockFileKey = 'folder/drawings.xlsx';
      const mockProjectId = 1;
      const mockBuffer = Buffer.from('mock file content');
      const mockFileData = [
        {
          'Drawing Number*': 'D001',
          'Drawing Name*': 'Drawing 1',
          Discipline: 'Mechanical',
        },
        {
          'Drawing Number*': 'D002',
          'Drawing Name*': 'Drawing 2',
          Discipline: 'Electrical',
        },
      ];

      dummyContext.repositories.projectRepository.findById.resolves({
        id: mockProjectId,
      });

      sandbox.stub(s3HelperService, 'getTheFileStream').resolves({
        on: sandbox.stub().callsFake((event, callback) => {
          if (event === 'data') callback(mockBuffer);
          if (event === 'end') callback();
        }),
      } as unknown as NodeJS.ReadableStream);

      sandbox.stub(fileUtils, 'processExcelAndCSV').resolves(mockFileData);

      dummyContext.repositories.drawingListRepository.findAll.resolves([
        {
          dataValues: {
            projectId: mockProjectId,
            name: 'Drawing 3',
            drawingNo: 'D0011',
          },
        },
      ]);

      const mockCreatedDrawings = [
        {
          drawingNo: 'D002',
          name: 'Drawing 2',
          discipline: 'Electrical',
          projectId: mockProjectId,
        },
      ];
      dummyContext.models.DrawingsList.bulkCreate.resolves(mockCreatedDrawings);

      const result = await DrawingsService.createDrawingFromExcel(
        {
          fileKey: mockFileKey,
          projectId: mockProjectId,
        },
        userAllPermission,
        dummyContext,
      );

      expect(result).to.deep.equal({
        createdDrawings: mockCreatedDrawings,
        duplicatedItems: [],
      });
    });

    it('should throw error if project does not exist', async () => {
      dummyContext.repositories.projectRepository.findById.resolves(null);
      try {
        await DrawingsService.createDrawingFromExcel(
          { fileKey: 'file.xlsx', projectId: 123 },
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Project with ID 123 not found');
      }
    });

    it('should throw error if file is empty or invalid', async () => {
      dummyContext.repositories.projectRepository.findById.resolves({ id: 1 });
      sandbox.stub(fileUtils, 'processExcelAndCSV').resolves([]);
      try {
        await DrawingsService.createDrawingFromExcel(
          { fileKey: 'file.xlsx', projectId: 1 },
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('The file is empty or contains invalid data.');
      }
    });

    it('should throw error if duplicate drawing exists', async () => {
      const mockFileData = [
        { 'Drawing Number*': 'D001', 'Drawing Name*': 'Drawing 1', Discipline: 'Mech' },
      ];
      dummyContext.repositories.projectRepository.findById.resolves({ id: 1 });
      sandbox.stub(fileUtils, 'processExcelAndCSV').resolves(mockFileData);
      dummyContext.repositories.drawingListRepository.findAll.resolves([
        { dataValues: { projectId: 1, name: 'Drawing 1', drawingNo: 'D001' } },
      ]);
      dummyContext.models.DrawingSource.create.resolves({ dataValues: { id: 1 } });
      try {
        await DrawingsService.createDrawingFromExcel(
          { fileKey: 'file.xlsx', projectId: 1 },
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('The uploaded drawing details already exist');
      }
    });
  });

  describe('createDrawingList', () => {
    it('should throw error if drawing number is missing', async () => {
      const fileData = [{ 'Drawing Name*': 'Name', Discipline: 'Disc' }];
      try {
        await DrawingsService.createDrawingList(
          fileData as any,
          1,
          userAllPermission,
          1,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('Drawing number is missing');
      }
    });

    it('should throw error if drawing name is missing', async () => {
      const fileData = [{ 'Drawing Number*': 'D001', Discipline: 'Disc' }];
      try {
        await DrawingsService.createDrawingList(
          fileData as any,
          1,
          userAllPermission,
          1,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('Drawing name is missing');
      }
    });

    it('should throw error if duplicate items found', async () => {
      dummyContext.repositories.drawingListRepository.findAll.resolves([
        { dataValues: { projectId: 1, name: 'Name', drawingNo: 'D001' } },
      ]);
      const fileData = [
        { 'Drawing Number*': 'D001', 'Drawing Name*': 'Name', Discipline: 'Disc' },
      ];
      try {
        await DrawingsService.createDrawingList(
          fileData as any,
          1,
          userAllPermission,
          1,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('The uploaded drawing details already exist');
      }
    });

    it('should create new drawings and return result', async () => {
      dummyContext.repositories.drawingListRepository.findAll.resolves([]);
      dummyContext.models.DrawingsList.bulkCreate.resolves([{ id: 1 }]);
      const fileData = [
        { 'Drawing Number*': 'D001', 'Drawing Name*': 'Name', Discipline: 'Disc' },
      ];
      const result = await DrawingsService.createDrawingList(
        fileData as any,
        1,
        userAllPermission,
        1,
        dummyContext,
      );
      expect(result.createdDrawings).to.deep.equal([{ id: 1 }]);
      expect(result.duplicatedItems).to.deep.equal([]);
    });
  });

  describe('findDrawingsById', () => {
    it('should return drawing if found', async () => {
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      const result = await DrawingsService.findDrawingsById(1, userAllPermission, dummyContext);
      expect(result).to.deep.equal({ id: 1 });
    });

    it('should throw error if drawing not found', async () => {
      dummyContext.repositories.drawingListRepository.findOne.resolves(null);
      try {
        await DrawingsService.findDrawingsById(1, userAllPermission, dummyContext);
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('Drawings not available with the given id');
      }
    });
  });

  describe('getAllDrawings', () => {
    it('should return paginated drawings', async () => {
      dummyContext.repositories.drawingListRepository.findAndCountAll.resolves({
        count: 2,
        rows: [
          { toJSON: () => ({ id: 1, projectDrawings: [{ version: 2 }, { version: 1 }] }) },
          { toJSON: () => ({ id: 2, projectDrawings: [] }) },
        ],
      });
      const result = await DrawingsService.getAllDrawings(
        { page: 1, limit: 1, projectId: 1 },
        userAllPermission,
        dummyContext,
      );
      expect(result.data.length).to.equal(2);
      expect(result.pagination.totalItems).to.equal(2);
    });

    it('should apply search filter', async () => {
      dummyContext.repositories.drawingListRepository.findAndCountAll.resolves({
        count: 1,
        rows: [{ toJSON: () => ({ id: 1, projectDrawings: [] }) }],
      });
      const result = await DrawingsService.getAllDrawings(
        { search: 'test' },
        userAllPermission,
        dummyContext,
      );
      expect(result.data.length).to.equal(1);
    });

    it('should use id as order when sortBy is name', async () => {
      dummyContext.repositories.drawingListRepository.findAndCountAll.resolves({
        count: 1,
        rows: [{ toJSON: () => ({ id: 1, projectDrawings: [] }) }],
      });
      const result = await DrawingsService.getAllDrawings(
        { sortBy: 'name', sortOrder: 'ASC' },
        userAllPermission,
        dummyContext,
      );
      expect(result.data.length).to.equal(1);
      // Optionally, check that the repository was called with the correct order
      const callArgs = dummyContext.repositories.drawingListRepository.findAndCountAll.getCall(0).args[0];
      expect(callArgs.orderBy).to.deep.equal([['id', 'ASC']]);
    });
  });

  describe('buildIncludeSection', () => {
    it('should return empty array if no permission', () => {
      const result = DrawingsService.buildIncludeSection({}, false, dummyContext.models);
      expect(result).to.deep.equal([]);
    });

    it('should build include with dueDate and status', () => {
      const query = {
        dueDate: { startDate: '2023-01-01', endDate: '2023-12-31' },
        status: 'active',
      };
      const result = DrawingsService.buildIncludeSection(query, true, dummyContext.models);
      expect(result[0]).to.have.property('model');
      // expect(result[0].where).to.have.property('due_date');
      // expect(result[0].where).to.have.property('status');
    });
  });

  describe('buildSearchFilters', () => {
    it('should build search filters', () => {
      const filters = DrawingsService.buildSearchFilters('abc');
      expect(filters).to.be.an('array').that.is.not.empty;
      expect(filters[0]).to.have.property('name');
    });
  });

  describe('deleteDrawingsById', () => {
    it('should throw error if drawing not found', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves(null);
      try {
        await DrawingsService.deleteDrawingsById(1, userAllPermission, dummyContext);
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('Drawings not available with the given id');
      }
    });

    it('should throw error if drawing already deleted', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({
        dataValues: { deletedAt: new Date() },
        projectDrawings: [],
      });
      try {
        await DrawingsService.deleteDrawingsById(1, userAllPermission, dummyContext);
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('already deleted');
      }
    });

    it('should throw error if files are linked', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({
        dataValues: { deletedAt: null },
        projectDrawings: [{}],
      });
      try {
        await DrawingsService.deleteDrawingsById(1, userAllPermission, dummyContext);
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.include('Cannot delete drawing');
      }
    });

    it('should delete drawing successfully', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({
        dataValues: { deletedAt: null },
        projectDrawings: [],
      });
      dummyContext.repositories.drawingListRepository.update.resolves([1]);
      const result = await DrawingsService.deleteDrawingsById(1, userAllPermission, dummyContext);
      expect(result.error).to.be.false;
      expect(result.data.id).to.equal(1);
    });
  });

  describe('getOptionsByKey', () => {
    it('should return unique values for key', async () => {
      dummyContext.repositories.drawingListRepository.getOptionsByKey.resolves(['A', 'B']);
      const result = await DrawingsService.getOptionsByKey('name', 1, dummyContext);
      expect(result.data).to.deep.equal(['A', 'B']);
      expect(result.error).to.be.false;
    });
  });
});
