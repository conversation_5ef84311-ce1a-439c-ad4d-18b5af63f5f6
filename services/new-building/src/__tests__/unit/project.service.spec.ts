import {expect} from 'chai';
import sinon from 'sinon';
import * as projectService from '../../services/projects.service';
import * as hullsService from '../../services/hulls.services';
import {
  projectData,
  inputProject,
  inputProjectWithCustom,
  newProject,
} from '../data/project.data';
import {HullStatus, ProjectDrawingStatus, ProjectStatus} from '../../enums';
import {userAllPermission, userWithOutHullPermission} from '../data/user.data';
import s3HelperService from '../../services/s3-helper.service';
import {vesselMasterDataService} from '../../services';

describe('Project Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        projectRepository: {
          findAll: sandbox.stub(),
          findAndCountAll: sandbox.stub(),
          findOne: sandbox.stub(),
          findById: sandbox.stub(),
          update: sandbox.stub(),
          getCountByStatus: sandbox.stub(),
        },
        projectHullRepository: {
          findAll: sandbox.stub(),
        },
        hullRepository: {
          findAll: sandbox.stub(),
          update: sandbox.stub(),
        },
        drawingListRepository: {
          findAll: sandbox.stub(),
        },
        projectDrawingRepository: {
          findAll: sandbox.stub(),
        },
      },
      models: {
        Project: {
          create: sandbox.stub(),
          update: sandbox.stub(),
          build: (data: any) => ({...data, toJSON: () => data}),
        },
        Hull: {
          update: sandbox.stub(),
        },
        HullClass: {
          update: sandbox.stub(),
        },
        ProjectHull: {
          update: sandbox.stub(),
          findAll: sandbox.stub(),
        },
      },
      sequelize: {
        transaction: sandbox.stub(),
      },
      user: userAllPermission,
    };
  });

  afterEach(() => {
    sandbox.restore();
    sinon.restore();
  });

  describe('findAllProjects', () => {
    it('should return all projects', async () => {
      const fakeProjects = [projectData[0], projectData[1]];
      dummyContext.repositories.projectRepository.findAll.resolves(
        fakeProjects,
      );

      const result = await projectService.findAllProjects(dummyContext);
      expect(result).to.deep.equal(fakeProjects);
    });
  });

  describe('getAllProjects', () => {
    it('should return projects with pagination', async () => {
      const mockProjects = [projectData[0], projectData[1], projectData[2]];
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {page: 1, limit: 3, offset: 0, orderBy: ['createdAt'], fields: 'name'},
        dummyContext,
      );
      expect(result.data.length).to.equal(3);
    });
  });

  describe('createProject', () => {
    it('should create a project with hulls and assets', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.resolves({
        getDataValue: (k: string) => (k === 'id' ? 1 : 'admin'),
      });
      sandbox.stub(hullsService, 'createHullList').resolves({createdHulls: []});
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      const inputProjectWithHulls = {
        ...inputProject,
        hulls: [{hullNo: 'HULL-001', status: HullStatus.ON_GOING}],
      };

      const result = await projectService.createProject(
        inputProjectWithHulls as any,
        userAllPermission,
        dummyContext,
      );
      expect(result.getDataValue('id')).to.equal(1);
      expect(mockTransaction.commit.calledOnce).to.be.true;
    });

    it('should rollback transaction and throw error on failure', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.throws(new Error('DB Error'));
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      try {
        await projectService.createProject(
          {} as any,
          userAllPermission,
          dummyContext,
        );
      } catch (error: any) {
        expect(error.message).to.include('DB Error');
        expect(mockTransaction.rollback.calledOnce).to.be.true;
      }
    });

    it('should throw error hull permission not given', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);

      try {
        await projectService.createProject(
          newProject as any,
          userWithOutHullPermission,
          dummyContext,
        );
      } catch (error: any) {
        expect(error.message).to.include(
          'You do not have permission to perform this action.',
        );
        expect(mockTransaction.rollback.calledOnce).to.be.true;
      }
    });

    it('should create a project without creating any assets', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.resolves({
        getDataValue: (k: string) => (k === 'id' ? 2 : 'admin'),
      });
      sandbox.stub(hullsService, 'createHullList').resolves({createdHulls: []});
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      const {
        assetName,
        assetType,
        assetPath,
        assetVersion,
        referenceType,
        ...input
      } = inputProject;

      const result = await projectService.createProject(
        input as any,
        userAllPermission,
        dummyContext,
      );
      expect(result.getDataValue('id')).to.equal(2);
      expect(mockTransaction.commit.calledOnce).to.be.true;
    });

    it('should throw error for vesselClasses not matched', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.resolves({
        getDataValue: (k: string) => (k === 'id' ? 2 : 'admin'),
      });
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      inputProject.hulls.forEach(hull => {
        hull.vesselClasses = ['test'];
        hull.flag = 'Flag A';
      });

      const {
        assetName,
        assetType,
        assetPath,
        assetVersion,
        referenceType,
        ...input
      } = inputProject;

      try {
        await projectService.createProject(
          input as any,
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include('Invalid vessel class');
      }
    });

    it('should throw error for flag not matched', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.resolves({
        getDataValue: (k: string) => (k === 'id' ? 2 : 'admin'),
      });
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      inputProject.hulls.forEach(hull => {
        hull.vesselClasses = ['NV'];
        hull.flag = 'test';
      });

      const {
        assetName,
        assetType,
        assetPath,
        assetVersion,
        referenceType,
        ...input
      } = inputProject;

      try {
        await projectService.createProject(
          input as any,
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include('Invalid flag provided');
      }
    });

    it('should create a project with custom ship type and engine type', async () => {
      const mockTransaction = {
        commit: sandbox.stub(),
        rollback: sandbox.stub(),
      };
      dummyContext.sequelize.transaction.resolves(mockTransaction);
      dummyContext.models.Project.create.resolves({
        getDataValue: (k: string) => (k === 'id' ? 2 : 'admin'),
      });
      sandbox.stub(hullsService, 'createHullList').resolves({createdHulls: []});
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });

      const result = await projectService.createProject(
        inputProjectWithCustom as any,
        userAllPermission,
        dummyContext,
      );
      expect(result.getDataValue('id')).to.equal(2);
      expect(mockTransaction.commit.calledOnce).to.be.true;
    });
  });

  describe('deleteProject', () => {
    let transactionCommitStub: any;
    let transactionRollbackStub: any;

    beforeEach(() => {
      transactionCommitStub = sinon.stub();
      transactionRollbackStub = sinon.stub();

      // Reset the transaction stub if it exists
      if (dummyContext.sequelize.transaction.restore) {
        dummyContext.sequelize.transaction.restore();
      }

      dummyContext.sequelize.transaction = sandbox.stub().resolves({
        commit: transactionCommitStub,
        rollback: transactionRollbackStub,
      } as any);

      dummyContext.models.Project.update = sandbox.stub().resolves([1]);
      dummyContext.models.Hull.update = sandbox.stub().resolves([1]);
      dummyContext.models.HullClass.update = sandbox.stub().resolves([1]);
      dummyContext.models.ProjectHull.update = sandbox.stub().resolves([1]);
      dummyContext.models.ProjectHull.findAll = sandbox
        .stub()
        .resolves([
          {dataValues: {hullId: 1}},
          {dataValues: {hullId: 2}},
        ] as any);
    });

    it('should throw error if project not found', async () => {
      dummyContext.repositories.projectRepository.findById.resolves(null);

      try {
        await projectService.deleteProject(1, userAllPermission, dummyContext);
      } catch (error: any) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Project not found');
      }
    });

    it('should throw error if project is closed', async () => {
      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {status: ProjectStatus.CLOSED, deleted: false},
      } as any);

      try {
        await projectService.deleteProject(1, userAllPermission, dummyContext);
      } catch (error: any) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.include('already completed/deleted');
      }
    });

    it('should throw error if project is already deleted', async () => {
      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {status: ProjectStatus.ON_GOING, deletedAt: new Date()},
      } as any);

      try {
        await projectService.deleteProject(1, userAllPermission, dummyContext);
      } catch (error: any) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.include('already completed/deleted');
      }
    });

    it('should soft delete the project and related entities successfully', async () => {
      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {id: 123, status: ProjectStatus.ON_GOING},
      });
      const projectId = 123;

      const result = await projectService.deleteProject(
        projectId,
        userAllPermission,
        dummyContext,
      );

      expect(dummyContext.sequelize.transaction.calledOnce).to.be.true;
      expect(dummyContext.models.ProjectHull.findAll.calledOnce).to.be.true;
      expect(dummyContext.models.Project.update.calledOnce).to.be.true;
      expect(dummyContext.models.Hull.update.calledOnce).to.be.true;
      expect(dummyContext.models.HullClass.update.calledOnce).to.be.true;
      expect(dummyContext.models.ProjectHull.update.calledOnce).to.be.true;
      expect(transactionCommitStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({message: 'Project deleted successfully'});
    });

    it('should rollback transaction if any error occurs', async () => {
      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {id: 123, status: ProjectStatus.ON_GOING},
      });
      const projectId = 123;

      dummyContext.models.Project.update.rejects(new Error('Update failed'));

      try {
        await projectService.deleteProject(
          projectId,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal('Update failed');
        expect(transactionRollbackStub.calledOnce).to.be.true;
      }
    });
  });

  describe('getAllProjects', () => {
    const mockProjects = [projectData[0], projectData[1], projectData[2]];

    const mockQuery = {
      page: 1,
      limit: 3,
      offset: 0,
      orderBy: ['createdAt'],
      fields: 'name',
    };

    it('should return projects with pagination', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        mockQuery,
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should return projects with searchParam', async () => {
      const mockQueryWithSearch = {
        ...mockQuery,
        searchParam: 'name',
        search: 'Pro',
      };
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        mockQueryWithSearch,
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should sort projects with pagination', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          ...mockQuery,
          sortBy: 'name',
        },
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should sort projects with associate tables', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          ...mockQuery,
          sortBy: 'projectType',
        },
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should search projects with name', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          ...mockQuery,
          search: 'Apple',
        },
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should filter projects with projectType', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          ...mockQuery,
          projectTypeId: 1,
        },
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });

    it('should filter projects with date filter', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          ...mockQuery,
          createdAt: {startDate: '2023-01-01', endDate: '2023-12-31'},
        },
        dummyContext,
      );
      expect(result.data.length).to.deep.equal(3);
    });

    it('should get projects without passing any params', async () => {
      dummyContext.repositories.projectRepository.findAndCountAll.resolves({
        count: 3,
        rows: mockProjects,
      });

      const result = await projectService.getAllProjects(
        {
          fields: ['projectType', 'name'],
        },
        dummyContext,
      );

      expect(result.data.length).to.deep.equal(3);
    });
  });

  describe('findByIdProject', () => {
    let generatePresignedDownloadUrlStub: sinon.SinonStub;

    beforeEach(() => {
      // Reset the findOne stub if it exists
      if (dummyContext.repositories.projectRepository.findOne.restore) {
        dummyContext.repositories.projectRepository.findOne.restore();
      }

      dummyContext.repositories.projectRepository.findOne = sandbox.stub();
      generatePresignedDownloadUrlStub = sandbox.stub(
        s3HelperService,
        'generatePresignedDownloadUrl',
      );
    });

    it('should call projectRepository.findOne with correct includes and return project', async () => {
      const mockProject = {...projectData[0], dataValues: projectData[0]};

      dummyContext.repositories.projectRepository.findOne.resolves(mockProject);

      const result = await projectService.findByIdProject(
        1,
        userAllPermission,
        dummyContext,
      );

      expect(dummyContext.repositories.projectRepository.findOne.calledOnce).to
        .be.true;
      expect(result).to.deep.equal(projectData[0]);
    });

    it('should throw an error if projectRepository throws', async () => {
      dummyContext.repositories.projectRepository.findOne.rejects(
        new Error('DB error'),
      );

      try {
        await projectService.findByIdProject(
          1,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('DB error');
      }
    });

    it('should throw 404 File Not Found', async () => {
      dummyContext.repositories.projectRepository.findOne.resolves(null);

      try {
        await projectService.findByIdProject(
          1,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Project not found');
      }
    });

    it('should update hull asset paths with presigned URLs and return project with hulls', async () => {
      const mockProject = {
        ...projectData[0],
        hulls: [
          {
            id: 1,
            dataValues: {
              assetPath: 'hull-asset-path-1',
              assetName: 'hull-asset-1',
            },
          },
          {
            id: 2,
            dataValues: {
              assetPath: 'hull-asset-path-2',
              assetName: 'hull-asset-2',
            },
          },
        ],
        dataValues: projectData[0],
      };

      dummyContext.repositories.projectRepository.findOne.resolves(mockProject);
      generatePresignedDownloadUrlStub
        .withArgs('hull-asset-path-1', 'hull-asset-1')
        .resolves('presigned-url-1');
      generatePresignedDownloadUrlStub
        .withArgs('hull-asset-path-2', 'hull-asset-2')
        .resolves('presigned-url-2');

      const result = await projectService.findByIdProject(
        1,
        userAllPermission,
        dummyContext,
      );

      expect(dummyContext.repositories.projectRepository.findOne.calledOnce).to
        .be.true;
      expect(result).to.deep.equal(projectData[0]);
    });

    it('should update project asset path with presigned URL and return project with asset', async () => {
      const mockProject = {
        ...projectData[0],
        dataValues: {
          ...projectData[0],
          assetPath: 'project-asset-path',
          assetName: 'project-asset',
        },
      };

      dummyContext.repositories.projectRepository.findOne.resolves(mockProject);
      generatePresignedDownloadUrlStub
        .withArgs('project-asset-path', 'project-asset')
        .resolves('presigned-project-url');

      const result = await projectService.findByIdProject(
        1,
        userAllPermission,
        dummyContext,
      );

      expect(dummyContext.repositories.projectRepository.findOne.calledOnce).to
        .be.true;
      expect(result).to.deep.equal({
        ...projectData[0],
        assetPath: 'presigned-project-url',
        assetName: 'project-asset',
      });
    });
  });

  describe('updateProject', () => {
    const projectId = 1;
    const existingProjectObject = {
      id: projectId,
      name: 'Project A',
      tenantId: 0,
      owner: 'owner1',
      shipType: 'test',
      fuelTypeId: 0,
      deletedAt: null,
      engineType: 'test',
      projectTypeId: 0,
      projectDescription: '',
      totalTimeTaken: 0,
      status: ProjectStatus.ON_GOING,
    };

    beforeEach(() => {
      sandbox.stub(vesselMasterDataService, 'getVesselMasterData').resolves({
        data: {
          miscCurrencys: [{value: 'USD'}],
          vesselTypes: [{value: 'Cargo'}],
          miscEngines: [{value: 'Engine A'}],
          vesselClasss: [{value: 'NV'}],
          flags: [{value: 'Flag A'}],
        },
      });
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should update a project and return the updated project', async () => {
      const updates = {
        name: 'Updated Project Name',
        projectDescription: 'Updated project description.',
        status: ProjectStatus.CLOSED,
      };
      const existingProject = {
        ...existingProjectObject,
        toJSON: () => existingProjectObject,
        dataValues: {...existingProjectObject},
      };
      dummyContext.repositories.projectRepository.findById
        .onCall(0)
        .resolves(existingProject) // first call
        .onCall(1)
        .resolves({
          ...existingProject,
          ...updates,
          dataValues: {...existingProjectObject, ...updates},
        });

      dummyContext.repositories.projectRepository.update.resolves([1]);

      sandbox.stub(s3HelperService, 'uploadProjectLogsToS3').resolves();
      const result = await projectService.updateProject(
        projectId,
        updates,
        userAllPermission,
        dummyContext,
      );

      expect(result).to.deep.equal({
        ...existingProject,
        ...updates,
        dataValues: {...existingProjectObject, ...updates},
      });
    });

    it('should update a project with custom shiptype and engine type and return the updated project', async () => {
      const updates = {
        name: 'Updated Project Name',
        projectDescription: 'Updated project description.',
        isShipTypeCustom: true,
        isEngineTypeCustom: true,
        shipType: 'test',
        engineType: 'test',
      };

      const existingProject = {
        ...existingProjectObject,
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        toJSON: () => existingProjectObject,
        dataValues: {...existingProjectObject},
      };
      dummyContext.repositories.projectRepository.findById
        .onCall(0)
        .resolves(existingProject) // first call
        .onCall(1)
        .resolves({
          ...existingProject,
          ...updates,
          dataValues: {...existingProjectObject, ...updates},
        });

      dummyContext.repositories.projectRepository.update.resolves([1]);

      sandbox.stub(s3HelperService, 'uploadProjectLogsToS3').resolves();
      const result = await projectService.updateProject(
        projectId,
        updates,
        userAllPermission,
        dummyContext,
      );

      expect(result).to.deep.equal({
        ...existingProject,
        ...updates,
        dataValues: {...existingProjectObject, ...updates},
      });
    });

    it('should return error if the project does not exist', async () => {
      dummyContext.repositories.projectRepository.findById.resolves(null);

      try {
        await projectService.updateProject(
          projectId,
          {name: 'Updated Project Name'},
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.an('error');
      }
    });

    it('should throw an error if the update fails', async () => {
      const existingProject = {
        ...existingProjectObject,
        toJSON: () => existingProjectObject,
      };
      dummyContext.repositories.projectRepository.findById.resolves(
        existingProject,
      );
      dummyContext.repositories.projectRepository.update.rejects(
        new Error('Update failed'),
      );

      try {
        await projectService.updateProject(
          projectId,
          {name: 'Updated Project Name'},
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.an('error');
      }
    });

    it('should throw error when currency not matched', async () => {
      const updates = {
        name: 'Updated Project Name',
        projectDescription: 'Updated project description.',
        isShipTypeCustom: true,
        isEngineTypeCustom: true,
        shipType: 'test',
        engineType: 'test',
        currency: 'INR', // Invalid currency
      };

      const existingProject = {
        ...existingProjectObject,
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        toJSON: () => existingProjectObject,
        dataValues: {...existingProjectObject},
      };
      dummyContext.repositories.projectRepository.findById.resolves(
        existingProject,
      );

      try {
        await projectService.updateProject(
          projectId,
          updates,
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include(
          'Invalid currency provided',
        );
      }
    });

    it('should throw error when shipType not matched', async () => {
      const updates = {
        name: 'Updated Project Name',
        projectDescription: 'Updated project description.',
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        shipType: 'test',
        engineType: 'test',
      };

      const existingProject = {
        ...existingProjectObject,
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        toJSON: () => existingProjectObject,
        dataValues: {...existingProjectObject},
      };
      dummyContext.repositories.projectRepository.findById.resolves(
        existingProject,
      );

      try {
        await projectService.updateProject(
          projectId,
          updates,
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include(
          'Invalid shipType provided',
        );
      }
    });

    it('should throw error when engineType not matched', async () => {
      const updates = {
        name: 'Updated Project Name',
        projectDescription: 'Updated project description.',
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        shipType: 'Cargo',
        engineType: 'test',
      };

      const existingProject = {
        ...existingProjectObject,
        isShipTypeCustom: false,
        isEngineTypeCustom: false,
        toJSON: () => existingProjectObject,
        dataValues: {...existingProjectObject},
      };
      dummyContext.repositories.projectRepository.findById.resolves(
        existingProject,
      );

      try {
        await projectService.updateProject(
          projectId,
          updates,
          userAllPermission,
          dummyContext,
        );
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include('Invalid engineType');
      }
    });
  });

  describe('countProjectsByStatus', () => {
    it('should return the count of onGoing and closed projects', async () => {
      const mockResult = [
        {status: ProjectStatus.ON_GOING, count: '10'},
        {status: ProjectStatus.CLOSED, count: '5'},
      ];

      dummyContext.repositories.projectRepository.getCountByStatus.resolves(
        mockResult,
      );

      const result = await projectService.countProjectsByStatus(dummyContext);

      expect(result).to.deep.equal(mockResult);
    });
  });

  describe('completeProject', () => {
    let transactionCommitStub: any;
    let transactionRollbackStub: any;

    beforeEach(() => {
      transactionCommitStub = sandbox.stub();
      transactionRollbackStub = sandbox.stub();

      // Reset the transaction stub if it exists
      if (dummyContext.sequelize.transaction.restore) {
        dummyContext.sequelize.transaction.restore();
      }

      dummyContext.sequelize.transaction = sandbox.stub().resolves({
        commit: transactionCommitStub,
        rollback: transactionRollbackStub,
      } as any);
    });

    it('should complete the project successfully', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
        expenses: 1000,
        currency: 'USD',
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deletedAt: null,
          currency: 'USD',
        },
      });

      dummyContext.repositories.projectHullRepository.findAll.resolves([
        {dataValues: {id: 1}},
        {dataValues: {id: 2}},
      ]);
      dummyContext.repositories.hullRepository.findAll.resolves([]);
      dummyContext.repositories.drawingListRepository.findAll.resolves([]);
      dummyContext.repositories.projectDrawingRepository.findAll.resolves([]);
      dummyContext.repositories.projectRepository.update.resolves([1]);
      dummyContext.models.Hull.update.resolves([1]);

      const result = await projectService.completeProject(
        projectId,
        projectDataObj,
        dummyContext,
      );

      expect(dummyContext.sequelize.transaction.calledOnce).to.be.true;
      expect(transactionCommitStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        message: 'Project completed successfully',
        projectId,
        error: false,
      });
    });

    it('should throw an error if the project does not exist', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
        expenses: 1000,
      };
      dummyContext.repositories.projectRepository.findById.resolves(null);
      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `Project with ID ${projectId} not found`,
        );
      }
    });

    it('should throw an error if the project is already completed or deleted', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.CLOSED,
          deleted: false,
        },
      });

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `Project with ID ${projectId} is already completed/deleted`,
        );
        expect(transactionRollbackStub.called).to.be.false;
      }
    });

    it("should throw an error if the project don't have currency", async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
        expenses: 1000,
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
        },
      });

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `Project currency is not set. Set the currency before closing the project.`,
        );
        expect(transactionRollbackStub.called).to.be.false;
      }
    });

    it('should throw an error if the project currency and passing currency mismatch', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
        currency: 'INR',
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
          currency: 'AUD',
        },
      });

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(`Project currency is mismatched.`);
        expect(transactionRollbackStub.called).to.be.false;
      }
    });

    it('should throw an error if associated hulls and drawings are not closed', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
          currency: 'USD',
        },
      });

      dummyContext.repositories.projectHullRepository.findAll.resolves([
        {dataValues: {id: 1}},
        {dataValues: {id: 2}},
      ]);
      dummyContext.repositories.hullRepository.findAll.resolves([
        {dataValues: {id: 1, status: HullStatus.ON_GOING}},
      ]);
      dummyContext.repositories.drawingListRepository.findAll.resolves([]);
      dummyContext.repositories.projectDrawingRepository.findAll.resolves([
        {dataValues: {id: 1, status: ProjectDrawingStatus.REFERENCE_ONLY}},
      ]);

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `Project cannot be closed as there are pending hulls and approval drawings without an approved Original/Version.`,
        );
        expect(transactionRollbackStub.calledOnce).to.be.true;
      }
    });

    it('should throw an error if associated hulls are not closed', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
          currency: 'USD',
        },
      });

      dummyContext.repositories.projectHullRepository.findAll.resolves([
        {dataValues: {id: 1}},
        {dataValues: {id: 2}},
      ]);
      dummyContext.repositories.hullRepository.findAll.resolves([
        {dataValues: {id: 1, status: HullStatus.ON_GOING}},
      ]);
      dummyContext.repositories.drawingListRepository.findAll.resolves([]);
      dummyContext.repositories.projectDrawingRepository.findAll.resolves([]);

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `There are pending hulls that have not been marked as Completed. Please update their status before closing the project.`,
        );
        expect(transactionRollbackStub.calledOnce).to.be.true;
      }
    });

    it('should throw an error if associated drawings are not closed', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
          currency: 'USD',
        },
      });

      dummyContext.repositories.projectHullRepository.findAll.resolves([]);
      dummyContext.repositories.hullRepository.findAll.resolves([]);
      dummyContext.repositories.drawingListRepository.findAll.resolves([]);
      dummyContext.repositories.projectDrawingRepository.findAll.resolves([
        {dataValues: {id: 1, status: ProjectDrawingStatus.REFERENCE_ONLY}},
      ]);

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal(
          `Some approval drawings are missing an approved Original/Version. Please ensure all drawings have an approved document before proceeding.`,
        );
        expect(transactionRollbackStub.calledOnce).to.be.true;
      }
    });

    it('should rollback transaction if an error occurs', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deleted: false,
          currency: 'USD',
        },
      });

      dummyContext.repositories.projectHullRepository.findAll.rejects(
        new Error('DB Error'),
      );

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        throw new Error('Test failed: error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal('DB Error');
        expect(transactionRollbackStub.calledOnce).to.be.true;
      }
    });

    it('should map and filter projectDrawingsListIds correctly (cover line 512)', async () => {
      const projectId = 123;
      const projectDataObj = {
        completionDate: new Date(),
        expenses: 1000,
        currency: 'USD',
      };

      dummyContext.repositories.projectRepository.findById.resolves({
        dataValues: {
          id: projectId,
          status: ProjectStatus.ON_GOING,
          deletedAt: null,
          currency: 'USD',
        },
      });

      // projectDrawingList contains some with id, some without id, some with null
      dummyContext.repositories.projectHullRepository.findAll.resolves([
        {dataValues: {id: 1}},
        {dataValues: {id: 2}},
      ]);
      dummyContext.repositories.hullRepository.findAll.resolves([]);
      dummyContext.repositories.drawingListRepository.findAll.resolves([
        {dataValues: {id: 10}},
        {dataValues: {id: undefined}},
        {dataValues: {id: 20}},
        {dataValues: {id: null}},
      ]);
      // Simulate projectDrawingRepository.findAll returning a drawing with status not APPROVED
      dummyContext.repositories.projectDrawingRepository.findAll.resolves([
        {dataValues: {id: 10, status: ProjectDrawingStatus.REFERENCE_ONLY}},
        {dataValues: {id: 20, status: ProjectDrawingStatus.APPROVED}},
      ]);
      dummyContext.repositories.projectRepository.update.resolves([1]);
      dummyContext.models.Hull.update.resolves([1]);

      try {
        await projectService.completeProject(
          projectId,
          projectDataObj,
          dummyContext,
        );
        expect('Should have thrown').to.include(
          'Some approval drawings are missing an approved Original/Version',
        );
      } catch (err: any) {
        expect(err.message).to.include(
          'Some approval drawings are missing an approved Original/Version',
        );
      }

      // Check that only ids 10 and 20 are included (undefined/null filtered out)
      const drawingListCall =
        dummyContext.repositories.drawingListRepository.findAll.getCall(0);
      expect(drawingListCall).to.not.be.undefined;
      // Optionally, check that the correct ids were used in projectDrawingRepository.findAll
      const projectDrawingCall =
        dummyContext.repositories.projectDrawingRepository.findAll.getCall(0);
      expect(projectDrawingCall).to.not.be.undefined;
      const whereArg =
        projectDrawingCall.args[2] ?? projectDrawingCall.args[0]?.where;
      // Should include only [10, 20]
      const ids =
        whereArg?.drawingListId?.[
          Object.getOwnPropertySymbols(whereArg.drawingListId)[0]
        ] ?? whereArg?.drawingListId;
      // Remove null/undefined from ids before assertion
      const filteredIds = Array.isArray(ids)
        ? ids.filter((id: any) => typeof id === 'number')
        : ids;
      expect(filteredIds).to.deep.equal([10, 20]);
      // Ensure no null or undefined in ids
      expect(filteredIds.every((id: any) => typeof id === 'number')).to.be.true;
    });
  });
});
