import sinon from 'sinon';
import axios from 'axios';
import {expect} from 'chai';
import VesselMasterDataService from '../../services/vessel-master-data.service';

describe('VesselMasterData Service', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('getAccessToken', () => {
    it('should return an access token successfully', async () => {
      const mockToken = 'mockAccessToken';
      const axiosStub = sinon.stub(axios, 'request').resolves({
        data: {access_token: mockToken},
      });

      const token = await VesselMasterDataService.getAccessToken();

      expect(token).to.equal(mockToken);
      expect(axiosStub.calledOnce).to.be.true;
    });

    it('should return undefined if there is an error fetching the access token', async () => {
      sinon.stub(axios, 'request').rejects(new Error('Service error'));

      const token = await VesselMasterDataService.getAccessToken();

      expect(token).to.be.undefined;
    });
  });

  describe('getVesselMasterData', () => {
    it('should fetch vessel master data from API and cache it if not cached', async () => {
      const mockData = {miscCurrencys: [{id: '1', value: 'USD'}]};
      const axiosStub = sinon.stub(axios, 'request').resolves({data: mockData});
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves('token');
      const result = await VesselMasterDataService.getVesselMasterData();
      expect(result).to.deep.equal(mockData);
      expect(axiosStub.calledOnce).to.be.true;
    });

    it('should throw an error if the API request fails', async () => {
      sinon.stub(axios, 'request').rejects(new Error('Service error'));

      try {
        await VesselMasterDataService.getVesselMasterData();
        expect.fail('Expected error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal('Failed to fetch vessel master data');
      }
    });
  });

  describe('getUserDetails', () => {
    it('should fetch user details successfully', async () => {
      const mockToken = 'mockAccessToken';
      const mockUserDetails = [
        {
          id: 'fe139a24-4ea3-4a80-ba97-005c349a555d',
          username: 'user1',
          email: '<EMAIL>',
        },
        {
          id: 'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
          username: 'user2',
          email: '<EMAIL>',
        },
      ];

      // Stub the getAccessToken method
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves(mockToken);

      // Stub the axios request for fetching user details
      const axiosStub = sinon.stub(axios, 'request').resolves({
        data: mockUserDetails,
      });

      const userIds = [
        'fe139a24-4ea3-4a80-ba97-005c349a555d',
        'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
      ];

      const result = await VesselMasterDataService.getUserDetails(userIds);

      expect(result).to.deep.equal(mockUserDetails);
      expect(axiosStub.calledOnce).to.be.true;
      expect(axiosStub.args[0][0].url).to.include(
        'userId=fe139a24-4ea3-4a80-ba97-005c349a555d&userId=d7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
      );
    });

    it('should throw an error if the API request fails', async () => {
      const mockToken = 'mockAccessToken';

      // Stub the getAccessToken method
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves(mockToken);

      // Stub the axios request to simulate an API failure
      sinon.stub(axios, 'request').rejects(new Error('Service error'));

      const userIds = [
        'fe139a24-4ea3-4a80-ba97-005c349a555d',
        'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
      ];

      try {
        await VesselMasterDataService.getUserDetails(userIds);
        expect.fail('Expected error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal('Failed to fetch user details');
      }
    });

    it('should throw an error if the access token cannot be fetched', async () => {
      // Stub the getAccessToken method to simulate a failure
      sinon
        .stub(VesselMasterDataService, 'getAccessToken')
        .rejects(new Error('Failed to fetch access token'));

      const userIds = [
        'fe139a24-4ea3-4a80-ba97-005c349a555d',
        'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
      ];

      try {
        await VesselMasterDataService.getUserDetails(userIds);
        expect.fail('Expected error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal('Failed to fetch access token');
      }
    });

    it('should throw an error if the access token fetchec as null', async () => {
      // Stub the getAccessToken method to simulate a failure
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves(null);

      const userIds = [
        'fe139a24-4ea3-4a80-ba97-005c349a555d',
        'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
      ];

      try {
        await VesselMasterDataService.getUserDetails(userIds);
        expect.fail('Expected error was not thrown');
      } catch (error: any) {
        expect(error.message).to.equal('Unable to fetch access token');
      }
    });
  });
});
