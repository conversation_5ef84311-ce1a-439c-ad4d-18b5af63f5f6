import { expect } from 'chai';
import sinon from 'sinon';
import {
  S3Client,
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';
import S3HelperService, { S3Service } from '../../services/s3-helper.service';
import { mockS3Response } from '../data/s3-helper.service.data';
import * as s3PresignedPost from '@aws-sdk/s3-presigned-post';
import * as s3RequestPresigner from '@aws-sdk/s3-request-presigner';
import contentDisposition from 'content-disposition';

describe('S3HelperService', () => {
  let sandbox: sinon.SinonSandbox;
  let s3ClientStub: sinon.SinonStubbedInstance<S3Client>;
  let s3Service: S3Service;
  let createPresignedPostStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    s3ClientStub = sinon.createStubInstance(S3Client);
    (S3HelperService as any).s3Client = s3ClientStub;
    s3Service = new S3Service({ region: 'us-east-1', credentials: { accessKeyId: 'a', secretAccessKey: 'b' } });
    (s3Service as any).s3Client = s3ClientStub;
    process.env.AWS_S3_BUCKET_NAME = 'bucket';
    process.env.AWS_S3_SIGNED_URL_EXPIRY = '900';
    process.env.AWS_S3_LOGS_EXPIRY_DAYS = '365';

    createPresignedPostStub = sinon
      .stub(S3HelperService, 'generatePresignedPost')
      .resolves(mockS3Response.presignedPost);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generatePresignedPost', () => {
    it('should generate a presigned POST URL successfully', async () => {
      const result = await S3HelperService.generatePresignedPost({
        fileName: 'upload/test.pdf',
        contentType: 'application/pdf',
      });

      expect(result).to.have.property('url');
      expect(result).to.have.property('fields');
      expect(result).to.have.property('fileKey');
      expect(result.expiresIn).to.equal(900);
      expect(createPresignedPostStub.calledOnce).to.be.true;
    });

    it('should handle errors when generating presigned POST URL', async () => {
      createPresignedPostStub.rejects(new Error('S3 Error'));

      try {
        await S3HelperService.generatePresignedPost({
          fileName: 'test.pdf',
          contentType: 'application/pdf',
        });
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal('S3 Error');
      }
    });
  });

  describe('generatePresignedDownloadUrl', () => {
    it('should generate a presigned download URL successfully', async () => {
      sinon
        .stub(S3HelperService, 'generatePresignedDownloadUrl')
        .resolves(mockS3Response.presignedUrl);

      const result = await S3HelperService.generatePresignedDownloadUrl(
        'test-file-key',
        'test.pdf',
      );

      expect(result).to.equal(mockS3Response.presignedUrl);
    });

    it('should handle errors when generating download URL', async () => {
      sinon
        .stub(S3HelperService, 'generatePresignedDownloadUrl')
        .rejects(new Error('S3 Error'));

      try {
        await S3HelperService.generatePresignedDownloadUrl(
          'test-file-key',
          'test.pdf',
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal('S3 Error');
      }
    });
  });

  describe('deleteFile', () => {
    it('should delete a file from S3', async () => {
      s3ClientStub.send.resolves({});
      await s3Service.deleteFile('file-key');
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(DeleteObjectCommand);
    });
  });

  describe('getFileMetadata', () => {
    it('should get file metadata', async () => {
      s3ClientStub.send.resolves({ Metadata: { foo: 'bar' } });
      const meta = await s3Service.getFileMetadata('file-key');
      expect(meta).to.deep.equal({ foo: 'bar' });
    });
  });

  describe('extractMetadata', () => {
    it('should extract required metadata', () => {
      const meta = { a: '1', b: '2' };
      const result = s3Service.extractMetadata(['a', 'b'], meta);
      expect(result).to.deep.equal(meta);
    });
    it('should throw if required key missing', () => {
      expect(() => s3Service.extractMetadata(['a', 'c'], { a: '1', b: '2' })).to.throw('c is not available on meta-data');
    });
  });

  describe('uploadProjectLogsToS3', () => {
    it('should upload logs as JSON', async () => {
      s3ClientStub.send.resolves({});
      const fileKey = await s3Service.uploadProjectLogsToS3(1, 2, { foo: 'bar' });
      expect(fileKey).to.include('/projects/2/');
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(PutObjectCommand);
      expect((command.input as import('@aws-sdk/client-s3').PutObjectCommandInput).ContentType).to.equal('application/json');
    });

    it('should throw error if upload fails', async () => {
      s3ClientStub.send.rejects(new Error('fail'));
      try {
        await s3Service.uploadProjectLogsToS3(1, 2, { foo: 'bar' });
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Failed to upload JSON to S3');
      }
    });
  });

  describe('getTheFileStream', () => {
    it('should return a readable stream', async () => {
      const mockStream = { on: sinon.stub() } as unknown as NodeJS.ReadableStream;
      s3ClientStub.send.resolves({ Body: mockStream });
      const result = await s3Service.getTheFileStream('file-key');
      expect(result).to.equal(mockStream);
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(GetObjectCommand);
    });

    it('should throw if S3 fails', async () => {
      s3ClientStub.send.rejects(new Error('fail'));
      try {
        await s3Service.getTheFileStream('file-key');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('fail');
      }
    });
  });

  describe('getObject', () => {
    it('should fetch object and return Buffer', async () => {
      const mockByteArray = new Uint8Array([65, 66, 67]);
      const mockBody = { transformToByteArray: sinon.stub().resolves(mockByteArray) };
      s3ClientStub.send.resolves({ Body: mockBody });
      const buf = await s3Service.getObject('file-key');
      expect(buf).to.be.instanceOf(Buffer);
      expect(buf.toString()).to.equal('ABC');
    });

    it('should throw if response body is empty', async () => {
      s3ClientStub.send.resolves({});
      try {
        await s3Service.getObject('file-key');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to fetch object from S3');
      }
    });

    it('should throw if transformToByteArray fails', async () => {
      const mockBody = { transformToByteArray: sinon.stub().rejects(new Error('fail')) };
      s3ClientStub.send.resolves({ Body: mockBody });
      try {
        await s3Service.getObject('file-key');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to fetch object from S3');
      }
    });

    it('should throw if S3 send fails', async () => {
      s3ClientStub.send.rejects(new Error('fail'));
      try {
        await s3Service.getObject('file-key');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to fetch object from S3');
      }
    });
  });

  describe('putObject', () => {
    it('should upload string to S3', async () => {
      s3ClientStub.send.resolves({});
      const key = await s3Service.putObject('file-key', 'abc', { foo: 'bar' }, 'text/plain');
      expect(key).to.equal('file-key');
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(PutObjectCommand);
      expect((command.input as import('@aws-sdk/client-s3').PutObjectCommandInput).ContentType).to.equal('text/plain');
      expect((command.input as import('@aws-sdk/client-s3').PutObjectCommandInput).Metadata).to.deep.equal({ foo: 'bar' });
    });

    it('should upload Buffer to S3', async () => {
      s3ClientStub.send.resolves({});
      const key = await s3Service.putObject('file-key', Buffer.from('abc'), undefined, 'application/octet-stream');
      expect(key).to.equal('file-key');
      expect(s3ClientStub.send.calledOnce).to.be.true;
    });

    it('should throw if upload fails', async () => {
      s3ClientStub.send.rejects(new Error('fail'));
      try {
        await s3Service.putObject('file-key', 'abc');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to upload object to S3');
      }
    });
  });

  describe('copyObject', () => {
    it('should copy object with preserveMetadata true', async () => {
      s3ClientStub.send.resolves({});
      const dest = await s3Service.copyObject('src', 'dest', true);
      expect(dest).to.equal('dest');
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(CopyObjectCommand);
      expect((command.input as import('@aws-sdk/client-s3').CopyObjectCommandInput).MetadataDirective).to.equal('COPY');
    });

    it('should copy object with preserveMetadata false', async () => {
      s3ClientStub.send.resolves({});
      const dest = await s3Service.copyObject('src', 'dest', false);
      expect(dest).to.equal('dest');
      expect(s3ClientStub.send.calledOnce).to.be.true;
      const command = s3ClientStub.send.getCall(0).args[0];
      expect((command.input as import('@aws-sdk/client-s3').CopyObjectCommandInput).MetadataDirective).to.equal('REPLACE');
    });

    it('should throw if copy fails', async () => {
      s3ClientStub.send.rejects(new Error('fail'));
      try {
        await s3Service.copyObject('src', 'dest');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to copy object in S3');
      }
    });
  });
});
