import { expect } from 'chai';
import sinon from 'sinon';

import cronService from '../../services/cron.service';
import * as transactionService from '../../services/transaction.service';
import { logger } from '../../utils';
import s3HelperService from '../../services/s3-helper.service';
import * as sharpUtils from '../../utils/sharp.utils';
import * as commonUtils from '../../utils/common.utils';
import { BasicStatus } from '../../enums';
import { TransactionStatus } from '../../enums/transaction-status.enum';

describe('Cron Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    // Stub logger to prevent actual logging in tests
    sandbox.stub(logger, 'info');
    sandbox.stub(logger, 'debug');
    sandbox.stub(logger, 'error');

    dummyContext = {}; // Add any context properties if needed for your service
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('processThumbnailTransactions', () => {
    it('should process pending transaction successfully', async () => {
      const mockTransaction = {
        dataValues: {
          uuid: 'test-uuid',
          parameters: {
            assetPath: 'path/to/image.jpg',
            tenantId: 123,
            projectId: 456,
          },
        },
        update: sandbox.stub().resolves(),
      };

      sandbox
        .stub(transactionService, 'getPendingImageThumbnailTransactions')
        .resolves(mockTransaction as any);

      const generateStub = sandbox
        .stub(cronService, 'generateAndUploadThumbnail')
        .resolves();

      await cronService.processThumbnailTransactions(dummyContext);

      expect(generateStub.calledOnce).to.be.true;
      expect(generateStub.firstCall.args[0]).to.equal(mockTransaction);
      expect(generateStub.firstCall.args[1]).to.equal(dummyContext);
    });

    it('should handle case when no pending transactions are found', async () => {
      sandbox
        .stub(transactionService, 'getPendingImageThumbnailTransactions')
        .resolves(null as unknown as undefined);
      await cronService.processThumbnailTransactions(dummyContext);
      const loggerInfoStub = logger.info as sinon.SinonStub;
      expect(
        loggerInfoStub.calledWith(
          '[Cron Service] No pending transactions found',
        ),
      ).to.be.true;
    });

    it('should throw error when transaction processing fails', async () => {
      const error = new Error('Transaction service error');
      sandbox
        .stub(transactionService, 'getPendingImageThumbnailTransactions')
        .rejects(error);

      try {
        await cronService.processThumbnailTransactions(dummyContext);
        expect.fail('Expected error was not thrown');
      } catch (err) {
        expect((err as Error).message).to.equal('Failed to generate thumbnail');
      }
    });
  });

  describe('processTenantMigrateTransactions', () => {
    let sqsHelperStub: any;
    let execAsyncStub: any;
    let originalEnv: any;

    beforeEach(() => {
      originalEnv = { ...process.env };
      process.env.TENANT_PROVISIONED_SQS_URL = 'mock-queue-url';
      sqsHelperStub = {
        receiveMessages: sandbox.stub(),
        deleteMessage: sandbox.stub().resolves(),
      };
      execAsyncStub = sandbox.stub().resolves({ stdout: 'ok', stderr: '' });
      // Fix: promisify should return a function, not a stub object
      sandbox.stub(require('util'), 'promisify').returns(() => execAsyncStub());
      sandbox.stub(
        require('../../services/sqs-helper.service').default.prototype,
        'receiveMessages',
      ).value(sqsHelperStub.receiveMessages);
      sandbox.stub(
        require('../../services/sqs-helper.service').default.prototype,
        'deleteMessage',
      ).value(sqsHelperStub.deleteMessage);
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should process tenant migrate transactions and run migration', async () => {
      const tenant = { key: 'tenant1' };
      const message = {
        sqsMessage: { ReceiptHandle: 'handle' },
        records: [tenant],
      };
      sqsHelperStub.receiveMessages.returns((async function* () {
        yield message;
      })());
      const cronService = require('../../services/cron.service').default;
      await cronService.processTenantMigrateTransactions();
      // Should call execAsync and deleteMessage
      sinon.assert.calledOnce(execAsyncStub);
      sinon.assert.calledOnce(sqsHelperStub.deleteMessage);
    });

    it('should skip tenant with no key', async () => {
      const tenant = {};
      const message = {
        sqsMessage: { ReceiptHandle: 'handle' },
        records: [tenant],
      };
      sqsHelperStub.receiveMessages.returns((async function* () {
        yield message;
      })());
      const cronService = require('../../services/cron.service').default;
      await cronService.processTenantMigrateTransactions();
      // Should not call execAsync for tenant with no key
      sinon.assert.notCalled(execAsyncStub);
    });

    it('should log warning if migration script has stderr', async () => {
      const tenant = { key: 'tenant1' };
      const message = {
        sqsMessage: { ReceiptHandle: 'handle' },
        records: [tenant],
      };
      execAsyncStub.resolves({ stdout: 'ok', stderr: 'warn' });
      sqsHelperStub.receiveMessages.returns((async function* () {
        yield message;
      })());
      const cronService = require('../../services/cron.service').default;
      await cronService.processTenantMigrateTransactions();
      // Should call execAsync and deleteMessage
      sinon.assert.calledOnce(execAsyncStub);
      sinon.assert.calledOnce(sqsHelperStub.deleteMessage);
    });

    it('should throw error if migration fails', async () => {
      const tenant = { key: 'tenant1' };
      const message = {
        sqsMessage: { ReceiptHandle: 'handle' },
        records: [tenant],
      };
      execAsyncStub.rejects({ stderr: 'fail' });
      sqsHelperStub.receiveMessages.returns((async function* () {
        yield message;
      })());
      const cronService = require('../../services/cron.service').default;
      try {
        await cronService.processTenantMigrateTransactions();
        // Should not reach here
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Failed to execute migration script');
      }
    });
  });

  describe('generateAndUploadThumbnail', () => {
    let transaction: any;
    beforeEach(() => {
      transaction = {
        dataValues: {
          uuid: 'uuid',
          parameters: {
            assetPath: 'asset.jpg',
            tenantId: 1,
            projectId: 2,
          },
        },
        update: sandbox.stub().resolves(),
      };
    });

    it('should fail if assetPath is missing', async () => {
      transaction.dataValues.parameters.assetPath = undefined;
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.include(
        'Missing required parameters',
      );
    });

    it('should fail if tenantId is missing', async () => {
      transaction.dataValues.parameters.tenantId = undefined;
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.include(
        'Missing required parameters',
      );
    });

    it('should fail if projectId is missing', async () => {
      transaction.dataValues.parameters.projectId = undefined;
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.include(
        'Missing required parameters',
      );
    });

    it('should fail if file is not found', async () => {
      sandbox.stub(require('../../services/s3-helper.service').default, 'getObject').resolves(undefined);
      sandbox.stub(require('../../services/s3-helper.service').default, 'copyObject').resolves();
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.equal('File not found');
    });

    it('should fail if thumbnails are not generated', async () => {
      sandbox.stub(require('../../services/s3-helper.service').default, 'getObject').resolves(Buffer.from('file'));
      sandbox.stub(require('../../services/s3-helper.service').default, 'copyObject').resolves();
      sandbox.stub(require('../../utils/sharp.utils'), 'generateThumbnails').resolves([]);
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.equal('Failed to generate thumbnails');
    });

    it('should fail if upload fails', async () => {
      sandbox.stub(require('../../services/s3-helper.service').default, 'getObject').resolves(Buffer.from('file'));
      sandbox.stub(require('../../services/s3-helper.service').default, 'copyObject').resolves();
      sandbox.stub(require('../../utils/sharp.utils'), 'generateThumbnails').resolves([
        { size: '126px-126px', data: Buffer.from('thumb'), format: 'image/jpeg', width: 126, height: 126 },
      ]);
      sandbox.stub(require('../../services/s3-helper.service').default, 'putObject').rejects(new Error('upload failed'));
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.calledOnce(transaction.update);
      expect(transaction.update.firstCall.args[0].status).to.equal(TransactionStatus.FAILED);
      expect(transaction.update.firstCall.args[0].result.error).to.include('Failed to upload thumbnails');
    });

    it('should complete transaction and delete file on success', async () => {
      sandbox.stub(require('../../services/s3-helper.service').default, 'getObject').resolves(Buffer.from('file'));
      sandbox.stub(require('../../services/s3-helper.service').default, 'copyObject').resolves();
      sandbox.stub(require('../../utils/sharp.utils'), 'generateThumbnails').resolves([
        { size: '126px-126px', data: Buffer.from('thumb'), format: 'image/jpeg', width: 126, height: 126 },
      ]);
      sandbox.stub(require('../../services/s3-helper.service').default, 'putObject').resolves();
      sandbox.stub(require('../../services/s3-helper.service').default, 'deleteFile').resolves();
      const cronService = require('../../services/cron.service').default;
      await cronService.generateAndUploadThumbnail(transaction, {});
      sinon.assert.called(transaction.update);
      sinon.assert.called(require('../../services/s3-helper.service').default.deleteFile);
    });
  });

  describe('extractItemMasterDataByKey', () => {
    it('should log and call bulkCreate with correct data', async () => {
      const vesselMasterData = {
        miscCurrencys: [
          { id: 1, value: 'INR' },
          { id: 2, value: 'USD' },
        ],
      };
      const repositoryClass = {
        model: {
          bulkCreate: sandbox.stub().resolves(),
        },
      };
      const cronService = require('../../services/cron.service').default;
      await cronService.extractItemMasterDataByKey(
        vesselMasterData,
        'miscCurrencys',
        repositoryClass,
        {},
      );
      sinon.assert.calledOnce(repositoryClass.model.bulkCreate);
      const args = repositoryClass.model.bulkCreate.firstCall.args[0];
      // Fix: use .some to check for inclusion in array
      expect(args.some((item: any) =>
        item.paris2_id === 1 &&
        item.name === 'INR' &&
        item.status === BasicStatus.ACTIVE &&
        item.deleted === false &&
        item.createdBy === '0000'
      )).to.be.true;
    });

    it('should throw error if bulkCreate fails', async () => {
      const vesselMasterData = {
        miscCurrencys: [
          { id: 1, value: 'INR' },
        ],
      };
      const repositoryClass = {
        model: {
          bulkCreate: sandbox.stub().throws(new Error('fail')),
        },
      };
      const cronService = require('../../services/cron.service').default;
      try {
        await cronService.extractItemMasterDataByKey(
          vesselMasterData,
          'miscCurrencys',
          repositoryClass,
          {},
        );
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Failed to extract miscCurrencys');
      }
    });
  });
});
