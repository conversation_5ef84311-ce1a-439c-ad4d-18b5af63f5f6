import { expect } from 'chai';
import sinon from 'sinon';
import {
  SQSClient,
  ReceiveMessageCommand,
  DeleteMessageCommand,
} from '@aws-sdk/client-sqs';
import SqsHelperService from '../../services/sqs-helper.service';
import { logger } from '../../utils';

describe('SqsHelperService', () => {
  let sandbox: sinon.SinonSandbox;
  let sqsClientStub: sinon.SinonStubbedInstance<SQSClient>;
  let sqsHelper: SqsHelperService;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sqsClientStub = sinon.createStubInstance(SQSClient);
    sqsHelper = new SqsHelperService();
    // Patch the internal SQSClient for the instance
    (sqsHelper as any).sqs = sqsClientStub;
    sandbox.stub(logger, 'info');
    sandbox.stub(logger, 'error');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('receiveMessages', () => {
    it('should yield parsed records from SQS messages (single record)', async () => {
      const tenant = {
        tenantId: 1,
        companyName: 'TestCo',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        email: '<EMAIL>',
        key: 'tenant-key',
      };
      const encodedBody = Buffer.from(JSON.stringify(tenant)).toString('base64');
      sqsClientStub.send.resolves({
        Messages: [
          {
            MessageId: 'msg-1',
            ReceiptHandle: 'handle-1',
            Body: encodedBody,
          },
        ],
      });

      const params = { QueueUrl: 'queue-url' };
      const iterator = sqsHelper.receiveMessages(params);

      const result = await iterator.next();
      expect(result.value.records).to.be.an('array').with.lengthOf(1);
      expect(result.value.records[0]).to.deep.equal(tenant);
      expect(result.value.sqsMessage.MessageId).to.equal('msg-1');
      expect(result.value.sqsMessage.ReceiptHandle).to.equal('handle-1');
      expect(result.done).to.be.false;
    });

    it('should yield parsed records from SQS messages (array of records)', async () => {
      const tenants = [
        { tenantId: 1, key: 't1' },
        { tenantId: 2, key: 't2' },
      ];
      const encodedBody = Buffer.from(JSON.stringify(tenants)).toString('base64');
      sqsClientStub.send.resolves({
        Messages: [
          {
            MessageId: 'msg-2',
            ReceiptHandle: 'handle-2',
            Body: encodedBody,
          },
        ],
      });

      const params = { QueueUrl: 'queue-url' };
      const iterator = sqsHelper.receiveMessages(params);

      const result = await iterator.next();
      expect(result.value.records).to.be.an('array').with.lengthOf(2);
      expect(result.value.records[0]).to.deep.equal(tenants[0]);
      expect(result.value.records[1]).to.deep.equal(tenants[1]);
      expect(result.value.sqsMessage.MessageId).to.equal('msg-2');
      expect(result.value.sqsMessage.ReceiptHandle).to.equal('handle-2');
      expect(result.done).to.be.false;
    });

    it('should skip message if Body is missing', async () => {
      sqsClientStub.send.resolves({
        Messages: [
          {
            MessageId: 'msg-3',
            ReceiptHandle: 'handle-3',
            // No Body
          },
        ],
      });

      const params = { QueueUrl: 'queue-url' };
      const iterator = sqsHelper.receiveMessages(params);

      const result = await iterator.next();
      expect(result.done).to.be.true;
    });

    it('should log error if JSON parse fails', async () => {
      const invalidBody = Buffer.from('not-json').toString('base64');
      sqsClientStub.send.resolves({
        Messages: [
          {
            MessageId: 'msg-4',
            ReceiptHandle: 'handle-4',
            Body: invalidBody,
          },
        ],
      });

      const params = { QueueUrl: 'queue-url' };
      const iterator = sqsHelper.receiveMessages(params);

      const result = await iterator.next();
      expect(result.done).to.be.true;
      sinon.assert.calledOnce(logger.error as sinon.SinonStub);
    });
  });

  describe('deleteMessage', () => {
    it('should delete a message from SQS', async () => {
      sqsClientStub.send.resolves({});
      const params = { QueueUrl: 'queue-url', ReceiptHandle: 'handle-1' };
      await sqsHelper.deleteMessage(params);
      expect(sqsClientStub.send.calledOnce).to.be.true;
      const command = sqsClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(DeleteMessageCommand);
      sinon.assert.calledOnce(logger.info as sinon.SinonStub);
    });

    it('should log error if delete fails', async () => {
      sqsClientStub.send.rejects(new Error('delete error'));
      const params = { QueueUrl: 'queue-url', ReceiptHandle: 'handle-2' };
      await sqsHelper.deleteMessage(params);
      sinon.assert.calledOnce(logger.error as sinon.SinonStub);
    });
  });
});
