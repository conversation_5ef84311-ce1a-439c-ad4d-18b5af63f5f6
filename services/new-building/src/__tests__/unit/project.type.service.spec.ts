import {expect} from 'chai';
import sinon from 'sinon';
import {getAllProjectTypes} from '../../services/project.type.service';
import {projectTypeData} from '../data/project.type.data';

describe('Project Type Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        projectTypeRepository: {
          findAll: sandbox.stub(),
        },
      },
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getAllProjectTypes', () => {
    it('should return a list of project types', async () => {
      dummyContext.repositories.projectTypeRepository.findAll.resolves(
        projectTypeData,
      );

      const result = await getAllProjectTypes(dummyContext);

      expect(result).to.deep.equal(projectTypeData);
      expect(dummyContext.repositories.projectTypeRepository.findAll.calledOnce)
        .to.be.true;
    });

    it('should throw an error if the repository method fails', async () => {
      dummyContext.repositories.projectTypeRepository.findAll.rejects(
        new Error('Database error'),
      );

      try {
        await getAllProjectTypes(dummyContext);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.an('error');
        expect((error as Error).message).to.equal('Database error');
      }
    });
  });
});
