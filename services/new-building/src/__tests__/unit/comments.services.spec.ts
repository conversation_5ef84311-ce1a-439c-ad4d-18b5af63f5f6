import { expect } from 'chai';
import sinon from 'sinon';
import {
  createComment,
  getAllComments,
  updateComment,
  resolveAllComments,
} from '../../services/comments.services';
import { CustomError } from '../../utils';
import { CommentType, CommentStatus, ProjectDrawingStatus } from '../../enums';
import { userAllPermission } from '../data/user.data';
import { inputCommentData } from '../data/comments.data';
import { IProjectDrawingCommentInstance } from '../../models';
import { CreationAttributes } from 'sequelize';

describe('Comments Service', () => {
  let dummyContext: any;
  let transactionStub: any;

  beforeEach(() => {
    transactionStub = {
      commit: sinon.stub(),
      rollback: sinon.stub(),
    };

    dummyContext = {
      repositories: {
        drawingListCommentRepository: {
          create: sinon.stub(),
          findAll: sinon.stub(),
          findById: sinon.stub(),
          update: sinon.stub(),
          updateAll: sinon.stub(),
        },
        inspectionCommentRepository: {
          create: sinon.stub(),
          findAll: sinon.stub(),
          findById: sinon.stub(),
          update: sinon.stub(),
          updateAll: sinon.stub(),
        },
        drawingListRepository: {
          findOne: sinon.stub(),
        },
        inspectionRepository: {
          findOne: sinon.stub(),
        },
      },
      models: {
        ProjectDrawing: {
          findOne: sinon.stub(),
        },
      },
      sequelize: {
        transaction: sinon.stub().resolves(transactionStub),
      },
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('createComment', () => {
    const drawingData = {
      ...inputCommentData,
      commentType: CommentType.DRAWING as const, // Use const assertion
      drawingListId: 1,
      comments: 'Test comment',
      status: CommentStatus.OPEN,
    };

    it('should create a drawing comment successfully', async () => {
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.models.ProjectDrawing.findOne.resolves(null);

      const expectedResponse = {
        id: 10,
        ...drawingData,
        createdBy: userAllPermission.user_id,
        createdAt: new Date(),
      };

      dummyContext.repositories.drawingListCommentRepository.create.resolves(
        expectedResponse,
      );

      const result = await createComment(
        drawingData,
        userAllPermission,
        dummyContext,
      );

      expect(result).to.deep.equal(expectedResponse);
      expect(
        dummyContext.repositories.drawingListCommentRepository.create
          .calledOnce,
      ).to.be.true;
    });

    it('should throw an error if drawing is not found', async () => {
      dummyContext.repositories.drawingListRepository.findOne.resolves(null);

      try {
        await createComment(drawingData, userAllPermission, dummyContext);
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Drawing not found');
      }
    });

    it('should throw an error if drawing is approved', async () => {
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.models.ProjectDrawing.findOne.resolves({
        status: ProjectDrawingStatus.APPROVED,
      });

      try {
        await createComment(drawingData, userAllPermission, dummyContext);
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal("You can't comment on approved drawing");
      }
    });

    it('should create an inspection comment successfully', async () => {
      const inspectionData = {
        ...inputCommentData,
        commentType: CommentType.INSPECTION as const, // Use const assertion
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.inspectionCommentRepository.create.resolves({
        id: 20,
        ...inspectionData,
      });

      const result = await createComment(
        inspectionData,
        userAllPermission,
        dummyContext,
      );

      expect(
        dummyContext.repositories.inspectionCommentRepository.create.calledOnce,
      ).to.be.true;
      expect(result).to.deep.equal({ id: 20, ...inspectionData });
    });

    it('should throw an error if inspection is not found', async () => {
      const inspectionData = {
        ...inputCommentData,
        commentType: CommentType.INSPECTION as const, // Use const assertion
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionRepository.findOne.resolves(null);

      try {
        await createComment(inspectionData, userAllPermission, dummyContext);
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Inspection not found');
      }
    });
  });

  describe('getAllComments', () => {
    it('should retrieve all drawing comments successfully', async () => {
      const query = {
        commentType: CommentType.DRAWING,
        drawingListId: 1,
      };

      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.findAll.resolves([
        inputCommentData,
      ]);

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal([inputCommentData]);
    });

    it('should retrieve all inspection comments successfully', async () => {
      const query = {
        commentType: CommentType.INSPECTION,
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.inspectionCommentRepository.findAll.resolves([
        inputCommentData,
      ]);

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal([inputCommentData]);
    });

    it('should throw an error if repository call fails', async () => {
      const query = {
        commentType: CommentType.DRAWING,
        drawingListId: 1,
      };
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.findAll.rejects(
        new Error('DB failure'),
      );

      try {
        await getAllComments(query, dummyContext);
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('DB failure');
      }
    });

    it('should retrieve all drawing comments with status filter', async () => {
      const query = {
        commentType: CommentType.DRAWING,
        drawingListId: 1,
        status: CommentStatus.OPEN,
      };

      const mockComments = [
        {
          id: 1,
          drawingListId: 1,
          status: CommentStatus.OPEN,
          comment: 'Test comment',
        },
      ];

      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.findAll.resolves(
        mockComments,
      );

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal(mockComments);
    });

    it('should retrieve all inspection comments with status filter', async () => {
      const query = {
        commentType: CommentType.INSPECTION,
        inspectionId: 2,
        status: CommentStatus.CLOSED,
      };

      const mockComments = [
        {
          id: 2,
          inspectionId: 2,
          status: CommentStatus.CLOSED,
          comment: 'Inspection comment',
        },
      ];
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.inspectionCommentRepository.findAll.resolves(
        mockComments,
      );

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal(mockComments);
    });

    it('should retrieve all drawing comments without status filter', async () => {
      const query = {
        commentType: CommentType.DRAWING,
        drawingListId: 1,
      };

      const mockComments = [
        {
          id: 1,
          drawingListId: 1,
          status: CommentStatus.OPEN,
          comment: 'Test comment',
        },
      ];

      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.findAll.resolves(
        mockComments,
      );

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal(mockComments);
    });

    it('should retrieve all inspection comments without status filter', async () => {
      const query = {
        commentType: CommentType.INSPECTION,
        inspectionId: 2,
      };

      const mockComments = [
        {
          id: 2,
          inspectionId: 2,
          status: CommentStatus.OPEN,
          comment: 'Inspection comment',
        },
      ];
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.inspectionCommentRepository.findAll.resolves(
        mockComments,
      );

      const result = await getAllComments(query, dummyContext);

      expect(result).to.deep.equal(mockComments);
    });
  });

  describe('updateComment', () => {
    beforeEach(() => {
      dummyContext.sequelize.transaction.resolves(transactionStub);
    });

    it('should update a comment successfully', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: userAllPermission.user_id,
        drawingListId: 1,
      };

      dummyContext.repositories.drawingListCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.update.resolves();

      const result = await updateComment(
        1,
        { status: CommentStatus.CLOSED, commentType: CommentType.DRAWING } as any,
        userAllPermission,
        dummyContext,
      );

      expect(
        dummyContext.repositories.drawingListCommentRepository.update
          .calledOnce,
      ).to.be.true;
      expect(transactionStub.commit.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        dataValues: commentData,
      });
    });

    it('should throw error if drawingListId is missing in comment data', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: userAllPermission.user_id,
      };

      dummyContext.repositories.drawingListCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.drawingListCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          { status: CommentStatus.CLOSED, commentType: CommentType.DRAWING } as any,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('drawingListId is missing in comment data');
      }
    });

    it('should throw error if Drawing not found', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: userAllPermission.user_id,
        drawingListId: 1,
      };

      dummyContext.repositories.drawingListCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.drawingListRepository.findOne.resolves();
      dummyContext.repositories.drawingListCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          { status: CommentStatus.CLOSED, commentType: CommentType.DRAWING } as any,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Drawing not found');
      }
    });

    it('should throw an error if comment is not found', async () => {
      dummyContext.repositories.drawingListCommentRepository.findById.resolves(
        null,
      );

      try {
        await updateComment(
          1,
          {
            status: CommentStatus.CLOSED,
            commentType: CommentType.DRAWING,
          } as any,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Comment not found');
      }
    });

    it('should throw an error if commentType is invalid', async () => {
      const invalidComment = {
        commentType: 'INVALID_TYPE',
      };

      try {
        await updateComment(
          1,
          invalidComment as any,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err.message).to.equal('Comment not found');
      }
    });

    it('should update an inspection comment successfully', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: 'user123',
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.inspectionRepository.findOne.resolves({
        id: 1,
      });
      dummyContext.repositories.inspectionCommentRepository.update.resolves();

      const result = await updateComment(
        1,
        {
          commentType: CommentType.INSPECTION,
          status: CommentStatus.CLOSED,
        } as any,
        userAllPermission,
        dummyContext,
      );

      expect(
        dummyContext.repositories.inspectionCommentRepository.update.calledOnce,
      ).to.be.true;
      expect(transactionStub.commit.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        dataValues: commentData,
      });
    });

    it('should throw error if inspectionId is missing in comment data', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: 'user123',
      };

      dummyContext.repositories.inspectionCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.inspectionRepository.findOne.resolves({
        id: 1,
      });
      dummyContext.repositories.inspectionCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          {
            commentType: CommentType.INSPECTION,
            status: CommentStatus.CLOSED,
          } as any,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('inspectionId is missing in comment data');
      }
    });

    it('should throw error if Inspection not found', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: 'user123',
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.inspectionRepository.findOne.resolves();
      dummyContext.repositories.inspectionCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          {
            commentType: CommentType.INSPECTION,
            status: CommentStatus.CLOSED,
          } as any,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Inspection not found');
      }
    });

    it('should throw error if Comment is already closed', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.CLOSED,
        createdBy: 'user123',
        inspectionId: 1,
      };

      dummyContext.repositories.inspectionCommentRepository.findById.resolves({
        dataValues: commentData,
      });
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });
      dummyContext.repositories.inspectionCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          {
            commentType: CommentType.INSPECTION,
            status: CommentStatus.CLOSED,
          } as any,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal(`Comment with ID ${commentData.id} is already closed`);
      }
    });

    it('throw error if user do not have permissions to perform action.', async () => {
      const commentData = {
        id: 1,
        status: CommentStatus.OPEN,
        createdBy: 'user123',
        inspectionId: 1,
      };
      const user = { user_id: 'user123' } as any;

      dummyContext.repositories.inspectionCommentRepository.findById.resolves({
        dataValues: commentData,
      });

      dummyContext.repositories.inspectionRepository.findOne.resolves({
        id: 1,
      });

      dummyContext.repositories.inspectionCommentRepository.update.resolves();

      try {
        await updateComment(
          1,
          {
            commentType: CommentType.INSPECTION,
            status: CommentStatus.CLOSED,
          } as any,
          user,
          dummyContext,
        );
      } catch (error: any) {
        expect(error).to.be.instanceOf(CustomError);
        expect(error.message).to.equal(
          'Forbidden: You do not have the required permissions to perform this action.',
        );
        expect((error as CustomError).code).to.equal(403);
      }
    });

    it('should throw an error if inspection comment is not found', async () => {
      const user = { user_id: 'user123' };

      dummyContext.repositories.inspectionCommentRepository.findById.resolves(
        null,
      );

      try {
        await updateComment(
          1,
          {
            commentType: CommentType.INSPECTION,
            status: CommentStatus.CLOSED,
          } as any,
          user as any,
          dummyContext,
        );
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Comment not found');
      }
    });
  });

  describe('resolveAllComments', () => {
    beforeEach(() => {
      dummyContext.sequelize.transaction.resolves(transactionStub);
    });

    it('should resolve all comments successfully', async () => {
      // Mock the drawing validation check
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.drawingListCommentRepository.updateAll.resolves(
        [2],
      );

      const result = await resolveAllComments(
        1,
        CommentType.DRAWING,
        userAllPermission,
        dummyContext,
      );

      expect(result).to.deep.equal({
        error: false,
        message: 'Comment has been resolved successfully',
      });
    });

    it('should throw error if Drawing not found', async () => {
      // Mock the drawing validation check
      dummyContext.repositories.drawingListRepository.findOne.resolves();

      try {
        resolveAllComments(
          1,
          CommentType.DRAWING,
          userAllPermission,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Drawing not found');
      }
    });

    it('should rollback the transaction if an error occurs', async () => {
      // Mock the drawing validation check
      dummyContext.repositories.drawingListRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.drawingListCommentRepository.updateAll.rejects(
        new Error('DB error'),
      );

      try {
        await resolveAllComments(
          1,
          CommentType.DRAWING,
          userAllPermission,
          dummyContext,
        );
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(transactionStub.rollback.calledOnce).to.be.true;
        expect(err.message).to.equal('DB error');
      }
    });

    it('should resolve all inspection comments successfully', async () => {
      const user = { user_id: 'user123' } as any;
      const entityId = 1;

      // Mock the inspection validation check
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.inspectionCommentRepository.updateAll.resolves([
        2,
      ]);

      const result = await resolveAllComments(
        entityId,
        CommentType.INSPECTION,
        user,
        dummyContext,
      );

      expect(result).to.deep.equal({
        error: false,
        message: 'Comment has been resolved successfully',
      });
      expect(transactionStub.commit.calledOnce).to.be.true;
    });

    it('should throw error if Inspection not found', async () => {
      const user = { user_id: 'user123' } as any;
      const entityId = 1;

      // Mock the inspection validation check
      dummyContext.repositories.inspectionRepository.findOne.resolves();

      try {
        resolveAllComments(
          entityId,
          CommentType.INSPECTION,
          user,
          dummyContext,
        );
      } catch (err: any) {
        expect(err).to.be.instanceOf(CustomError);
        expect(err.message).to.equal('Inspection not found');
      }
    });

    it('should resolve only user-specific inspection comments if user lacks COMMENT_All permission', async () => {
      const user = { user_id: 'user123' } as any;
      const entityId = 1;

      // Mock the inspection validation check
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.inspectionCommentRepository.updateAll.resolves([
        1,
      ]);

      const result = await resolveAllComments(
        entityId,
        CommentType.INSPECTION,
        user,
        dummyContext,
      );

      expect(result).to.deep.equal({
        error: false,
        message: 'Comment has been resolved successfully',
      });
      expect(transactionStub.commit.calledOnce).to.be.true;
    });

    it('should return an error message if no pending inspection comments are found', async () => {
      const user = { user_id: 'user123' } as any;
      const entityId = 1;

      // Mock the inspection validation check
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.inspectionCommentRepository.updateAll.resolves([
        0,
      ]);

      const result = await resolveAllComments(
        entityId,
        CommentType.INSPECTION,
        user,
        dummyContext,
      );

      expect(result).to.deep.equal({
        error: true,
        message: 'No pending comments to close',
      });
      expect(transactionStub.commit.calledOnce).to.be.true;
    });

    it('should rollback the transaction if an error occurs', async () => {
      const user = { user_id: 'user123' } as any;
      const entityId = 1;

      // Mock the inspection validation check
      dummyContext.repositories.inspectionRepository.findOne.resolves({ id: 1 });

      dummyContext.repositories.inspectionCommentRepository.updateAll.rejects(
        new Error('DB error'),
      );

      try {
        await resolveAllComments(
          entityId,
          CommentType.INSPECTION,
          user,
          dummyContext,
        );
        throw new Error('Expected error not thrown');
      } catch (err: any) {
        expect(transactionStub.rollback.calledOnce).to.be.true;
        expect(err.message).to.equal('DB error');
      }
    });
  });
});
