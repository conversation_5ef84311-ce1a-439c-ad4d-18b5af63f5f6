import {expect} from 'chai';
import sinon from 'sinon';
import {logger, CustomError} from '../../utils';
import * as assetService from '../../services/assets.services';

describe('modifyExistingAsset', () => {
  const id = 1;
  const changedFields = {name: 'Updated Name'};

  let dummyContext: any;

  beforeEach(() => {
    dummyContext = {
      repositories: {
        projectRepository: {update: sinon.stub().resolves()},
        hullRepository: {update: sinon.stub().resolves()},
      },
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should update a project asset successfully', async () => {
    const updateStub = dummyContext.repositories.projectRepository.update;
    const result = await assetService.modifyExistingAsset(
      'project',
      id,
      changedFields,
      dummyContext,
    );

    expect(updateStub.calledOnceWithExactly(id, changedFields)).to.be.true;
    expect(result).to.deep.equal({
      error: false,
      message: 'Asset updated successfully',
    });
  });

  it('should update a hull asset successfully', async () => {
    const updateStub = dummyContext.repositories.hullRepository.update;
    const result = await assetService.modifyExistingAsset(
      'hull',
      id,
      changedFields,
      dummyContext,
    );

    expect(updateStub.calledOnceWithExactly(id, changedFields)).to.be.true;
    expect(result).to.deep.equal({
      error: false,
      message: 'Asset updated successfully',
    });
  });

  it('should log an error and return success if type is unknown', async () => {
    const loggerStub = sinon.stub(logger, 'error');
    const result = await assetService.modifyExistingAsset(
      'unknown',
      id,
      changedFields,
      dummyContext,
    );

    expect(loggerStub.called).to.be.true;
    expect(result).to.deep.equal({
      error: false,
      message: 'Asset updated successfully',
    });
  });

  it('should throw CustomError on exception', async () => {
    dummyContext.repositories.projectRepository.update.throws(
      new Error('DB error'),
    );
    const loggerStub = sinon.stub(logger, 'error');

    try {
      await assetService.modifyExistingAsset(
        'project',
        id,
        changedFields,
        dummyContext,
      );
      throw new Error('Expected error was not thrown');
    } catch (err) {
      expect(loggerStub.called).to.be.true;
      expect(err).to.be.instanceOf(CustomError);
      expect((err as CustomError).message).to.equal('Internal Server Error');
      expect((err as CustomError).code).to.equal(500);
    }
  });
});
