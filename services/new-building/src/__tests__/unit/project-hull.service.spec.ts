import {expect} from 'chai';
import sinon from 'sinon';
import * as projectHullService from '../../services/project-hull.services';

describe('Project Hull Service', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        projectHullRepository: {
          create: sandbox.stub(),
          findOne: sandbox.stub(),
          update: sandbox.stub(),
        },
      },
      user: {user_id: 1},
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createProjectHull', () => {
    it('should create a new project-hull successfully', async () => {
      const mockProjectHull = {
        id: 1,
        projectId: 1,
        hullId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      dummyContext.repositories.projectHullRepository.create.resolves(
        mockProjectHull,
      );

      const result = await projectHullService.createProjectHull(
        {projectId: 1, hullId: 1} as any,
        dummyContext,
      );

      expect(result).to.deep.equal(mockProjectHull);
    });

    it('should handle errors when creating a project-hull', async () => {
      dummyContext.repositories.projectHullRepository.create.rejects(
        new Error('Service error'),
      );

      try {
        await projectHullService.createProjectHull(
          {projectId: 1, hullId: 1} as any,
          dummyContext,
        );
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal('Service error');
      }
    });
  });

  describe('getProjectHull', () => {
    it('should fetch a project-hull successfully', async () => {
      const mockProjectHull = {
        id: 1,
        projectId: 1,
        hullId: 1,
      };

      dummyContext.repositories.projectHullRepository.findOne.resolves(
        mockProjectHull,
      );

      const result = await projectHullService.getProjectHull(
        {projectId: 1},
        dummyContext,
      );

      expect(result).to.deep.equal(mockProjectHull);
    });

    it('should return null if no project-hull is found', async () => {
      dummyContext.repositories.projectHullRepository.findOne.resolves(null);

      const result = await projectHullService.getProjectHull(
        {projectId: 1},
        dummyContext,
      );

      expect(result).to.be.null;
    });

    it('should handle errors when fetching a project-hull', async () => {
      dummyContext.repositories.projectHullRepository.findOne.rejects(
        new Error('Service error'),
      );

      try {
        await projectHullService.getProjectHull({projectId: 1}, dummyContext);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal('Service error');
      }
    });
  });

  describe('deleteProjectHull', () => {
    it('should delete a project-hull successfully', async () => {
      dummyContext.repositories.projectHullRepository.update.resolves([1]);

      const result = await projectHullService.deleteProjectHull(
        1,
        {user_id: 1} as any,
        dummyContext,
      );

      expect(result).to.deep.equal([1]);
    });

    it('should handle errors when deleting a project-hull', async () => {
      dummyContext.repositories.projectHullRepository.update.rejects(
        new Error('Service error'),
      );

      try {
        await projectHullService.deleteProjectHull(
          1,
          {user_id: 1} as any,
          dummyContext,
        );
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal('Service error');
      }
    });
  });
});
