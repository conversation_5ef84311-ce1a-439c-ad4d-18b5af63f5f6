import { expect } from 'chai';
import sinon from 'sinon';
import { SNSClient, ConfirmSubscriptionCommand } from '@aws-sdk/client-sns';
import SNSHelperService, { SNSHelperService as SNSHelperServiceClass } from '../../services/sns-helper.service';
import { logger } from '../../utils';

describe('SNSHelperService', () => {
  let sandbox: sinon.SinonSandbox;
  let snsClientStub: sinon.SinonStubbedInstance<SNSClient>;
  let snsHelper: SNSHelperServiceClass;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    snsClientStub = sinon.createStubInstance(SNSClient);
    snsHelper = new SNSHelperServiceClass();
    // Patch the internal snsClient for the instance
    (snsHelper as any).snsClient = snsClientStub;
    sandbox.stub(logger, 'info');
    sandbox.stub(logger, 'error');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('confirmSubscription', () => {
    it('should send ConfirmSubscriptionCommand and return response', async () => {
      const fakeResponse = { SubscriptionArn: 'arn:aws:sns:123:sub' };
      snsClientStub.send.resolves(fakeResponse);

      const topicArn = 'arn:aws:sns:region:account:topic';
      const token = 'token123';

      const result = await snsHelper.confirmSubscription(topicArn, token);

      expect(result).to.equal(fakeResponse);
      expect(snsClientStub.send.calledOnce).to.be.true;
      const command = snsClientStub.send.getCall(0).args[0];
      expect(command).to.be.instanceOf(ConfirmSubscriptionCommand);
      expect((command as ConfirmSubscriptionCommand).input.TopicArn).to.equal(topicArn);
      expect((command as ConfirmSubscriptionCommand).input.Token).to.equal(token);
    });

    it('should throw error if send fails', async () => {
      snsClientStub.send.rejects(new Error('SNS error'));
      try {
        await snsHelper.confirmSubscription('arn', 'token');
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('SNS error');
      }
    });
  });

  describe('confirmSnsSubscription', () => {
    it('should return true if UnsubscribeURL is present', async () => {
      const body = { TopicArn: 'arn', Token: 'token', UnsubscribeURL: 'url' };
      const result = await snsHelper.confirmSnsSubscription(body);
      expect(result).to.be.true;
    });

    it('should return false and log info if SubscriptionArn is present', async () => {
      const body = { TopicArn: 'arn', Token: 'token' };
      const fakeResponse = { SubscriptionArn: 'arn:aws:sns:123:sub' };
      sandbox.stub(snsHelper, 'confirmSubscription').resolves(fakeResponse as any);

      const result = await snsHelper.confirmSnsSubscription(body);
      expect(result).to.be.false;
      sinon.assert.calledWithMatch(logger.info as sinon.SinonStub, 'Subscription confirmed successfully:');
    });

    it('should return false and log info if SubscriptionArn is not present', async () => {
      const body = { TopicArn: 'arn', Token: 'token' };
      const fakeResponse = {};
      sandbox.stub(snsHelper, 'confirmSubscription').resolves(fakeResponse as any);

      const result = await snsHelper.confirmSnsSubscription(body);
      expect(result).to.be.false;
      sinon.assert.calledWithMatch(logger.info as sinon.SinonStub, 'Subscription confirmation response:');
    });

    it('should return false and log error if confirmSubscription throws', async () => {
      const body = { TopicArn: 'arn', Token: 'token' };
      sandbox.stub(snsHelper, 'confirmSubscription').rejects(new Error('fail'));
      const result = await snsHelper.confirmSnsSubscription(body);
      expect(result).to.be.false;
      sinon.assert.calledWithMatch(logger.error as sinon.SinonStub, 'Error confirming subscription:');
    });
  });
});
