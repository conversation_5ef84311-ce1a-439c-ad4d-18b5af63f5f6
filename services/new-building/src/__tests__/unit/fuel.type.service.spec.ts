import {expect} from 'chai';
import sinon from 'sinon';
import {getAllFuelTypes} from '../../services/fuel.type.service';
import {fuelTypeData} from '../data/fuel.type.data';

describe('Fuel Type Service', () => {
  let dummyContext: any;

  beforeEach(() => {
    dummyContext = {
      repositories: {
        fuelTypeRepository: {
          findAll: sinon.stub(),
        },
      },
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAllFuelTypes', () => {
    it('should return a list of Fuel types', async () => {
      dummyContext.repositories.fuelTypeRepository.findAll.resolves(
        fuelTypeData,
      );
      const result = await getAllFuelTypes(dummyContext);
      expect(result).to.deep.equal(fuelTypeData);
    });

    it('should throw an error if the repository method fails', async () => {
      dummyContext.repositories.fuelTypeRepository.findAll.rejects(
        new Error('Database error'),
      );
      try {
        await getAllFuelTypes(dummyContext);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.an('error');
        expect((error as Error).message).to.equal('Database error');
      }
    });
  });
});
