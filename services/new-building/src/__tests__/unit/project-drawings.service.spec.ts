import { expect } from 'chai';
import sinon from 'sinon';
import projectDrawingService from '../../services/projects-drawings.service';
import { ProjectDrawingStatus } from '../../enums';
import { CustomError } from '../../utils';
import { userAllPermission } from '../data/user.data';
import { S3HelperService } from '../../services';
import * as resolveAllCommentsModule from '../../services/comments.services';

describe('ProjectDrawingsService', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        drawingListRepository: { findById: sandbox.stub() },
        projectDrawingRepository: {
          create: sandbox.stub(),
          findOne: sandbox.stub(),
          findById: sandbox.stub(),
          findAndCountAll: sandbox.stub(),
          updateAll: sandbox.stub(),
        },
        projectHullRepository: { findAll: sandbox.stub() },
      },
      models: {
        ProjectDrawing: { findOne: sandbox.stub() },
      },
      sequelize: {
        transaction: sandbox.stub().resolves({
          commit: sandbox.stub().resolves(),
          rollback: sandbox.stub().resolves(),
        }),
      },
      user: userAllPermission,
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createProjectDrawings', () => {
    it('should throw if drawingList not found', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves(null);
      try {
        await projectDrawingService.createProjectDrawings(
          1,
          { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1 },
          userAllPermission,
          dummyContext
        );
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('Invalid drawing information shared');
      }
    });

    it('should throw if current drawing is approved', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.resolves({ dataValues: { status: ProjectDrawingStatus.APPROVED, version: 1 } });
      try {
        await projectDrawingService.createProjectDrawings(
          1,
          { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1 },
          userAllPermission,
          dummyContext
        );
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include("can't upload more drawings after approval");
      }
    });

    it('should throw if hulls are not valid', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.resolves(null);
      sandbox.stub(projectDrawingService, 'checkIsValidHullId').resolves(false);
      try {
        await projectDrawingService.createProjectDrawings(
          1,
          { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1, hullNoId: [1, 2] },
          userAllPermission,
          dummyContext
        );
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('Some given hulls are not associated');
      }
      (projectDrawingService.checkIsValidHullId as sinon.SinonStub).restore();
    });

    it('should create drawing and resolve comments if status is APPROVED', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.resolves(null);
      sandbox.stub(projectDrawingService, 'checkIsValidHullId').resolves(true);
      dummyContext.repositories.projectDrawingRepository.create.resolves({ id: 1 });
      // Use sandbox.stub on the imported resolveAllComments directly, not via require().resolveAllComments assignment
      const resolveAllCommentsStub = sandbox.stub(resolveAllCommentsModule, 'resolveAllComments').resolves();
      const result = await projectDrawingService.createProjectDrawings(
        1,
        { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: ProjectDrawingStatus.APPROVED },
        userAllPermission,
        dummyContext
      );
      expect(result).to.deep.equal({ id: 1 });
      resolveAllCommentsStub.restore();
      (projectDrawingService.checkIsValidHullId as sinon.SinonStub).restore();
    });

    it('should create drawing and not resolve comments if status is not APPROVED', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.resolves(null);
      sandbox.stub(projectDrawingService, 'checkIsValidHullId').resolves(true);
      dummyContext.repositories.projectDrawingRepository.create.resolves({ id: 2 });
      // Use sandbox.stub on the imported resolveAllComments directly, not via require().resolveAllComments assignment
      const resolveAllCommentsStub = sandbox.stub(resolveAllCommentsModule, 'resolveAllComments');
      const result = await projectDrawingService.createProjectDrawings(
        1,
        { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1 },
        userAllPermission,
        dummyContext
      );
      expect(result).to.deep.equal({ id: 2 });
      expect(resolveAllCommentsStub.notCalled).to.be.true;
      resolveAllCommentsStub.restore();
      (projectDrawingService.checkIsValidHullId as sinon.SinonStub).restore();
    });

    it('should handle error in catch and throw CustomError', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.throws(new Error('fail'));
      try {
        await projectDrawingService.createProjectDrawings(
          1,
          { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1 },
          userAllPermission,
          dummyContext
        );
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('There is some error in creating project drawings');
      }
    });

    it('should rethrow CustomError in catch', async () => {
      dummyContext.repositories.drawingListRepository.findById.resolves({ dataValues: { projectId: 1, name: 'n' } });
      dummyContext.models.ProjectDrawing.findOne.throws(new CustomError('custom', 400));
      try {
        await projectDrawingService.createProjectDrawings(
          1,
          { dueDate: new Date(), assetName: 'a', assetType: 'b', assetPath: 'c', status: 1 },
          userAllPermission,
          dummyContext
        );
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('custom');
      }
    });
  });

  describe('findDrawingsById', () => {
    it('should throw if not found', async () => {
      dummyContext.repositories.projectDrawingRepository.findById.resolves(null);
      try {
        await projectDrawingService.findDrawingsById(1, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('Drawings not available');
      }
    });

    it('should return drawing with assetPath as url if assetPath exists', async () => {
      dummyContext.repositories.projectDrawingRepository.findById.resolves({
        dataValues: { assetPath: 'p', assetName: 'n' },
      });
      const url = 'http://signed-url';
      sandbox.stub(S3HelperService, 'generatePresignedDownloadUrl').resolves(url);
      const result = await projectDrawingService.findDrawingsById(1, dummyContext);
      expect(result.dataValues.assetPath).to.equal(url);
    });

    it('should return drawing if assetPath does not exist', async () => {
      dummyContext.repositories.projectDrawingRepository.findById.resolves({
        dataValues: {},
      });
      const result = await projectDrawingService.findDrawingsById(1, dummyContext);
      expect(result.dataValues).to.deep.equal({});
    });
  });

  describe('checkIsValidHullId', () => {
    it('should return true if all hulls found', async () => {
      dummyContext.repositories.projectHullRepository.findAll.resolves([{}, {}]);
      const fakeTransaction = {
        commit: sinon.stub(),
        rollback: sinon.stub(),
        afterCommit: sinon.stub(),
        LOCK: {},
      };
      const result = await projectDrawingService.checkIsValidHullId([1, 2], 1, fakeTransaction as any, dummyContext);
      expect(result).to.be.true;
    });

    it('should return false if not all hulls found', async () => {
      dummyContext.repositories.projectHullRepository.findAll.resolves([{}]);
      const fakeTransaction = {
        commit: sinon.stub(),
        rollback: sinon.stub(),
        afterCommit: sinon.stub(),
        LOCK: {},
      };
      const result = await projectDrawingService.checkIsValidHullId([1, 2], 1, fakeTransaction as any, dummyContext);
      expect(result).to.be.false;
    });
  });

  describe('getAllDrawings', () => {
    it('should return paginated drawings', async () => {
      dummyContext.repositories.projectDrawingRepository.findAndCountAll.resolves({
        count: 2,
        rows: [{ id: 1 }, { id: 2 }],
      });
      const result = await projectDrawingService.getAllDrawings({ page: 1, limit: 1, drawingListId: 1 }, dummyContext);
      expect(result.data.length).to.equal(2);
      expect(result.pagination.totalItems).to.equal(2);
    });

    it('should handle no drawingListId', async () => {
      dummyContext.repositories.projectDrawingRepository.findAndCountAll.resolves({
        count: 0,
        rows: [],
      });
      const result = await projectDrawingService.getAllDrawings({}, dummyContext);
      expect(result.data.length).to.equal(0);
      expect(result.pagination.totalPages).to.equal(0);
    });
  });

  describe('deleteDrawingsById', () => {
    beforeEach(() => {
      dummyContext.sequelize.transaction.resolves({
        commit: sandbox.stub().resolves(),
        rollback: sandbox.stub().resolves(),
      });
    });

    it('should throw if currentLatestDrawing not found', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves(null);
      try {
        await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('Drawings not available');
      }
    });

    it('should throw if drawing is approved', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves({
        dataValues: { status: ProjectDrawingStatus.APPROVED, deletedAt: null, id: 1 },
      });
      try {
        await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('already approved/deleted');
      }
    });

    it('should throw if drawing is deleted', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves({
        dataValues: { status: 1, deletedAt: new Date(), id: 1 },
      });
      try {
        await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('already approved/deleted');
      }
    });

    it('should throw if id > currentLatestDrawing.id', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves({
        dataValues: { status: 1, deletedAt: null, id: 1 },
      });
      try {
        await projectDrawingService.deleteDrawingsById(1, 2, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('Drawing not found');
      }
    });

    it('should throw if id !== currentLatestDrawing.id', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves({
        dataValues: { status: 1, deletedAt: null, id: 2 },
      });
      try {
        await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('delete a previous drawing version');
      }
    });

    it('should delete drawing successfully', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.resolves({
        dataValues: { status: 1, deletedAt: null, id: 1 },
      });
      dummyContext.repositories.projectDrawingRepository.updateAll.resolves([1]);
      const result = await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
      expect(result).to.deep.equal({ message: 'Drawing deleted successfully' });
    });

    it('should handle error in catch and throw', async () => {
      dummyContext.repositories.projectDrawingRepository.findOne.throws(new Error('fail'));
      try {
        await projectDrawingService.deleteDrawingsById(1, 1, userAllPermission, dummyContext);
        expect.fail('Should throw');
      } catch (err: any) {
        expect(err.message).to.include('fail');
      }
    });
  });
});
