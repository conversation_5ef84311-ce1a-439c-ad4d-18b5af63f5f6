import {expect} from 'chai';
import sinon from 'sinon';
import ObservationService from '../../services/observations.service';
import {CustomError} from '../../utils';
import {userAllPermission} from '../data/user.data';

describe('ObservationService - createObservation', () => {
  let sandbox: sinon.SinonSandbox;
  let dummyContext: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    dummyContext = {
      repositories: {
        inspectionRepository: {
          findOne: sandbox.stub(),
        },
        inspectionObservationRepository: {
          createAll: sandbox.stub(),
        },
      },
      models: {
        InspectionObservation: {
          findOne: sandbox.stub(),
        },
      },
      user: userAllPermission,
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create observations successfully', async () => {
    const inspectionId = 1;
    const assets = [
      {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'image'},
      {fileKey: 'file2.jpg', assetName: 'Asset 2', assetType: 'image'},
    ];
    const user = userAllPermission;

    const mockInspection = {id: inspectionId} as any;
    const mockHighestVersionAsset = {get: () => 2} as any;
    const mockCreatedAssets = [
      {
        assetName: 'Asset 1',
        inspectionId: inspectionId,
        assetPath: 'file1.jpg',
        assetType: 'image',
        version: 3,
        createdBy: user.user_id,
      },
      {
        assetName: 'Asset 2',
        inspectionId: inspectionId,
        assetPath: 'file2.jpg',
        assetType: 'image',
        version: 4,
        createdBy: user.user_id,
      },
    ] as any;

    dummyContext.repositories.inspectionRepository.findOne.resolves(
      mockInspection,
    );
    dummyContext.models.InspectionObservation.findOne.resolves(
      mockHighestVersionAsset,
    );
    dummyContext.repositories.inspectionObservationRepository.createAll.resolves(
      mockCreatedAssets,
    );

    const result = await ObservationService.createObservation(
      inspectionId,
      assets,
      user,
      dummyContext,
    );

    expect(
      dummyContext.repositories.inspectionObservationRepository.createAll
        .calledOnce,
    ).to.be.true;
    expect(result).to.deep.equal(mockCreatedAssets);
  });

  it('should throw an error for invalid request payload', async () => {
    const inspectionId = 1;
    const assets = [] as any;
    const user = userAllPermission;

    try {
      await ObservationService.createObservation(
        inspectionId,
        assets,
        user,
        dummyContext,
      );
      throw new Error('Expected error not thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomError);
      expect(err.message).to.equal('Invalid request payload');
      expect((err as CustomError).code).to.equal(422);
    }
  });

  it('should throw an error if inspection is not found', async () => {
    const inspectionId = 1;
    const assets = [
      {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'image'},
    ];
    const user = userAllPermission;

    dummyContext.repositories.inspectionRepository.findOne.resolves(null);

    try {
      await ObservationService.createObservation(
        inspectionId,
        assets,
        user,
        dummyContext,
      );
      throw new Error('Expected error not thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomError);
      expect(err.message).to.equal('Inspection not found');
      expect((err as CustomError).code).to.equal(404);
    }
  });

  it('should create observations with version starting from 1 if no previous assets exist', async () => {
    const inspectionId = 1;
    const assets = [
      {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'image'},
    ];
    const user = userAllPermission;

    const mockInspection = {id: inspectionId};
    const mockHighestVersionAsset = null; // No previous assets
    const mockCreatedAssets = [
      {
        assetName: 'Asset 1',
        inspectionId: inspectionId,
        assetPath: 'file1.jpg',
        assetType: 'image',
        version: 1,
        createdBy: user.user_id,
      },
    ];

    dummyContext.repositories.inspectionRepository.findOne.resolves(
      mockInspection as any,
    );
    dummyContext.models.InspectionObservation.findOne.resolves(
      mockHighestVersionAsset,
    );
    dummyContext.repositories.inspectionObservationRepository.createAll.resolves(
      mockCreatedAssets as any,
    );

    const result = await ObservationService.createObservation(
      inspectionId,
      assets,
      user,
      dummyContext,
    );

    expect(
      dummyContext.repositories.inspectionObservationRepository.createAll
        .calledOnce,
    ).to.be.true;
    expect(result).to.deep.equal(mockCreatedAssets);
  });
});
