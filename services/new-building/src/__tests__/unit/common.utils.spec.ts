import {expect} from 'chai';
import {getAssetDestinationPath} from '../../utils/common.utils';

describe('Common Utils', () => {
  describe('getAssetDestinationPath', () => {
    it('should return correct destination path for a typical asset path', () => {
      const tenantId = 10;
      const projectId = 20;
      const assetPath = 'some/path/to/image.jpg';
      const result = getAssetDestinationPath(tenantId, projectId, assetPath);
      expect(result).to.equal('10/projects/20/images/image.jpg');
    });

    it('should handle asset path with only filename', () => {
      const tenantId = 1;
      const projectId = 2;
      const assetPath = 'file.png';
      const result = getAssetDestinationPath(tenantId, projectId, assetPath);
      expect(result).to.equal('1/projects/2/images/file.png');
    });

    it('should handle asset path with trailing slash', () => {
      const tenantId = 5;
      const projectId = 6;
      const assetPath = 'folder/';
      const result = getAssetDestinationPath(tenantId, projectId, assetPath);
      expect(result).to.equal('5/projects/6/images/');
    });

    it('should handle asset path with multiple slashes', () => {
      const tenantId = 7;
      const projectId = 8;
      const assetPath = 'a/b/c/d/e.txt';
      const result = getAssetDestinationPath(tenantId, projectId, assetPath);
      expect(result).to.equal('7/projects/8/images/e.txt');
    });

    it('should handle empty asset path', () => {
      const tenantId = 3;
      const projectId = 4;
      const assetPath = '';
      const result = getAssetDestinationPath(tenantId, projectId, assetPath);
      expect(result).to.equal('3/projects/4/images/');
    });
  });
});
