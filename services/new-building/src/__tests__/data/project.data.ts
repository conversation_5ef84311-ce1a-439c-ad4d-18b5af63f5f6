import {ProjectStatus} from '../../enums';
const today = new Date(Date.now() + 5 * 60 * 1000).toISOString();
export const projectData = [
  {
    id: 1,
    name: 'Project A',
    tenantId: 0,
    owner: 'owner 1',
    shipType: 'test',
    fuelTypeId: 1,
    engineType: 'test',
    projectTypeId: 0,
    projectDescription: 'description 1',
    totalTimeTaken: 0,
    deletedAt: null,
    status: ProjectStatus.ON_GOING,
  },
  {
    id: 2,
    name: 'Project B',
    tenantId: 0,
    owner: 'owner 2',
    shipType: 'test',
    fuelTypeId: 1,
    engineType: 'test',
    projectTypeId: 0,
    deletedAt: null,
    projectDescription: 'Description 1',
    totalTimeTaken: 0,
    status: ProjectStatus.ON_GOING,
  },
  {
    id: 3,
    name: 'Project C',
    tenantId: 0,
    owner: 'owner 1',
    shipType: 'test',
    fuelTypeId: 1,
    engineType: 'test',
    deletedAt: null,
    projectTypeId: 0,
    projectDescription: 'description 3',
    totalTimeTaken: 0,
    status: ProjectStatus.ON_GOING,
  },
];

export const newProject = {
  name: 'Sunrise Shipyard - Vessel Alpha',
  shipType: 'test',
  fuelTypeId: 1,
  engineType: 'test',
  projectTypeId: 1,
  projectDescription:
    'Refitting of bulk carrier with hybrid propulsion system.',
  status: ProjectStatus.ON_GOING,
  owner: 'Marine Logistics Ltd.',
  assetName: 'Test',
  assetType: 'image',
  assetPath: 'document/asset.png',
  hulls: [
    {
      hullNo: 'HN-101',
      keelLaidDate: today,
      steelCuttingDate: today,
      launchDate: today,
      seaTrialDate: today,
      flag: 'test',
      status: 1,
      deliveryDate: today,
      deadWeightGT: 1,
      deadWeightNT: 1,
      capacity: 1,
      vesselClasses: ['NV'],
      assetName: 'file',
      assetPath: 'path',
      assetType: '1',
      imo: 12,
    },
  ],
};

export const inputProject = {
  name: 'Test Project',
  shipType: 'Cargo',
  engineType: 'Engine A',
  fuelTypeId: 3,
  projectTypeId: 4,
  projectDescription: 'desc',
  status: 1,
  hulls: [
    {
      hullNo: 'H1',
      keelLaidDate: new Date(),
      steelCuttingDate: new Date(),
      launchDate: new Date(),
      seaTrialDate: new Date(),
      flag: 'Flag A',
      shipTypeId: 1,
      deadWeight: 1000,
      temperature: 25,
      tonnage: 3000,
      deliveryDate: new Date(),
      vesselClasses: ['NV'],
      status: 1,
    },
  ],
  assetName: 'ProjectAsset',
  assetType: 'pdf',
  assetPath: '/path/to/file',
  assetVersion: 1,
  referenceType: 1,
};

export const inputProjectWithCustom = {
  name: 'Test Project',
  shipType: 'Cargo',
  engineType: 'Engine A',
  fuelTypeId: 3,
  projectTypeId: 4,
  projectDescription: 'desc',
  status: 1,
  isShipTypeCustom: true,
  isEngineTypeCustom: true,
  hulls: [
    {
      hullNo: 'H1',
      keelLaidDate: new Date(),
      steelCuttingDate: new Date(),
      launchDate: new Date(),
      seaTrialDate: new Date(),
      flag: 'Flag A',
      shipTypeId: 1,
      deadWeight: 1000,
      temperature: 25,
      tonnage: 3000,
      deliveryDate: new Date(),
      vesselClasses: ['NV'],
      status: 1,
    },
  ],
};
