export const userAllPermission = {
  exp: **********,
  iat: **********,
  auth_time: **********,
  jti: 'abc123def456',
  iss: 'https://auth.example.com/realms/marine',
  aud: 'marine-app',
  sub: 'user-7890',
  typ: 'Bearer',
  azp: 'marine-frontend',
  nonce: 'xyz123nonce',
  session_state: '123abc456def789',
  'allowed-origins': ['https://marine-app.example.com'],
  realm_access: {
    roles: [
      'user',
      'project-manager',
      'nbp|prj|add',
      'nbp|prj|edit',
      'nbp|prj|view',
      'nbp|prj|del',
      'nbp|hull|add',
      'nbp|hull|edit',
      'nbp|hull|view',
      'nbp|hull|del',
      'nbp|comm|add',
      'nbp|comm|view',
      'nbp|drwp|add',
      'nbp|drwp|view',
      'nbp|drwp|del',
      'nbp|drwv|upld',
      'nbp|drwv|view',
      'nbp|drwv|del',
      'nbp|admin|all',
      'nbp|comm|rslv',
      'nbp|obsv|add',
      'nbp|obsv|edit',
      'nbp|obsv|view',
      'nbp|obsv|del',
      'nbp|insp|add',
      'nbp|insp|edit',
      'nbp|insp|view',
      'nbp|insp|del',
    ],
  },
  resource_access: {
    account: {
      roles: ['manage-account', 'view-profile'],
    },
    'marine-app': {
      roles: ['create-project', 'delete-project'],
    },
  },
  scope: 'openid profile email',
  sid: 'session-id-123456',
  user_name_hash: 'hash-abcdef123456',
  email_verified: true,
  user_id: 'u123456',
  name: 'John Doe',
  preferred_username: 'john.doe',
  given_name: 'John',
  financialOwnerReportingAccess: 'yes',
  tenantId: 't001',
  tenantKey: 'marine_tenant',
  email: '<EMAIL>',
  group: ['engineering', 'shipyard'],
};

export const userWithOutHullPermission = {
  exp: **********,
  iat: **********,
  auth_time: **********,
  jti: 'abc123def456',
  iss: 'https://auth.example.com/realms/marine',
  aud: 'marine-app',
  sub: 'user-7890',
  typ: 'Bearer',
  azp: 'marine-frontend',
  nonce: 'xyz123nonce',
  session_state: '123abc456def789',
  'allowed-origins': ['https://marine-app.example.com'],
  realm_access: {
    roles: [
      'user',
      'project-manager',
      'nbp|prj|add',
      'nbp|prj|edit',
      'nbp|prj|view',
      'nbp|prj|del',
    ],
  },
  resource_access: {
    account: {
      roles: ['manage-account', 'view-profile'],
    },
    'marine-app': {
      roles: ['create-project', 'delete-project'],
    },
  },
  scope: 'openid profile email',
  sid: 'session-id-123456',
  user_name_hash: 'hash-abcdef123456',
  email_verified: true,
  user_id: 'u123456',
  name: 'John Doe',
  preferred_username: 'john.doe',
  given_name: 'John',
  financialOwnerReportingAccess: 'yes',
  tenantId: 't001',
  tenantKey: 'marine_tenant',
  email: '<EMAIL>',
  group: ['engineering', 'shipyard'],
};
