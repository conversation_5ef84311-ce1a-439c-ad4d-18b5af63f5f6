import {HullInstance} from '../../models/hull.model';
import {
  HullAttributes,
  ProjectHullAttributes,
} from '../../types/inspection.types';

const date = new Date();
date.setFullYear(date.getFullYear() - 2);
export const twoYearsAgo = date.toISOString();

const today = new Date(Date.now() + 5 * 60 * 1000).toISOString();
export const yesterday = new Date(
  Date.now() - 24 * 60 * 60 * 1000,
).toISOString();

export const hulltypeData = [
  {
    projectId: 1,
    hullNo: 'HN-101',
    keelLaidDate: today,
    steelCuttingDate: today,
    launchDate: today,
    seaTrialDate: today,
    flag: 'Flag A',
    status: 1,
    deliveryDate: today,
    deadWeightGT: 1,
    deadWeightNT: 1,
    capacity: 1,
    vesselClasses: ['NV'],
    assetName: 'file',
    assetPath: 'path',
    assetType: '1',
    imo: 1,
  },
];

export const hullDataForGetAll = [
  {
    id: 1,
    hullNo: 'HN-101',
    shipName: 'Ever Glory',
    imo: 1234567,
    flag: 'Flag A',
    vesselClasses: ['NV'],
    deadWeightGT: 50000,
    deadWeightNT: 15000,
    steelCuttingDate: today,
    keelLaidDate: today,
    launchDate: today,
    capacity: 10,
    seaTrialDate: today,
    deliveryDate: today,
    status: 1,
    createdBy: '16843f95-6513-45cf-82e3-13a1c818b39d',
    updatedBy: null,
    deleted: false,
    deletedAt: null,
    deletedBy: null,
    createdAt: '2025-04-25T01:49:17.793Z',
    updatedAt: '2025-04-25T01:49:17.793Z',
  },
  {
    id: 2,
    hullNo: 'HN-102',
    shipName: 'Ever Glory',
    imo: 1234567,
    flag: 'Flag A',
    deadWeightGT: 50000,
    deadWeightNT: 15000,
    steelCuttingDate: today,
    keelLaidDate: today,
    launchDate: today,
    capacity: 10,
    tenantId: 1,
    seaTrialDate: today,
    deliveryDate: today,
    status: 1,
    vesselClasses: ['NV'],
    createdBy: '16843f95-6513-45cf-82e3-13a1c818b39d',
    updatedBy: null,
    deleted: false,
    deletedAt: null,
    deletedBy: null,
    createdAt: '2025-04-25T02:06:24.137Z',
    updatedAt: '2025-04-25T02:06:24.137Z',
  },
];

export const mockAsset = {
  id: 1,
  assetName: 'Test Asset',
  referenceId: 100,
  assetPath: 'path',
  createdBy: '123',
};

export const hullInput = {
  hullNo: 'HN-102',
  shipName: 'Ever Glory',
  imo: 1234567,
  flag: 'Flag A',
  deadWeightGT: 50000,
  deadWeightNT: 15000,
  steelCuttingDate: new Date(),
  keelLaidDate: new Date(),
  launchDate: new Date(),
  capacity: 10,
  tenantId: 1,
  seaTrialDate: new Date(),
  deliveryDate: new Date(),
  status: 1,
  vesselClasses: ['NV'],
};
// Update the type casting to use HullInstance
export const hullMockData = hullDataForGetAll[1] as unknown as HullInstance;

export const mockHullResponse = {
  data: hullDataForGetAll as unknown as HullInstance[],
  pagination: {
    totalItems: 2,
    totalPages: 1,
    page: 1,
    pageSize: 10,
  },
};

export const mockHulls: HullAttributes[] = [
  {
    id: 1,
    hullNo: 'JSHT350',
  },
  {
    id: 2,
    hullNo: 'XTRN920',
  },
];

export const mockProjectHulls: ProjectHullAttributes[] = [
  {
    id: 101,
    projectId: 1001,
    hullId: 1,
    hull: mockHulls[0],
  },
  {
    id: 102,
    projectId: 1002,
    hullId: 2,
    hull: mockHulls[1],
  },
];

export const updateHullInputData = {
  projectId: 1,
  hullNo: 'HN-101',
  keelLaidDate: today,
  steelCuttingDate: today,
  launchDate: today,
  seaTrialDate: today,
  flag: 'Flag A',
  status: 1,
  deliveryDate: today,
  deadWeightGT: 1,
  deadWeightNT: 1,
  capacity: 1,
  vesselClasses: ['NV'],
  imo: 1,
};
