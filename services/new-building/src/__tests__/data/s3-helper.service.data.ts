export const mockS3Response = {
  presignedPost: {
    url: 'https://test-bucket.s3.amazonaws.com',
    fields: {
      key: 'uploads/mock-uuid.pdf',
      bucket: 'test-bucket',
      'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
      'X-Amz-Credential': 'test-credential',
      'X-Amz-Date': '20250410T050216Z',
      Policy: 'test-policy',
      'X-Amz-Signature': 'test-signature',
    },
    fileKey: 'uploads/test-uuid',
    bucketName: 'test-bucket',
    expiresIn: 900,
  },
  presignedUrl:
    'https://test-bucket.s3.amazonaws.com/test-file-key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=test',
};
