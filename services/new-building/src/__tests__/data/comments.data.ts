import {CreationAttributes} from 'sequelize';
import {CommentStatus} from '../../enums';
import {IProjectDrawingCommentInstance} from '../../models/drawing-list-comment.model';

export const input = {
  referenceType: 1,
  referenceId: 1,
  comments: 'Test comment',
};

export const inputCommentData: CreationAttributes<IProjectDrawingCommentInstance> =
  {
    drawingListId: 1,
    comments: 'This is a test comment',
    status: 1,
    deletedAt: null,
  };

export const mockComments = [
  {
    id: 1,
    referenceType: 1,
    referenceId: 1001,
    comments: 'Test comment 1',
    status: 1,
  },
  {
    id: 2,
    referenceType: 1,
    referenceId: 1001,
    comments: 'Test comment 2',
    status: 2,
  },
];

export const existingComment = {
  id: 1,
  referenceType: 1,
  referenceId: 2,
  comments: 'Old comment',
  status: CommentStatus.OPEN,
  deleted: false,
  createdBy: 123456,
  createdAt: new Date().toISOString(),
  dataValues: {},
};

export const activeComment = {
  id: 1,
  referenceType: 1,
  referenceId: 2,
  comments: 'Some comment',
  status: CommentStatus.OPEN,
  deleted: false,
  createdBy: 1,
  createdAt: new Date(),
  dataValues: {
    deleted: false,
    status: CommentStatus.OPEN,
  },
};
