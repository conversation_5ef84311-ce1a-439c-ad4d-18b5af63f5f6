import { expect } from 'chai';
import sinon from 'sinon';
import {
  col,
  fn,
  GroupedCountResultItem,
  Includeable,
  Model,
  ModelStatic,
  Op,
} from 'sequelize';
import BaseRepository from '../../repositories/base.repository';
import { IFindQuery } from '../../types';

describe('BaseRepository', () => {
  let mockModel: sinon.SinonStubbedInstance<ModelStatic<Model>>;
  let baseRepository: BaseRepository<Model>;

  beforeEach(() => {
    mockModel = {
      findAll: sinon.stub(),
      findByPk: sinon.stub(),
      create: sinon.stub(),
      update: sinon.stub(),
      destroy: sinon.stub(),
      findAndCountAll: sinon.stub(),
      bulkCreate: sinon.stub(),
      truncate: sinon.stub(),
      findOne: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<ModelStatic<Model>>;

    baseRepository = new BaseRepository(mockModel);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('findAll', () => {
    it('should call model.findAll and return the result', async () => {
      const mockData = [{ id: 1, name: 'Test' }] as unknown as Model<any, any>[];
      mockModel.findAll.resolves(mockData);
      const mockOrder = [['name', 'ASC']];
      const result = await baseRepository.findAll(
        [],
        false,
        {},
        undefined,
        undefined,
        mockOrder as any,
      );

      expect(mockModel.findAll.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockData);
    });
    it('should call model.findAll with default parameters', async () => {
      const mockData = [{ id: 1, name: 'Test' }] as unknown as Model<any, any>[];
      mockModel.findAll.resolves(mockData);
      const result = await baseRepository.findAll();
      expect(mockModel.findAll.calledOnce).to.be.true;
      expect(mockModel.findAll.firstCall.args[0]).to.deep.equal({
        where: { deletedAt: null },
        attributes: { exclude: [] },
        include: undefined,
        transaction: undefined,
      });

      // Verify the result
      expect(result).to.deep.equal(mockData);
    });
    it('should call model.findAll with order if provided', async () => {
      const mockData = [{ id: 1, name: 'Test' }] as unknown as Model<any, any>[];
      mockModel.findAll.resolves(mockData);
      const order = [['name', 'DESC']];
      await baseRepository.findAll([], false, {}, undefined, undefined, order as any);
      expect(mockModel.findAll.calledOnce).to.be.true;
      expect(mockModel.findAll.firstCall.args[0]?.order).to.deep.equal(order);
    });
  });

  describe('findAndCountAll', () => {
    it('should call model.findAndCountAll with the correct query options and return the result', async () => {
      const mockData = {
        count: 2,
        rows: [
          { id: 1, name: 'Test 1' },
          { id: 2, name: 'Test 2' },
        ],
      };
      const queryOptions = {
        attributes: ['id', 'name'],
        where: { deleted: false },
        limit: 10,
        offset: 0,
        orderBy: [['name', 'ASC']],
      };

      mockModel.findAndCountAll.resolves(
        mockData as unknown as {
          rows: Model<any, any>[];
          count: GroupedCountResultItem[];
        },
      );

      const result = await baseRepository.findAndCountAll(
        queryOptions as unknown as IFindQuery,
      );
      mockModel.findAndCountAll.calledOnceWith({
        attributes: queryOptions.attributes,
        limit: queryOptions.limit,
        offset: queryOptions.offset,
      });
      expect(result).to.deep.equal(mockData);
    });
    it('should call model.findAndCountAll with default and custom where parameters', async () => {
      const mockData = {
        count: 2,
        rows: [
          { id: 1, name: 'Test 1' },
          { id: 2, name: 'Test 2' },
        ],
      };

      const queryOptions = {
        where: { name: 'Test' }, // Custom where condition
        limit: 10,
        offset: 0,
        orderBy: [['name', 'ASC']],
      };

      // Stub the model's findAndCountAll method to return mock data
      mockModel.findAndCountAll.resolves(mockData as any);

      // Call the findAndCountAll method with includeDeleted = false (default behavior)
      const result = await baseRepository.findAndCountAll(
        queryOptions as unknown as IFindQuery,
      );

      // Verify that findAndCountAll was called with the correct arguments
      expect(
        mockModel.findAndCountAll.calledOnceWith(
          sinon.match({
            where: { name: 'Test', deletedAt: null }, // Merged where condition
            limit: queryOptions.limit,
            offset: queryOptions.offset,
            order: queryOptions.orderBy,
          }),
        ),
      ).to.be.true;

      // Verify the result
      expect(result).to.deep.equal(mockData);

      // Call the findAndCountAll method with includeDeleted = true
      const resultWithDeleted = await baseRepository.findAndCountAll(
        queryOptions as unknown as IFindQuery,
        true,
      );

      // Verify that findAndCountAll was called with the correct arguments for includeDeleted = true
      expect(mockModel.findAndCountAll.calledTwice).to.be.true;
      expect(mockModel.findAndCountAll.secondCall.args[0]).to.deep.include({
        where: { name: 'Test' },
        limit: queryOptions.limit,
        offset: queryOptions.offset,
        order: queryOptions.orderBy,
      });

      // Verify the result
      expect(resultWithDeleted).to.deep.equal(mockData);
    });
    it('should call model.findAndCountAll with attributes if provided', async () => {
      const mockData = { count: 1, rows: [{ id: 1 }] };
      const queryOptions = {
        attributes: ['id', 'name'],
        where: { deletedAt: null },
        limit: 10,
        offset: 0,
        orderBy: [['name', 'ASC']],
      };
      mockModel.findAndCountAll.resolves(mockData as any);
      const result = await baseRepository.findAndCountAll(queryOptions as any);
      expect(result).to.deep.equal(mockData);
      expect(mockModel.findAndCountAll.firstCall.args[0].attributes).to.deep.equal(['id', 'name']);
    });

    it('should call model.findAndCountAll with distinct and include if provided', async () => {
      const mockData = { count: 1, rows: [{ id: 1 }] };
      const queryOptions = {
        where: { deletedAt: null },
        limit: 10,
        offset: 0,
        orderBy: [['name', 'ASC']],
        distinct: true,
        include: [{ model: {} }],
      };
      mockModel.findAndCountAll.resolves(mockData as any);
      const result = await baseRepository.findAndCountAll(queryOptions as any);
      expect(result).to.deep.equal(mockData);
      expect(mockModel.findAndCountAll.firstCall.args[0].distinct).to.equal(true);
      expect(mockModel.findAndCountAll.firstCall.args[0].include).to.deep.equal([{ model: {} }]);
    });
  });

  describe('findById', () => {
    it('should call model.findByPk with the correct id and return the result', async () => {
      const mockData = { id: 1, name: 'Test' } as unknown as Model<any, any>;
      mockModel.findOne.resolves(mockData);

      const result = await baseRepository.findById(1);

      expect(
        mockModel.findOne.calledOnceWithExactly({
          where: { id: 1, deletedAt: null },
        }),
      ).to.be.true;

      expect(result).to.deep.equal(mockData);
    });
    it('should call model.findOne with include if includeRelations is true', async () => {
      const mockData = { id: 1, name: 'Test' } as unknown as Model<any, any>;
      mockModel.findOne.resolves(mockData);
      // Create a mock model constructor to satisfy Includeable type
      class MockIncludeModel extends Model { }
      const include = [{ model: MockIncludeModel }];
      const result = await baseRepository.findById(1, true, { include });
      expect(mockModel.findOne.calledOnce).to.be.true;
      expect(mockModel.findOne.firstCall.args[0]?.include).to.deep.equal(include);
      expect(result).to.deep.equal(mockData);
    });
  });

  describe('create', () => {
    it('should call model.create with the correct data and return the result', async () => {
      const mockData = { name: 'Test' };
      const createdData = { id: 1, ...mockData } as unknown as Model<any, any>;
      mockModel.create.resolves(createdData);

      const result = await baseRepository.create(mockData);

      expect(mockModel.create.calledOnceWith(mockData)).to.be.true;
      expect(result).to.deep.equal(createdData);
    });
    it('should call model.create with options if provided', async () => {
      const mockData = { name: 'Test' };
      const createdData = { id: 1, ...mockData } as unknown as Model<any, any>;
      mockModel.create.resolves(createdData);
      const options = { returning: true };
      const result = await baseRepository.create(mockData, options);
      expect(mockModel.create.calledOnceWith(mockData, options)).to.be.true;
      expect(result).to.deep.equal(createdData);
    });
  });

  describe('update', () => {
    it('should call model.update with the correct id and updates', async () => {
      const updates = { name: 'Updated Test' };
      mockModel.update.resolves([1]); // Sequelize returns the number of affected rows

      const result = await baseRepository.update(1, updates);
      expect(
        mockModel.update.calledOnceWith(updates, sinon.match({ where: { id: 1 } })),
      ).to.be.true;

      expect(result).to.deep.equal([1]);
    });
    it('should call model.update with transaction if provided', async () => {
      const updates = { name: 'Updated Test' };
      const transaction = { id: 'tx' };
      mockModel.update.resolves([1]);
      const result = await baseRepository.update(1, updates, transaction as any);
      expect(mockModel.update.calledOnce).to.be.true;
      expect(mockModel.update.firstCall.args[1].transaction).to.equal(transaction);
      expect(result).to.deep.equal([1]);
    });
  });

  describe('delete', () => {
    it('should call model.destroy with the correct id', async () => {
      mockModel.destroy.resolves(1); // Sequelize returns the number of affected rows

      const result = await baseRepository.delete(1);

      expect(mockModel.destroy.calledOnceWith({ where: { id: 1 } })).to.be.true;
      expect(result).to.equal(1);
    });
    it('should call model.destroy and return 0 if nothing deleted', async () => {
      mockModel.destroy.resolves(0);
      const result = await baseRepository.delete(999);
      expect(mockModel.destroy.calledOnceWith({ where: { id: 999 } })).to.be.true;
      expect(result).to.equal(0);
    });
  });

  describe('findOne', () => {
    it('should call model.findOne with correct parameters', async () => {
      const where = { id: 1 };
      const excludeFields = ['password'];
      const include = [{ association: 'profile' }];
      const expectedResult = { id: 1, name: 'Test User' };

      mockModel.findOne.resolves(expectedResult as any);

      const result = await baseRepository.findOne(
        where,
        excludeFields,
        false,
        include,
      );

      expect(mockModel.findOne.calledOnce).to.be.true;
      expect(mockModel.findOne.firstCall.args[0]).to.deep.equal({
        where: {
          id: 1,
          deletedAt: null,
        },
        attributes: {
          exclude: excludeFields,
        },
        include,
      });
      expect(result).to.equal(expectedResult);
    });
    it('should call model.findOne with extraOptions if provided', async () => {
      const where = { id: 1 };
      const excludeFields: string[] = [];
      const include: Includeable[] = [];
      // Fix: Pass extraOptions as a valid FindOptions object
      const extraOptions = { order: [['createdAt', 'DESC']] } as any;
      mockModel.findOne.resolves({ id: 1 } as any);
      const result = await baseRepository.findOne(where, excludeFields, false, include, extraOptions);
      expect(mockModel.findOne.calledOnce).to.be.true;
      expect(mockModel.findOne.firstCall?.args[0]?.order).to.deep.equal([['createdAt', 'DESC']]);
      expect(result).to.deep.equal({ id: 1 });
    });
    it('should call model.findOne with includeDeleted true', async () => {
      const where = { id: 1 };
      mockModel.findOne.resolves({ id: 1 } as any);
      const result = await baseRepository.findOne(where, [], true);
      expect(mockModel.findOne.calledOnce).to.be.true;
      expect('deletedAt' in (mockModel.findOne.firstCall.args[0]?.where || {})).to.be.false;
      expect(result).to.deep.equal({ id: 1 });
    });
  });

  describe('truncateTableData', () => {
    it('should call model.truncate and resolve successfully', async () => {
      mockModel.truncate.resolves();
      await baseRepository.truncateTableData();
      expect(mockModel.truncate.calledOnce).to.be.true;
    });
    it('should throw error if truncate fails', async () => {
      mockModel.truncate.rejects(new Error('truncate error'));
      try {
        await baseRepository.truncateTableData();
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('truncate error');
      }
    });
  });

  describe('createAll', () => {
    it('should call model.bulkCreate with the correct data and resolve successfully', async () => {
      const mockData = [{ name: 'Test 1' }, { name: 'Test 2' }];
      const createdData = [
        { id: 1, name: 'Test 1' },
        { id: 2, name: 'Test 2' },
      ];
      mockModel.bulkCreate.resolves(createdData as any);
      const result = await baseRepository.createAll(mockData);
      expect(mockModel.bulkCreate.calledOnceWith(mockData)).to.be.true;
      expect(result).to.deep.equal(createdData);
    });
    it('should call model.bulkCreate with transaction if provided', async () => {
      const mockData = [{ name: 'Test 1' }];
      const transaction = { id: 'tx' };
      mockModel.bulkCreate.resolves([{ id: 1, name: 'Test 1' }] as any);
      const result = await baseRepository.createAll(mockData, undefined, transaction as any);
      expect(mockModel.bulkCreate.calledOnce).to.be.true;
      expect(mockModel.bulkCreate.firstCall.args[1]?.transaction).to.equal(transaction);
      expect(result).to.deep.equal([{ id: 1, name: 'Test 1' }]);
    });
  });

  describe('updateAll', () => {
    it('should call model.update with the correct updates and where condition', async () => {
      const updates = { status: 'active' };
      const where = { projectId: 1 };
      const mockTransaction = { id: 'mock-transaction' }; // Mock transaction object

      // Stub the model's update method to simulate a successful update
      mockModel.update.resolves([5]); // Sequelize returns the number of affected rows

      const result = await baseRepository.updateAll(
        updates,
        where,
        mockTransaction as any,
      );

      // Verify that the update method was called with the correct arguments
      expect(
        mockModel.update.calledOnceWith(
          updates,
          sinon.match({ where, transaction: mockTransaction }),
        ),
      ).to.be.true;

      // Verify the result
      expect(result).to.deep.equal([5]);
    });

    it('should throw an error if the update fails', async () => {
      const updates = { status: 'inactive' };
      const where = { projectId: 2 };

      // Stub the model's update method to throw an error
      mockModel.update.rejects(new Error('Update failed'));

      try {
        await baseRepository.updateAll(updates, where);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        // Verify the error message
        expect((error as Error).message).to.equal('Update failed');
      }
    });
    it('should call model.update with returning true', async () => {
      const updates = { status: 'active' };
      const where = { projectId: 1 };
      mockModel.update.resolves([5]);
      const result = await baseRepository.updateAll(updates, where);
      expect(mockModel.update.calledOnce).to.be.true;
      expect(mockModel.update.firstCall.args[1].returning).to.be.true;
      expect(result).to.deep.equal([5]);
    });
  });

  describe('getCountByStatus', () => {
    it('should return grouped count by status with tenantId', async () => {
      const mockResult = [
        { status: 1, count: '2' },
        { status: 2, count: '3' },
      ];
      mockModel.findAll.resolves(mockResult as any);
      const result = await baseRepository.getCountByStatus('tenant-1');
      expect(result).to.deep.equal({ 1: 2, 2: 3 });
    });

    it('should return grouped count by status without tenantId', async () => {
      const mockResult = [
        { status: 1, count: '2' },
        { status: 2, count: '3' },
      ];
      mockModel.findAll.resolves(mockResult as any);
      const result = await baseRepository.getCountByStatus();
      expect(result).to.deep.equal({ 1: 2, 2: 3 });
    });

    it('should return empty object if no results', async () => {
      mockModel.findAll.resolves([] as any);
      const result = await baseRepository.getCountByStatus();
      expect(result).to.deep.equal({});
    });
  });

  describe('getOptionsByKey', () => {
    it('should fetch unique values for a valid key', async () => {
      const mockKey = 'status';
      const validKeys = ['status', 'type'];
      const mockExtraWhere = { deletedAt: null };
      const mockData = [{ status: 'active' }, { status: 'inactive' }];

      // Stub the model's findAll method to return mock data
      mockModel.findAll.resolves(mockData as any);

      const result = await baseRepository.getOptionsByKey(
        mockKey,
        validKeys,
        mockExtraWhere,
      );

      // Verify that findAll was called with the correct arguments
      expect(
        mockModel.findAll.calledOnceWith(
          sinon.match({
            attributes: [[fn('DISTINCT', col(mockKey)), mockKey]],
            where: {
              ...mockExtraWhere,
              [mockKey]: { [Op.ne]: null }, // Exclude null values
            },
            raw: true,
          }),
        ),
      ).to.be.true;

      // Verify the result
      expect(result).to.deep.equal(mockData);
    });

    it('should throw an error if the key is invalid', async () => {
      const mockKey = 'invalidKey';
      const validKeys = ['status', 'type'];

      try {
        await baseRepository.getOptionsByKey(mockKey, validKeys);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        // Verify the error message
        expect((error as Error).message).to.equal(`Invalid key: ${mockKey}`);
      }
    });

    it('should handle an empty result set gracefully', async () => {
      const mockKey = 'status';
      const validKeys = ['status', 'type'];
      const mockExtraWhere = { deletedAt: null };

      // Stub the model's findAll method to return an empty array
      mockModel.findAll.resolves([]);

      const result = await baseRepository.getOptionsByKey(
        mockKey,
        validKeys,
        mockExtraWhere,
      );

      // Verify that findAll was called with the correct arguments
      expect(
        mockModel.findAll.calledOnceWith(
          sinon.match({
            attributes: [[fn('DISTINCT', col(mockKey)), mockKey]],
            where: {
              ...mockExtraWhere,
              [mockKey]: { [Op.ne]: null }, // Exclude null values
            },
            raw: true,
          }),
        ),
      ).to.be.true;

      // Verify the result
      expect(result).to.deep.equal([]);
    });

    it('should throw error if findAll fails', async () => {
      const mockKey = 'status';
      const validKeys = ['status', 'type'];
      mockModel.findAll.rejects(new Error('findAll error'));
      try {
        await baseRepository.getOptionsByKey(mockKey, validKeys);
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.equal('findAll error');
      }
    });
  });
});
