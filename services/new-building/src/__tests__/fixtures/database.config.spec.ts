import {expect} from 'chai';
import sinon from 'sinon';
import {Sequelize, Model, ModelCtor} from 'sequelize';
import {getTenantDb, getTenantDbConfig} from '../../configs/database.config';
import * as models from '../../models/registerModels';

describe('Database Config', () => {
  let sequelizeStub: sinon.SinonStub;
  let registerModelsStub: sinon.SinonStub;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Store original environment variables
    originalEnv = {...process.env};

    // Set test environment variables for pool configuration
    process.env.DB_HOST = 'localhost';
    process.env.DB_USER = 'test_user';
    process.env.DB_PASSWORD = 'test_password';
    process.env.DB_PORT = '5432';
    process.env.DB_MIN_CONNECTIONS = '5';
    process.env.DB_MAX_CONNECTIONS = '20';
    process.env.PG_SSL_ENABLED = 'false';

    // Clear the cache before each test to ensure test isolation
    const databaseConfig = require('../../configs/database.config');
    if (databaseConfig.tenantDbCache) {
      Object.keys(databaseConfig.tenantDbCache).forEach(key => {
        delete databaseConfig.tenantDbCache[key];
      });
    }

    sequelizeStub = sinon.stub(Sequelize.prototype, 'authenticate').resolves();

    // Mock the return value of registerModels to include the required model constructors
    registerModelsStub = sinon.stub(models, 'registerModels').returns({
      Project: class extends Model {},
      Hull: class extends Model {},
      ProjectType: class extends Model {},
      FuelType: class extends Model {},
      ProjectHull: class extends Model {},
      HullClass: class extends Model {},
      ProjectDrawing: class extends Model {},
      DrawingsList: class extends Model {},
      DrawingListComment: class extends Model {},
      InspectionObservation: class extends Model {},
      Transaction: class extends Model {},
      InspectionComment: class extends Model {},
      InspectionSource: class extends Model {},
      Inspection: class extends Model {},
      InspectionSourceFile: class extends Model {},
      DrawingSource: class extends Model {},
    });
  });

  afterEach(() => {
    sinon.restore(); // Restore all stubs after each test
    // Restore original environment variables
    process.env = originalEnv;
  });

  describe('getTenantDbConfig', () => {
    it('should return the correct database configuration for a tenant', () => {
      const tenantKey = 'tenant1';
      const config = getTenantDbConfig(tenantKey);

      expect(config).to.deep.equal({
        host: process.env.DB_HOST,
        database: tenantKey,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        port: Number(process.env.DB_PORT),
        dialect: 'postgres',
        logging: false,
        dialectOptions: {
          ssl:
            process.env.PG_SSL_ENABLED === 'true' ||
            process.env.PG_SSL_ENABLED === 'TRUE'
              ? {require: true, rejectUnauthorized: false}
              : undefined,
        },
        pool: {
          min: Number(process.env.DB_MIN_CONNECTIONS) ?? 5,
          max: Number(process.env.DB_MAX_CONNECTIONS) ?? 20,
          acquire: 60000, // 60 seconds
          idle: 10000, // 10 seconds
          evict: 1000, // 1 second
        },
      });
    });
  });

  describe('getTenantDb', () => {
    it('should create a new tenant database connection if not cached', async () => {
      const tenantKey = 'test-tenant-1';

      const result = await getTenantDb(tenantKey);

      expect(sequelizeStub.calledOnce).to.be.true;
      expect(registerModelsStub.calledOnce).to.be.true;
      expect(result).to.have.property('sequelize');
      expect(result).to.have.property('models');
      expect(result.models).to.have.keys([
        'Project',
        'Hull',
        'ProjectType',
        'FuelType',
        'ProjectHull',
        'HullClass',
        'ProjectDrawing',
        'DrawingsList',
        'DrawingListComment',
        'InspectionObservation',
        'Transaction',
        'InspectionComment',
        'InspectionSource',
        'Inspection',
        'InspectionSourceFile',
        'DrawingSource',
      ]);
    });

    it('should return the cached tenant database connection if already created', async () => {
      const tenantKey = 'test-tenant-2';

      // First call to create the connection
      await getTenantDb(tenantKey);

      // Second call should use the cached connection
      const result = await getTenantDb(tenantKey);

      expect(sequelizeStub.calledOnce).to.be.true; // authenticate should only be called once
      expect(registerModelsStub.calledOnce).to.be.true; // registerModels should only be called once
      expect(result).to.have.property('sequelize');
      expect(result).to.have.property('models');
      expect(result.models).to.have.keys([
        'Project',
        'Hull',
        'ProjectType',
        'FuelType',
        'ProjectHull',
        'HullClass',
        'ProjectDrawing',
        'DrawingsList',
        'DrawingListComment',
        'InspectionObservation',
        'Transaction',
        'InspectionComment',
        'InspectionSource',
        'Inspection',
        'InspectionSourceFile',
        'DrawingSource',
      ]);
    });

    it('should throw an error if the database connection fails', async () => {
      const tenantKey = 'test-tenant-3';
      sequelizeStub.rejects(new Error('Connection failed'));

      try {
        await getTenantDb(tenantKey);
      } catch (err) {
        if (err instanceof Error) {
          expect(err.message).to.equal('Connection failed');
        } else {
          throw err;
        }
      }

      expect(sequelizeStub.calledOnce).to.be.true;
    });
  });
});
