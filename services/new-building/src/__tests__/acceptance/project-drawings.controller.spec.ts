import request from 'supertest';
import sinon from 'sinon';
import projectDrawingService from '../../services/projects-drawings.service';
import { expect } from 'chai';
import app from './test-helper';
import { bearerToken } from '../data/auth-user.data';
import { Model } from 'sequelize';
import * as databaseConfig from '../../configs/database.config';
import { CustomError } from '../../utils';
import { IProjectDrawingsInstance } from '../../models';

describe('Project Drawings Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });

    // Stub the service methods directly without context comparison
    sinon
      .stub(projectDrawingService, 'createProjectDrawings')
      .callsFake(async (drawingListId, data) => {
        return {
          id: 1,
          ...data,
          drawingListId,
          dataValues: {
            id: 1,
            ...data,
            drawingListId,
          },
        } as unknown as Model;
      });

    sinon.stub(projectDrawingService, 'findDrawingsById').resolves({
      id: 1,
      name: 'Drawing A',
      assetPath: 'folder/drawing-a.png',
      assetType: 'image',
      status: 1,
      dataValues: {
        id: 1,
        name: 'Drawing A',
        assetPath: 'folder/drawing-a.png',
        assetType: 'image',
        status: 1,
      },
    } as unknown as Model);

    sinon.stub(projectDrawingService, 'deleteDrawingsById').resolves({
      message: 'Drawing deleted successfully',
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('GET /project-drawings/:id', () => {
    it('should handle error and return 500 if service throws', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      // Throw with status 400, but expect 500 in test
      sinon.stub(projectDrawingService, 'findDrawingsById').throws(
        new CustomError('Internal server error', 500)
      );
      const response = await request(app)
        .get('/project-drawings/999')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(500);
      expect(response.body.message).to.equal('Internal server error');
    });

    it('should fetch drawing successfully by ID', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      // Throw with status 400, but expect 500 in test
      sinon.stub(projectDrawingService, 'findDrawingsById').resolves(
        {
          id: 999,
          name: 'Drawing A'
        } as unknown as IProjectDrawingsInstance
      );

      const response = await request(app)
        .get('/project-drawings/999')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.eq({
        id: 999,
        name: 'Drawing A'
      });
    });
  });

  describe('GET /project-drawings', () => {
    it('should fetch all drawings successfully', async () => {
      sinon.stub(projectDrawingService, 'getAllDrawings').resolves({
        data: [
          {
            id: 1,
            name: 'Drawing A',
            assetPath: 'folder/drawing-a.png',
            assetType: 'image',
            status: 1,
            dataValues: {
              id: 1,
              name: 'Drawing A',
              assetPath: 'folder/drawing-a.png',
              assetType: 'image',
              status: 1,
            },
          } as unknown as Model,
        ],
        pagination: { totalItems: 1, totalPages: 1, page: 1, pageSize: 10 },
      });
      const response = await request(app)
        .get('/project-drawings')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('pagination');
    });

    it('should handle error and return 500 if service throws', async () => {
      sinon.stub(projectDrawingService, 'getAllDrawings').throws(
        new CustomError('Internal server error', 500)
      );
      const response = await request(app)
        .get('/project-drawings')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(500);
      expect(response.body.message).to.include('Internal server error');
    });
  });

  describe('POST /project-drawings', () => {
    it('should save a drawing version successfully', async () => {
      const mockRequestBody = {
        drawingListId: 1,
        assetName: 'Drawing.jpg',
        dueDate: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
        assetType: 'image',
        assetPath: 'folder/drawing-a.png',
        status: 1,
      };

      const response = await request(app)
        .post('/project-drawings')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal({
        message: 'Drawing version saved successfully.',
        error: false,
        result: {
          id: 1,
          ...mockRequestBody,
          dataValues: {
            id: 1,
            ...mockRequestBody,
          },
        },
      });
    });

    it('should handle error and return 500 if service throws', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      sinon.stub(projectDrawingService, 'createProjectDrawings').throws(
        new CustomError('An error occurred while saving the drawings', 500)
      );
      const response = await request(app)
        .post('/project-drawings')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send({
          drawingListId: 1,
          assetName: 'Drawing.jpg',
          dueDate: new Date().toISOString(),
          assetType: 'image',
          assetPath: 'folder/drawing-a.png',
          status: 1,
        });
      expect(response.status).to.equal(500);
      expect(response.body.message).to.include('An error occurred while saving the drawings');
    });
  });

  describe('DELETE /project-drawings/:drawingListId/:id', () => {
    it('should delete the latest drawing and return 200', async () => {
      const response = await request(app)
        .delete('/project-drawings/1/10')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal({
        message: 'Drawing deleted successfully',
      });
    });

    it('should handle error and return 500 if service throws', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      sinon.stub(projectDrawingService, 'deleteDrawingsById').throws(
        new CustomError('Internal server error', 500)
      );
      const response = await request(app)
        .delete('/project-drawings/1/10')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(500);
      expect(response.body.message).to.include('Internal server error');
    });
  });
});
