import express from 'express';
import {
  FuelType<PERSON><PERSON>roller,
  ProjectTypeC<PERSON>roller,
  AssetsController,
  ProjectsController,
  CronsController,
  DrawingsController,
  HullController,
  ProjectDrawingsController,
  CommentsController,
  InspectionController,
  ObservationsController,
  VesselMasterDataController,
} from '../../controllers';

export function setupEnv() {
  process.env.JWT_PUBLIC_KEY = 'false';
  process.env.JWT_SECRET =
    '7e3a1fa5e4606d2481096e59ec085a5086cfe5c95bfa95f3f5be40607ae9cac66656cec374c91c7765ee8d0c9940aa0d25ef41a6f9ab1dcdd7ddd5796ddb4484dc21c9768b2ca3eec37353552fc7e4fb0a67f5c6be6aa962e4c3a51f45ed4de994e986359d43832dcd0a8efe8fe9263c1a0366e1bae248b26c04298bfa65f57b920cb4a79f774b24d42cc5818c0b41366589625789429b0f5709d8891e2c6553868894a4a3cf926bad28562af187cf880fc71097b0e2670f6da42ea3f2cad3bf6c5a397f6033e792d150aa1731538fc2556a6f239b10b8805e67c3062879a4c465a406b6c6422fc0363702ef1405f854bed4969d98ca4ae417eb8ddbf5ebf901';
  process.env.JWT_ISSUER = 'https://test.com/auth';
  process.env.DB_USER = 'test_user';
  process.env.DB_PASSWORD = 'test_password';
  process.env.DB_HOST = 'localhost';
  process.env.DB_PORT = '5432';
}

function setupApp() {
  const app = express();
  app.use(express.json());
  app.use('/projects', ProjectsController);
  app.use('/fuel-type', FuelTypeController);
  app.use('/project-type', ProjectTypeController);
  app.use('/assets', AssetsController);
  app.use('/crons', CronsController);
  app.use('/drawing-lists', DrawingsController);
  app.use('/hulls', HullController);
  app.use('/project-drawings', ProjectDrawingsController);
  app.use('/comments', CommentsController);
  app.use('/inspections', InspectionController);
  app.use('/observations', ObservationsController);
  app.use('/vessel', VesselMasterDataController);
  setupEnv();

  return app;
}

const app = setupApp();

export default app;
