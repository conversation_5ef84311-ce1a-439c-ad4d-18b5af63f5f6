import request from 'supertest';
import sinon from 'sinon';
import {expect} from 'chai';
import {drawingsService} from '../../services';
import app from './test-helper';
import {bearerToken} from '../data/auth-user.data';
import {CustomError} from '../../utils';
import * as databaseConfig from '../../configs/database.config';

describe('Drawings Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /drawing-lists', () => {
    it('should save drawings successfully and return the result', async () => {
      const mockRequestBody = {
        fileKey: 'folder/drawing-lists.xlsx',
        projectId: 1,
      };

      const mockResult = {
        error: false,
        message: 'Drawings saved successfully',
        result: [
          {
            drawingNo: 'D001',
            name: 'Drawing 1',
            discipline: 'Mechanical',
            projectId: 1,
          },
        ],
      };

      // Stub the drawingsService.createDrawingFromExcel method
      sinon
        .stub(drawingsService, 'createDrawingFromExcel')
        .resolves(mockResult as any);

      const response = await request(app)
        .post('/drawing-lists')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(200);
    });

    it('should return 400 if required fields are missing', async () => {
      const mockRequestBody = {
        projectId: 1, // Missing fileKey
      };

      const response = await request(app)
        .post('/drawing-lists')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);
      expect(response.status).to.equal(400);
    });

    it('should return something went wrong', async () => {
      const mockRequestBody = {
        fileKey: 'folder/drawing-lists.xlsx',
        projectId: 1,
      };

      // Stub the drawingsService.createDrawingFromExcel method to throw an error
      sinon
        .stub(drawingsService, 'createDrawingFromExcel')
        .rejects(new Error('Internal server error'));

      const response = await request(app)
        .post('/drawing-lists')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(400);
    });
  });

  describe('GET /drawing-lists/:id', () => {
    it('should fetch drawing details by ID successfully', async () => {
      const mockDrawing = {
        id: 1,
        name: 'Mechanical Drawings',
        projectId: 101,
        createdAt: '2025-04-25T10:00:00Z',
        updatedAt: '2025-04-26T12:00:00Z',
      };

      sinon
        .stub(drawingsService, 'findDrawingsById')
        .resolves(mockDrawing as any);

      const response = await request(app)
        .get('/drawing-lists/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockDrawing);
    });
  });

  it('should return 400 if the service throws an error', async () => {
    sinon
      .stub(drawingsService, 'findDrawingsById')
      .rejects(new Error('Internal server error'));

    const response = await request(app)
      .get('/drawing-lists/1')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(400);
  });
});

describe('GET /drawing-lists/project/:projectId', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });
  it('should fetch drawing lists by project ID successfully', async () => {
    const mockDrawings = [
      {
        id: 1,
        name: 'Mechanical Drawings',
        projectId: 101,
        createdAt: '2025-04-25T10:00:00Z',
        updatedAt: '2025-04-26T12:00:00Z',
      },
      {
        id: 2,
        name: 'Electrical Drawings',
        projectId: 101,
        createdAt: '2025-04-25T11:00:00Z',
        updatedAt: '2025-04-26T13:00:00Z',
      },
    ];

    sinon.stub(drawingsService, 'getAllDrawings').resolves(mockDrawings as any);

    const response = await request(app)
      .get('/drawing-lists/?projectId=1')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal(mockDrawings);
  });

  it('should return 400 if the service throws an error', async () => {
    sinon
      .stub(drawingsService, 'getAllDrawings')
      .rejects(new Error('Internal server error'));

    const response = await request(app)
      .get('/drawing-lists/?projectId=101')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(400);
  });
});

describe('DELETE /api/drawings/:id', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });
  afterEach(() => {
    sinon.restore();
  });

  it('should return 409 if files are linked to drawing number', async () => {
    sinon
      .stub(drawingsService, 'deleteDrawingsById')
      .throws(
        new CustomError(
          'Cannot delete drawing: one or more files are linked to this drawing number.',
          409,
        ),
      );

    const res = await request(app)
      .delete('/drawing-lists/1')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(409);
  });

  it('should delete drawing successfully if no files linked', async () => {
    const mockResponse = {
      error: false,
      message: 'Drawing has been deleted successfully',
      data: {id: 2},
    };

    sinon.stub(drawingsService, 'deleteDrawingsById').resolves(mockResponse);

    const res = await request(app)
      .delete('/drawing-lists/2')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(200);
    expect(res.body.message).to.equal('Drawing has been deleted successfully');
    expect(res.body.data).to.have.property('id', 2);
  });

  it('should return 404 if drawing not found', async () => {
    sinon
      .stub(drawingsService, 'deleteDrawingsById')
      .throws(new CustomError('Drawings not available with the given id', 404));

    const res = await request(app)
      .delete('/drawing-lists/3')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(404);
  });

  it('should return 409 if drawing is already deleted', async () => {
    sinon
      .stub(drawingsService, 'deleteDrawingsById')
      .throws(new CustomError('Drawing with ID 4 is already deleted', 409));

    const res = await request(app)
      .delete('/drawing-lists/4')
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(409);
  });

  it('should return 401 if token is missing', async () => {
    const res = await request(app).delete('/drawing-lists/5');
    expect(res.status).to.equal(401);
  });

  describe('GET /drawing-lists/options/:key', () => {
    it('should fetch unique values for a valid key successfully', async () => {
      const mockKey = 'discipline';
      const mockResult = {
        error: false,
        message: `Unique values for key '${mockKey}' fetched successfully`,
        data: ['Mechanical', 'Electrical', 'Civil'],
      };

      // Stub the drawingsService.getOptionsByKey method
      sinon
        .stub(drawingsService, 'getOptionsByKey')
        .resolves(mockResult as any);

      const response = await request(app)
        .get(`/drawing-lists/options/${mockKey}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should return 400 if the key is invalid', async () => {
      const mockKey = 'invalidKey';
      sinon
        .stub(drawingsService, 'getOptionsByKey')
        .rejects(new Error('Invalid key: invalidKey'));

      const response = await request(app)
        .get(`/drawing-lists/options/${mockKey}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });
});
