import request from 'supertest';
import {expect} from 'chai';
import sinon from 'sinon';
import S3HelperService from '../../services/s3-helper.service';
import {mockS3Response} from '../data/s3-helper.service.data';
import {bearerToken} from '../data/auth-user.data';
import app from './test-helper';
import * as assetService from '../../services/assets.services';
import * as databaseConfig from '../../configs/database.config';

describe('Assets Controller Endpoints', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /assets/presigned-upload-url', () => {
    it('should return presigned upload URL', async () => {
      sinon
        .stub(S3HelperService, 'generatePresignedPost')
        .resolves(mockS3Response.presignedPost);

      const response = await request(app)
        .post('/assets/presigned-upload-url')
        .set('Content-Type', 'application/json')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send({
          fileName: 'test.pdf',
          contentType: 'application/pdf',
        });

      expect(response.status).to.equal(201);
      expect(response.body).to.deep.equal(mockS3Response.presignedPost);
    });

    it('should return 500 if service throws error', async () => {
      sinon
        .stub(S3HelperService, 'generatePresignedPost')
        .rejects(new Error('S3 Error'));

      const response = await request(app)
        .post('/assets/presigned-upload-url')
        .set('Content-Type', 'application/json')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send({
          fileName: 'test.pdf',
          contentType: 'application/pdf',
        });

      expect(response.status).to.equal(500);
      expect(response.body).to.deep.equal({
        error: 'Failed to generate presigned URL',
      });
    });
  });

  describe('Assets Controller - updateAssetData', () => {
    afterEach(() => {
      sinon.restore();
    });

    describe('PUT /assets/:type/:id', () => {
      it('should update a project asset successfully', async () => {
        const mockRequestBody = {
          assetName: 'Updated Project Asset',
          assetPath: 'updated/path/to/project',
          assetType: 'image/png',
        };

        const mockResponse = {
          error: false,
          message: 'Asset updated successfully',
        };

        const modifyAssetStub = sinon
          .stub(assetService, 'modifyExistingAsset')
          .resolves(mockResponse);

        const response = await request(app)
          .put('/assets/project/1')
          .set('Authorization', `Bearer ${bearerToken}`)
          .send(mockRequestBody);

        expect(modifyAssetStub.calledOnce).to.be.true;
        expect(modifyAssetStub.calledWith('project', 1, mockRequestBody)).to.be
          .true;
        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockResponse);
      });

      it('should update a hull asset successfully', async () => {
        const mockRequestBody = {
          assetName: 'Updated Hull Asset',
          assetPath: 'updated/path/to/hull',
          assetType: 'image/png',
        };

        const mockResponse = {
          error: false,
          message: 'Asset updated successfully',
        };

        const modifyAssetStub = sinon
          .stub(assetService, 'modifyExistingAsset')
          .resolves(mockResponse);

        const response = await request(app)
          .put('/assets/hull/2')
          .set('Authorization', `Bearer ${bearerToken}`)
          .send(mockRequestBody);

        expect(modifyAssetStub.calledOnce).to.be.true;
        expect(modifyAssetStub.calledWith('hull', 2, mockRequestBody)).to.be
          .true;
        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockResponse);
      });

      it('should return 500 if an error occurs while updating a project asset', async () => {
        const mockRequestBody = {
          assetName: 'Updated Project Asset',
          assetPath: 'updated/path/to/project',
          assetType: 'image/png',
        };

        sinon
          .stub(assetService, 'modifyExistingAsset')
          .throws(new Error('DB error'));

        const response = await request(app)
          .put('/assets/project/1')
          .set('Authorization', `Bearer ${bearerToken}`)
          .send(mockRequestBody);

        expect(response.status).to.equal(500);
        expect(response.body).to.deep.equal({
          error: 'Failed to updating asset',
        });
      });

      it('should return 500 if an error occurs while updating a hull asset', async () => {
        const mockRequestBody = {
          assetName: 'Updated Hull Asset',
          assetPath: 'updated/path/to/hull',
          assetType: 'image/png',
        };

        sinon
          .stub(assetService, 'modifyExistingAsset')
          .throws(new Error('DB error'));

        const response = await request(app)
          .put('/assets/hull/2')
          .set('Authorization', `Bearer ${bearerToken}`)
          .send(mockRequestBody);

        expect(response.status).to.equal(500);
        expect(response.body).to.deep.equal({
          error: 'Failed to updating asset',
        });
      });
    });
  });
});
