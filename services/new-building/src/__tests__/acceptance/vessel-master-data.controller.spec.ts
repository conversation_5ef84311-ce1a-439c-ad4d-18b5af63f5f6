import request from 'supertest';
import {expect} from 'chai';
import sinon from 'sinon';
import app from './test-helper';
import VesselMasterDataService from '../../services/vessel-master-data.service';
import {bearerToken} from '../data/auth-user.data';
import * as databaseConfig from '../../configs/database.config';

describe('VesselMasterData Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore(); // Restore all stubs after each test
  });

  describe('GET /api/vessel/master-data', () => {
    it('should fetch vessel master data successfully and return 200', async () => {
      const mockVesselData = {
        miscCurrencys: [
          {id: 1, value: 'USD', updated_at: '2025-05-23T10:00:00Z'},
        ],
        owners: [{id: 1, value: 'Owner A', updated_at: '2025-05-23T10:00:00Z'}],
        vesselTypes: [
          {id: 1, value: 'Cargo', updated_at: '2025-05-23T10:00:00Z'},
        ],
        miscEngines: [
          {id: 1, value: 'Engine A', updated_at: '2025-05-23T10:00:00Z'},
        ],
        flags: [{id: 1, value: 'Flag A', updated_at: '2025-05-23T10:00:00Z'}],
        vesselClasses: [
          {id: 1, value: 'Class A', updated_at: '2025-05-23T10:00:00Z'},
        ],
      };

      // Stub the service function to return mock data
      sinon
        .stub(VesselMasterDataService, 'getVesselMasterData')
        .resolves(mockVesselData);
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves('token');

      const res = await request(app)
        .get('/vessel/master-data')
        .set('Authorization', `Bearer ${bearerToken}`); // Mock token for authentication

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockVesselData);
    });

    it('should return 500 if the service throws an error', async () => {
      // Stub the service function to throw an error
      sinon
        .stub(VesselMasterDataService, 'getVesselMasterData')
        .rejects(new Error('Service error'));

      const res = await request(app)
        .get('/vessel/master-data')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(500);
      expect(res.body).to.have.property('message', 'Internal server error');
    });
  });

  describe('GET /api/vessel/user-details', () => {
    it('should fetch user details successfully and return 200', async () => {
      const mockUserDetails = [
        {
          id: 'fe139a24-4ea3-4a80-ba97-005c349a555d',
          username: 'user1',
          email: '<EMAIL>',
        },
        {
          id: 'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
          username: 'user2',
          email: '<EMAIL>',
        },
      ];

      // Stub the service function to return mock user details
      sinon
        .stub(VesselMasterDataService, 'getUserDetails')
        .resolves(mockUserDetails);

      const res = await request(app)
        .get(
          '/vessel/user-details?userId=fe139a24-4ea3-4a80-ba97-005c349a555d&userId=d7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
        )
        .set('Authorization', `Bearer ${bearerToken}`); // Mock token for authentication

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockUserDetails);
    });

    it('should return 500 if the service throws an error', async () => {
      // Stub the service function to throw an error
      sinon
        .stub(VesselMasterDataService, 'getUserDetails')
        .rejects(new Error('Service error'));

      const res = await request(app)
        .get(
          '/vessel/user-details?userId=fe139a24-4ea3-4a80-ba97-005c349a555d&userId=d7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
        )
        .set('Authorization', `Bearer ${bearerToken}`); // Mock token for authentication

      expect(res.status).to.equal(500);
      expect(res.body).to.have.property('message', 'Internal server error');
    });

    it('should handle missing userId by passing [undefined] to the service', async () => {
      const mockUserDetails = [
        {
          id: 'fe139a24-4ea3-4a80-ba97-005c349a555d',
          username: 'user1',
          email: '<EMAIL>',
        },
        {
          id: 'd7aef6e0-8cbd-489e-9dd5-97b280f66ca5',
          username: 'user2',
          email: '<EMAIL>',
        },
      ];
      // Stub the service function to return mock user details
      sinon
        .stub(VesselMasterDataService, 'getUserDetails')
        .resolves(mockUserDetails);

      const res = await request(app)
        .get('/vessel/user-details?userId=fe139a24-4ea3-4a80-ba97-005c349a555d')
        .set('Authorization', `Bearer ${bearerToken}`); // Mock token for authentication

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockUserDetails);
    });
  });
});
