import request from 'supertest';
import sinon from 'sinon';
import {expect} from 'chai';
import app from '../acceptance/test-helper';
import * as hullService from '../../services/hulls.services';
import {
  hulltypeData,
  mockHullResponse,
  twoYearsAgo,
  updateHullInputData,
  yesterday,
  hullInput,
} from '../data/hull.type.data';
import {bearerToken} from '../data/auth-user.data';
import {createRepositories} from '../../repositories';
import {CustomError} from '../../utils';
import * as models from '../../models';
import {HullInstance} from '../../models/hull.model';
import {Models} from '../../models/registerModels';
import * as requestContext from '../../utils/request-context';
import * as databaseConfig from '../../configs/database.config';

describe('Hulls Controller', () => {
  let repositories: ReturnType<typeof createRepositories>;

  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
    repositories = createRepositories(models as unknown as Models);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /hulls', () => {
    it('should create a hull and return 201 with created hull', async () => {
      sinon
        .stub(hullService, 'createHull')
        .resolves(hulltypeData[0] as unknown as HullInstance);

      const res = await request(app)
        .post('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(hulltypeData[0]);
      expect(res.body).to.deep.equal(hulltypeData[0]);
    });

    it('should return 400 if there is an error creating a hull', async () => {
      sinon
        .stub(repositories.projectRepository, 'findById')
        .resolves({id: 1} as any);
      sinon.stub(repositories.hullRepository, 'findAll').resolves([]);
      sinon
        .stub(hullService, 'createHull')
        .rejects(new Error('Something went wrong'));

      const response = await request(app)
        .post('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(hulltypeData[0]);

      expect(response.status).to.equal(400);
    });

    it('should throw error if data is back date', async () => {
      const mockHull = {
        ...hulltypeData[0],
        keelLaidDate: '2023-04-30',
      };

      const response = await request(app)
        .post('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockHull);
      expect(response.status).to.equal(400);
    });
  });

  describe('GET /hulls', () => {
    it('should fetch all hulls successfully', async () => {
      const mockHulls = {
        data: [hulltypeData[0] as unknown as HullInstance],
        pagination: {
          totalItems: 2,
          totalPages: 1,
          page: 1,
          pageSize: 10,
        },
      };

      sinon.stub(hullService, 'getAllHulls').resolves(mockHulls);

      const response = await request(app)
        .get('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockHulls);
    });

    it('should return 500 if there is an error fetching hulls', async () => {
      sinon
        .stub(hullService, 'getAllHulls')
        .rejects(new Error('Service error'));

      const response = await request(app)
        .get('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(500);
      expect(response.body).to.deep.equal({
        error: 'Error retrieving hulls for selected filter',
      });
    });

    it('should return hulls based on search query', async () => {
      const getAllHullsStub = sinon.stub(hullService, 'getAllHulls');
      getAllHullsStub.resolves(mockHullResponse);

      const searchQuery = 'HULL';

      const res = await request(app)
        .get('/hulls')
        .set('Authorization', `Bearer ${bearerToken}`)
        .query({search: searchQuery});

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockHullResponse);
      expect(getAllHullsStub.calledOnce).to.be.true;
      expect(getAllHullsStub.firstCall.args[0]).to.include({
        search: searchQuery,
      });
    });
  });

  describe('GET /hulls/:id', () => {
    it('should fetch a hull by ID successfully', async () => {
      const mockHull = {
        ...hullInput,
        id: 1,
        tenantId: 1,
        deliveryDate: hullInput.deliveryDate.toISOString(),
        keelLaidDate: hullInput.keelLaidDate.toISOString(),
        launchDate: hullInput.launchDate.toISOString(),
        seaTrialDate: hullInput.seaTrialDate.toISOString(),
        steelCuttingDate: hullInput.steelCuttingDate.toISOString(),
      } as any;
      sinon.stub(hullService, 'findByIdHull').resolves(mockHull);

      const response = await request(app)
        .get('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockHull);
    });

    it('should return 404 if the hull is not found', async () => {
      sinon
        .stub(hullService, 'findByIdHull')
        .throws(new CustomError('Hull id not found', 404));

      const response = await request(app)
        .get('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`);
      expect(response.status).to.equal(404);
    });

    it('should return 400 if there is an error fetching the hull', async () => {
      sinon
        .stub(hullService, 'findByIdHull')
        .rejects(new Error('Service error'));

      const response = await request(app)
        .get('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });

  describe('PUT /hulls/:id', () => {
    it('should update a hull successfully', async () => {
      const mockUpdatedHull = updateHullInputData as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      sinon.stub(hullService, 'updateHull').resolves(mockUpdatedHull);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockUpdatedHull);
    });

    it('should return 404 if the hull to update is not found', async () => {
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon.stub().resolves(),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);
      const mockUpdatedHull = updateHullInputData as unknown as HullInstance;

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(404);
    });

    it('should return 400 if there is an error updating the hull', async () => {
      const mockUpdatedHull = updateHullInputData as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      sinon.stub(hullService, 'updateHull').rejects(new Error('Service error'));

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);
      expect(response.status).to.equal(400);
    });

    it('should return 400 if deliveryDate is past date when updating the hull', async () => {
      const mockUpdatedHull = {
        ...updateHullInputData,
        deliveryDate: yesterday,
      } as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(400);
      expect(response.body.details[0].message).to.include(
        '"deliveryDate" must be after',
      );
    });

    it('should return 400 if seaTrialDate is past date when updating the hull', async () => {
      const mockUpdatedHull = {
        ...updateHullInputData,
        seaTrialDate: yesterday,
      } as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(400);
      expect(response.body.details[0].message).to.include(
        '"seaTrialDate" must be after',
      );
    });
    it('should return 400 if launchDate is past date when updating the hull', async () => {
      const mockUpdatedHull = {
        ...updateHullInputData,
        launchDate: yesterday,
      } as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(400);
      expect(response.body.details[0].message).to.include(
        '"launchDate" must be after',
      );
    });
    it('should return 400 if keelLaidDate is past date when updating the hull', async () => {
      const mockUpdatedHull = {
        ...updateHullInputData,
        keelLaidDate: twoYearsAgo,
      } as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(400);
      expect(response.body.details[0].message).to.include(
        '"keelLaidDate" must be after',
      );
    });
    it('should return 400 if steelCuttingDate is past date when updating the hull', async () => {
      const mockUpdatedHull = {
        ...updateHullInputData,
        steelCuttingDate: twoYearsAgo,
      } as unknown as HullInstance;

      // Mock the middleware's hull check
      const mockContext = {
        repositories: {
          hullRepository: {
            findById: sinon
              .stub()
              .resolves({dataValues: {createdAt: new Date()}}),
          },
        },
      };
      sinon
        .stub(requestContext, 'getRequestContext')
        .returns(mockContext as any);

      const response = await request(app)
        .put('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockUpdatedHull);

      expect(response.status).to.equal(400);
      expect(response.body.details[0].message).to.include(
        '"steelCuttingDate" must be after',
      );
    });
  });

  describe('DELETE /hulls/:id', () => {
    it('should delete a hull successfully', async () => {
      sinon.stub(hullService, 'deleteHull').resolves();

      const response = await request(app)
        .delete('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal({
        message: 'Hull deleted successfully',
      });
    });

    it('should return 400 if there is an error deleting the hull', async () => {
      sinon
        .stub(hullService, 'deleteHull')
        .rejects(new Error('Something went wrong'));

      const response = await request(app)
        .delete('/hulls/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });
});
