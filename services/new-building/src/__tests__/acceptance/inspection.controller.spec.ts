import request from 'supertest';
import sinon from 'sinon';
import {expect} from 'chai';
import {inspectionsService} from '../../services';
import app from './test-helper';
import {bearerToken} from '../data/auth-user.data';
import {CustomError} from '../../utils';
import {error} from 'console';
import * as databaseConfig from '../../configs/database.config';

describe('Inspection Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });
  afterEach(() => {
    sinon.restore();
  });

  describe('POST /inspections', () => {
    it('should save inspections successfully and return the result', async () => {
      const mockRequestBody = {
        fileKey: 'folder/inspection.xlsx',
        projectId: 1,
        hullId: 1,
      };

      const mockResult = {
        error: false,
        message: 'Inspections saved successfully',
      };

      // Stub the inspectionsService.createInspectionFromExcel method
      sinon
        .stub(inspectionsService, 'createInspectionFromExcel')
        .resolves(mockResult as any);

      const response = await request(app)
        .post('/inspections')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should handle errors and return 400 if the service throws an error', async () => {
      const mockRequestBody = {
        fileKey: 'folder/inspection.xlsx',
        projectId: 1,
        hullId: 1,
      };

      // Stub the inspectionsService.createInspectionFromExcel method to throw an error
      sinon
        .stub(inspectionsService, 'createInspectionFromExcel')
        .rejects(new Error('Internal server error'));

      const response = await request(app)
        .post('/inspections')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(400);
    });
  });

  describe('GET /inspections', () => {
    it('should fetch all inspections successfully and return the result', async () => {
      const mockQuery = {
        page: 1,
        limit: 10,
      };

      const mockResult = {
        data: [
          {
            id: 1,
            hullId: 1,
            projectId: 1,
            submissionDate: '2025-05-01',
            dueDate: '2025-05-10',
            time: '10:00:00',
            description: 'Inspection 1',
            discipline: 'Mechanical',
            forOwner: true,
            forClass: false,
            remark: 'Test remark',
            status: 1,
          },
        ],
        pagination: {
          totalItems: 1,
          totalPages: 1,
          page: 1,
          pageSize: 10,
        },
      };

      // Stub the inspectionsService.getAllInspections method
      sinon
        .stub(inspectionsService, 'getAllInspections')
        .resolves(mockResult as any);

      const response = await request(app)
        .get('/inspections')
        .set('Authorization', `Bearer ${bearerToken}`)
        .query(mockQuery);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should handle errors and return 400 if the service throws an error', async () => {
      const mockQuery = {
        page: 1,
        limit: 10,
      };

      sinon
        .stub(inspectionsService, 'getAllInspections')
        .rejects(new Error('Internal server error'));

      const response = await request(app)
        .get('/inspections')
        .set('Authorization', `Bearer ${bearerToken}`)
        .query(mockQuery);

      expect(response.status).to.equal(400);
    });
  });

  describe('DELETE /inspections/:id', () => {
    it('should delete an inspection successfully and return a success message', async () => {
      const mockInspectionId = 1;
      const mockResult = {
        error: false,
        message: 'Inspection deleted successfully',
      };

      // Stub the inspectionsService.deleteInspection method
      sinon
        .stub(inspectionsService, 'deleteInspection')
        .resolves(mockResult as any);

      const response = await request(app)
        .delete(`/inspections/${mockInspectionId}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should return 404 if the inspection is not found', async () => {
      const mockInspectionId = 999;

      // Stub the inspectionsService.deleteInspection method to throw a 404 error
      sinon
        .stub(inspectionsService, 'deleteInspection')
        .rejects(new CustomError('Inspection not found', 404));

      const response = await request(app)
        .delete(`/inspections/${mockInspectionId}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(404);
    });

    it('should return 400 if an unexpected error occurs', async () => {
      const mockInspectionId = 1;

      // Stub the inspectionsService.deleteInspection method to throw a generic error
      sinon
        .stub(inspectionsService, 'deleteInspection')
        .rejects(new Error('Internal server error'));

      const response = await request(app)
        .delete(`/inspections/${mockInspectionId}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });

  describe('GET /inspections/count', () => {
    it('should return the count of inspections grouped by status', async () => {
      const mockResult = {
        error: false,
        message: 'Get count successfully',
        data: {
          ongoing: 10,
          closed: 5,
        },
      };

      // Stub the inspectionsService.countInspectionByStatus method
      sinon
        .stub(inspectionsService, 'countInspectionByStatus')
        .resolves(mockResult as any);

      const response = await request(app)
        .get('/inspections/count')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should return 400 if an unexpected error occurs', async () => {
      // Stub the inspectionsService.countInspectionByStatus method to throw an error
      sinon
        .stub(inspectionsService, 'countInspectionByStatus')
        .rejects(new Error('Internal server error'));

      const response = await request(app)
        .get('/inspections/count')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });

  describe('GET /inspections/:id', () => {
    const mockInspection = {
      error: false,
      message: 'Inspection found successfully',
      data: {
        id: 123,
        project: {id: 1, name: 'Project A'},
        hull: {id: 2, hullNo: 'HULL-001'},
      },
    } as any;

    beforeEach(() => {
      sinon
        .stub(inspectionsService, 'findInspectionById')
        .resolves(mockInspection);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return 200 and the inspection data', async () => {
      const res = await request(app)
        .get('/inspections/123')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockInspection);
    });

    it('should return 404 if inspection is not found', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      sinon
        .stub(inspectionsService, 'findInspectionById')
        .throws(new CustomError('Inspection not found id 123', 404));

      const res = await request(app)
        .get('/inspections/123')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(404);
      expect(res.body).to.have.property('status');
    });

    it('should return 500 for unhandled errors', async () => {
      sinon.restore();
      sinon.stub(databaseConfig, 'getTenantDb').resolves({
        sequelize: {} as any,
        models: {} as any,
      });
      sinon
        .stub(inspectionsService, 'findInspectionById')
        .throws(new CustomError('Unexpected error', 500));

      const res = await request(app)
        .get('/inspections/123')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(500);
      expect(res.body).to.have.property('status');
    });
  });

  describe('GET /inspections/options/:key', () => {
    it('should fetch unique values for a valid key successfully', async () => {
      const mockKey = 'discipline';
      const mockResult = {
        error: false,
        message: `Unique values for key '${mockKey}' fetched successfully`,
        data: ['Mechanical', 'Electrical', 'Civil'],
      };

      // Stub the drawingsService.getOptionsByKey method
      sinon
        .stub(inspectionsService, 'getOptionsByKey')
        .resolves(mockResult as any);

      const response = await request(app)
        .get(`/inspections/options/${mockKey}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResult);
    });

    it('should return 400 if the key is invalid', async () => {
      const mockKey = 'invalidKey';
      sinon
        .stub(inspectionsService, 'getOptionsByKey')
        .rejects(new Error('Invalid key: invalidKey'));

      const response = await request(app)
        .get(`/inspections/options/${mockKey}`)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(400);
    });
  });
});
