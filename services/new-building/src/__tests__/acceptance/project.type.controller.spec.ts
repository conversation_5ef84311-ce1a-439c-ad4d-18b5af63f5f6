import {expect} from 'chai';
import sinon from 'sinon';
import request from 'supertest';
import * as projectTypeService from '../../services/project.type.service';
import app from './test-helper';
import {projectTypeData} from '../data/project.type.data';
import {bearerToken} from '../data/auth-user.data';
import ErrorMessages from '../../utils/error.messages.utils';
import {ProjectTypeInstance} from '../../models';
import * as databaseConfig from '../../configs/database.config';

describe('Project Type Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore(); // Restore the original behavior of all stubbed methods
  });

  describe('GET /projectType', () => {
    it('should return a list of project types with status 200', async () => {
      const mockProjectTypes =
        projectTypeData as unknown as ProjectTypeInstance[];
      sinon
        .stub(projectTypeService, 'getAllProjectTypes')
        .resolves(mockProjectTypes);

      const response = await request(app)
        .get('/project-type')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockProjectTypes);
    });

    it('should return a 500 error if the service throws an error', async () => {
      sinon
        .stub(projectTypeService, 'getAllProjectTypes')
        .rejects(new Error('Service error'));

      const response = await request(app)
        .get('/project-type')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property(
        'error',
        ErrorMessages.errorFetching('Project types'),
      );
      expect(response.body).to.have.property('error');
    });
  });
});
