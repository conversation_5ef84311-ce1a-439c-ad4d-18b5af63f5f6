import request from 'supertest';
import {expect} from 'chai';
import sinon from 'sinon';
import {observationService} from '../../services';
import {bearerToken} from '../data/auth-user.data';
import app from './test-helper';
import * as databaseConfig from '../../configs/database.config';

describe('Observations Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /api/observations', () => {
    it('should create a new observation successfully', async () => {
      const mockRequestBody = {
        inspectionId: 1,
        assets: [
          {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'Image'},
          {fileKey: 'file2.jpg', assetName: 'Asset 2', assetType: 'Image'},
        ],
      };

      const mockResponse = [
        {
          id: 1,
          assetName: 'Asset 1',
          referenceId: 1,
          referenceType: 'INSPECTION',
          assetPath: 'file1.jpg',
          assetType: 'Image',
          assetVersion: 1,
          createdBy: 'user123',
        },
      ];

      sinon
        .stub(observationService, 'createObservation')
        .resolves(mockResponse as any);

      const response = await request(app)
        .post('/observations')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(201);
      expect(response.body.message).to.equal(
        'Observation created successfully',
      );
      expect(response.body.error).to.be.false;
      expect(response.body.message).to.deep.equal(
        'Observation created successfully',
      );
    });

    it('should return 422 if request payload is invalid', async () => {
      const mockRequestBody = {
        assets: [
          {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'Image'},
        ],
      };

      const response = await request(app)
        .post('/observations')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);
      expect(response.status).to.equal(422);
    });

    it('should return 400 if service throws an error', async () => {
      const mockRequestBody = {
        inspectionId: 1,
        assets: [
          {fileKey: 'file1.jpg', assetName: 'Asset 1', assetType: 'Image'},
        ],
      };

      sinon
        .stub(observationService, 'createObservation')
        .rejects(new Error('Internal Server Error'));

      const response = await request(app)
        .post('/observations')
        .set('Authorization', `Bearer ${bearerToken}`)
        .send(mockRequestBody);

      expect(response.status).to.equal(400);
    });
  });
});
