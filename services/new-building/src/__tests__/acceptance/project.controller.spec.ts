import {expect} from 'chai';
import sinon from 'sinon';
import request from 'supertest';
import * as projectService from '../../services/projects.service';
import {ProjectStatus} from '../../enums';
import {newProject, projectData} from '../data/project.data';
import ErrorMessages from '../../utils/error.messages.utils';
import {
  bearerToken,
  bearerTokenInvalid,
  bearerTokenWithoutTenant,
  bearerTokenWithTenantWithoutPermission,
} from '../data/auth-user.data';
import app from './test-helper';
import {ProjectInstance} from '../../models';
import * as databaseConfig from '../../configs/database.config';

// Mocks
const createProjectStub = sinon.stub();

afterEach(() => {
  sinon.restore();
  createProjectStub.reset();
});

describe('POST /projects', () => {
  let createProjectStub: sinon.SinonStub;

  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });

    createProjectStub = sinon.stub(projectService, 'createProject');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should create a project and return 201', async () => {
    const createdProject = {...newProject, id: 1};
    createProjectStub.resolves(createdProject);

    const res = await request(app)
      .post('/projects')
      .send(newProject)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(201);
    expect(res.body).to.deep.equal(createdProject);
  });

  it('should create a project without asset and return 201', async () => {
    const {assetName, assetType, assetPath, ...input} = newProject;

    const createdProject = {...input, id: 2};
    createProjectStub.resolves(createdProject);

    const res = await request(app)
      .post('/projects')
      .send(input)
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(201);
    expect(res.body).to.deep.equal(createdProject);
  });

  it('should create a project without asset and without hull return 201', async () => {
    const {assetName, assetType, assetPath, hulls, ...input} = newProject;

    const createdProject = {...input, id: 2};
    createProjectStub.resolves(createdProject);

    const res = await request(app)
      .post('/projects')
      .send(input)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(201);
    expect(res.body).to.deep.equal(createdProject);
  });

  it('should return 400 on service error', async () => {
    const error = new Error('Something went wrong');
    createProjectStub.rejects(error);

    const res = await request(app)
      .post('/projects')
      .send(newProject)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(400);
  });

  it('should return 401 when token not sent', async () => {
    const res = await request(app).post('/projects').send(newProject);
    expect(res.status).to.equal(401);
  });

  it('should return 403 when tenantId not sent', async () => {
    const res = await request(app)
      .post('/projects')
      .send(newProject)
      .set('Authorization', `Bearer ${bearerTokenWithoutTenant}`);

    expect(res.status).to.equal(403);
  });

  it('should return 403 when permission not provided', async () => {
    const res = await request(app)
      .post('/projects')
      .send(newProject)
      .set('Authorization', `Bearer ${bearerTokenWithTenantWithoutPermission}`);

    expect(res.status).to.equal(403);
  });

  it('should return 403 when invalid token provided', async () => {
    const res = await request(app)
      .post('/projects')
      .send(newProject)
      .set('Authorization', `Bearer ${bearerTokenInvalid}`);

    expect(res.status).to.equal(403);
  });
});

describe('GET /api/projects', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  it('should return project list with pagination', async () => {
    sinon.stub(projectService, 'getAllProjects').resolves({
      data: [
        projectData[0] as unknown as ProjectInstance,
        projectData[1] as unknown as ProjectInstance,
      ],
      pagination: {
        totalItems: 2,
        totalPages: 1,
        page: 1,
        pageSize: 10,
      },
    });
    const res = await request(app)
      .get('/projects')
      .query({fields: ['name', 'owner', 'status'], page: 1, limit: 10})
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(200);
    expect(res.body.data).to.be.an('array').with.lengthOf(2);
    expect(res.body.pagination).to.deep.equal({
      totalItems: 2,
      totalPages: 1,
      page: 1,
      pageSize: 10,
    });
  });
  it('should return 500 on service error', async () => {
    sinon.stub(projectService, 'getAllProjects').rejects(new Error('DB error'));
    const res = await request(app)
      .get('/projects')
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(500);
    expect(res.body).to.have.property(
      'error',
      ErrorMessages.errorFetching('projects for selected filter'),
    );
  });
});
describe('GET /api/projects/:id', () => {
  let findByIdStub: sinon.SinonStub;
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });

    findByIdStub = sinon.stub(projectService, 'findByIdProject');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return a project when found', async () => {
    const mockProject = {
      id: 1,
      name: 'Titanic',
      owner: 'Jack',
      status: 'active',
    };
    findByIdStub.resolves(mockProject);
    const res = await request(app)
      .get('/projects/1')
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal(mockProject);
    expect(findByIdStub.calledOnceWith(1)).to.be.true;
  });

  it('should return 400 on service error', async () => {
    findByIdStub.rejects(new Error('DB Error'));
    const res = await request(app)
      .get('/projects/1')
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(400);
    expect(res.body).to.have.property('status');
  });
});

describe('PUT /projects/:id', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  const projectId = 1;
  const updateData = {
    name: 'Updated Project Name',
    projectDescription: 'Updated project description.',
    owner: 'Updated Owner',
    shipType: 'Updated Ship Type',
    fuelTypeId: 2,
    engineType: 'Updated Engine Type',
    projectTypeId: 3,
  };

  it('should update a project and return 200', async () => {
    const existingProject = {
      id: projectId,
      name: 'Old Project Name',
      projectDescription: 'Old project description.',
      status: ProjectStatus.ON_GOING,
    } as unknown as ProjectInstance;

    const updatedProject = {
      ...existingProject,
      ...updateData,
    };

    sinon
      .stub(projectService, 'updateProject')
      .resolves(updatedProject as unknown as ProjectInstance);

    const res = await request(app)
      .put(`/projects/${projectId}`)
      .send(updateData)
      .set('Authorization', `Bearer ${bearerToken}`);
      console.log({res});

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal(updatedProject);
  });

  it('should return 404 if the project does not exist', async () => {
    sinon.stub(projectService, 'updateProject').resolves(null);

    const res = await request(app)
      .put(`/projects/${projectId}`)
      .send(updateData)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(res.status).to.equal(404);
    expect(res.body.error).to.equal(ErrorMessages.notFound('projects'));
  });

  it('should return 400 on service error', async () => {
    sinon
      .stub(projectService, 'updateProject')
      .rejects(new Error('Service error'));

    const res = await request(app)
      .put(`/projects/${projectId}`)
      .send(updateData)
      .set('Authorization', `Bearer ${bearerToken}`);
    expect(res.status).to.equal(400);
    expect(res.body.message).to.equal(
      'There is some error when updating the project',
    );
  });

  describe('Delete /api/projects/:id', () => {
    let deleteByIdStub: sinon.SinonStub;

    beforeEach(() => {
      deleteByIdStub = sinon.stub(projectService, 'deleteProject');
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return a project when found', async () => {
      const res = await request(app)
        .delete('/projects/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal({message: 'Project deleted successfully'});
      expect(deleteByIdStub.calledOnceWith(1)).to.be.true;
    });

    it('should return 400 on service error', async () => {
      deleteByIdStub.rejects(new Error('DB Error'));

      const res = await request(app)
        .delete('/projects/1')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(400);
    });
  });
});

describe('Complete project', () => {
  let completeProjectStub: sinon.SinonStub;

  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });

    completeProjectStub = sinon.stub(projectService, 'completeProject');
    sinon.stub().callsFake((permissions: string[]) => {
      return (req: any, res: any, next: any) => {
        req.user = {
          user_id: 'test-user',
          tenantId: 1,
          permissions,
        };
        next();
      };
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should complete the project successfully and return 200', async () => {
    const projectId = 123;
    const requestBody = {
      completionDate: '2025-04-29T00:00:00Z',
      expenses: 5000,
      currency: 'INR',
    };

    const mockResponse = {
      message: 'Project completed successfully',
      projectId,
      error: false,
    };

    completeProjectStub.resolves(mockResponse);

    const response = await request(app)
      .put(`/projects/${projectId}/complete`)
      .send(requestBody)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal(mockResponse);
  });

  it('should return 400 if validation fails', async () => {
    const projectId = 123;
    const invalidRequestBody = {
      expenses: 5000, // Missing required fields like completionDate
    };

    const response = await request(app)
      .put(`/projects/${projectId}/complete`)
      .send(invalidRequestBody)
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(400);
    expect(response.body.status).to.equal('error');
  });

  it('should handle errors and call handleErrors when an exception occurs', async () => {
    const projectId = 123;
    const requestBody = {
      completionDate: '2025-04-29T00:00:00Z',
      expenses: 5000,
      currency: 'INR',
    };

    const error = new Error('Service error');
    completeProjectStub.rejects(error);

    const response = await request(app)
      .put(`/projects/${projectId}/complete`)
      .send(requestBody)
      .set('Authorization', `Bearer ${bearerToken}`);

    // Assertions
    expect(response.status).to.equal(400);
    expect(completeProjectStub.calledOnceWith(projectId, sinon.match.any)).to.be
      .true;
  });
});

describe('GET /api/projects/count', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  it('should return the count of onGoing and closed projects', async () => {
    sinon.stub(projectService, 'countProjectsByStatus').resolves({
      onGoing: 10,
      closed: 5,
    });

    const response = await request(app)
      .get('/projects/count')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(200);
    expect(response.body).to.deep.equal({
      onGoing: 10,
      closed: 5,
    });
  });

  it('should return 500 if the service throws an error', async () => {
    sinon
      .stub(projectService, 'countProjectsByStatus')
      .rejects(new Error('Service error'));

    const response = await request(app)
      .get('/projects/count')
      .set('Authorization', `Bearer ${bearerToken}`);

    expect(response.status).to.equal(500);
  });
});
