import {expect} from 'chai';
import sinon from 'sinon';
import request from 'supertest';
import * as fuelTypeService from '../../services/fuel.type.service';
import app from './test-helper';
import {fuelTypeData} from '../data/fuel.type.data';
import {bearerToken} from '../data/auth-user.data';
import ErrorMessages from '../../utils/error.messages.utils';
import {FuelTypeInstance} from '../../models';
import * as databaseConfig from '../../configs/database.config';

describe('Fuel Type Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore(); // Restore the original behavior of all stubbed methods
  });

  describe('GET /fuel-type', () => {
    it('should return a list of Fuel types with status 200', async () => {
      const mockFuelTypes = fuelTypeData as unknown as FuelTypeInstance[];
      sinon.stub(fuelTypeService, 'getAllFuelTypes').resolves(mockFuelTypes);

      const response = await request(app)
        .get('/fuel-type')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockFuelTypes);
    });

    it('should return a 500 error if the service throws an error', async () => {
      sinon
        .stub(fuelTypeService, 'getAllFuelTypes')
        .rejects(new Error('Service error'));

      const response = await request(app)
        .get('/fuel-type')
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property(
        'error',
        ErrorMessages.errorFetching('Fuel types'),
      );
      expect(response.body).to.have.property('error');
    });
  });
});
