import {expect} from 'chai';
import sinon from 'sinon';
import request from 'supertest';
import {cronService} from '../../services';
import app from './test-helper';
import {bearerToken} from '../data/auth-user.data';
import * as databaseConfig from '../../configs/database.config';

describe('Crons Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /image-thumbnail', () => {
    it('should trigger the generation of image thumbnails successfully', async () => {
      const processThumbnailTransactionsStub = sinon
        .stub(cronService, 'processThumbnailTransactions')
        .resolves();

      const response = await request(app)
        .post('/crons/image-thumbnail')
        .set('Authorization', `Bearer ${bearerToken}`);

      // Assertions
      expect(response.status).to.equal(200);
      expect(processThumbnailTransactionsStub.calledOnce).to.be.true;
    });

    it('should log an error if thumbnail generation fails', async () => {
      const processThumbnailTransactionsStub = sinon
        .stub(cronService, 'processThumbnailTransactions')
        .rejects(new Error('Thumbnail generation error'));

      const consoleErrorStub = sinon.stub(console, 'error');

      const response = await request(app)
        .post('/crons/image-thumbnail')
        .set('Authorization', `Bearer ${bearerToken}`);

      // Assertions
      expect(response.status).to.equal(200); // Endpoint always returns 200
      expect(processThumbnailTransactionsStub.calledOnce).to.be.true;
      expect(consoleErrorStub.calledOnce).to.be.true;
      expect(consoleErrorStub.firstCall.args[0]).to.equal(
        'Error generating thumbnail:',
      );
      expect(consoleErrorStub.firstCall.args[1]).to.be.instanceOf(Error);
      expect(consoleErrorStub.firstCall.args[1].message).to.equal(
        'Thumbnail generation error',
      );
    });

    it('should handle missing Authorization header and return 401', async () => {
      const response = await request(app)
        .post('/crons/image-thumbnail');
      expect(response.status).to.equal(401);
    });
  });

  describe('POST /tenant-migrate', () => {
    it('should handle invalid JSON body gracefully', async () => {
      const response = await request(app)
        .post('/crons/tenant-migrate')
        .set('Content-Type', 'application/json')
        .send('invalid-json');
      expect(response.status).to.be.oneOf([200, 400]);
    });
  });
});
