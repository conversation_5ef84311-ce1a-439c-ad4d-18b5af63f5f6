import request from 'supertest';
import sinon from 'sinon';
import {expect} from 'chai';
import app from './test-helper';
import {mockComments, existingComment} from '../data/comments.data';
import {
  bearerToken,
  bearerTokenInvalid,
  bearerTokenWithoutTenant,
  bearerTokenWithTenantWithoutPermission,
} from '../data/auth-user.data';
import * as commentService from '../../services/comments.services';
import {CommentStatus, CommentType} from '../../enums';
import * as databaseConfig from '../../configs/database.config';

describe('Comments Controller', () => {
  beforeEach(() => {
    // Mock getTenantDb to avoid actual database connections
    sinon.stub(databaseConfig, 'getTenantDb').resolves({
      sequelize: {} as any,
      models: {} as any,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('POST /api/comments', () => {
    let createCommentStub: sinon.SinonStub;

    const newComment = {
      commentType: CommentType.DRAWING,
      drawingListId: 1,
      comments: 'Sample comment',
    };

    beforeEach(() => {
      createCommentStub = sinon.stub(commentService, 'createComment');
    });

    it('should create a comment and return 201', async () => {
      const createdComment = {...newComment, id: 1};
      createCommentStub.resolves(createdComment);

      const res = await request(app)
        .post('/comments')
        .send(newComment)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(201);
      expect(res.body).to.deep.equal({
        message: 'Comment created successfully',
        data: createdComment,
      });
    });

    it('should return 400 on service error', async () => {
      createCommentStub.rejects(new Error('Something went wrong'));

      const res = await request(app)
        .post('/comments')
        .send(newComment)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(400);
    });

    it('should return 401 when token not sent', async () => {
      const res = await request(app).post('/comments').send(newComment);
      expect(res.status).to.equal(401);
    });

    it('should return 403 when tenantId not sent', async () => {
      const res = await request(app)
        .post('/comments')
        .send(newComment)
        .set('Authorization', `Bearer ${bearerTokenWithoutTenant}`);

      expect(res.status).to.equal(403);
    });

    it('should return 403 when permission not provided', async () => {
      const res = await request(app)
        .post('/comments')
        .send(newComment)
        .set(
          'Authorization',
          `Bearer ${bearerTokenWithTenantWithoutPermission}`,
        );

      expect(res.status).to.equal(403);
    });

    it('should return 403 when invalid token provided', async () => {
      const res = await request(app)
        .post('/comments')
        .send(newComment)
        .set('Authorization', `Bearer ${bearerTokenInvalid}`);

      expect(res.status).to.equal(403);
    });
  });

  describe('GET /comments', () => {
    let getAllCommentsStub: sinon.SinonStub;

    const queryParams = {
      commentType: CommentType.DRAWING,
      drawingListId: 1,
      sortBy: 'createdAt',
      sortOrder: 'DESC',
    };

    beforeEach(() => {
      getAllCommentsStub = sinon.stub(commentService, 'getAllComments');
    });

    it('should return 200 and a list of comments', async () => {
      getAllCommentsStub.resolves(mockComments);

      const res = await request(app)
        .get('/comments')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal(mockComments);
    });

    it('should return 400 if getAllComments throws an error', async () => {
      getAllCommentsStub.rejects(new Error('Something went wrong'));

      const res = await request(app)
        .get('/comments')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(400);
    });

    it('should return 401 when no token is provided', async () => {
      const res = await request(app).get('/comments').query(queryParams);
      expect(res.status).to.equal(401);
    });

    it('should return 403 when token without tenant is used', async () => {
      const res = await request(app)
        .get('/comments')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerTokenWithoutTenant}`);

      expect(res.status).to.equal(403);
    });

    it('should return 403 when token without permission is used', async () => {
      const res = await request(app)
        .get('/comments')
        .query(queryParams)
        .set(
          'Authorization',
          `Bearer ${bearerTokenWithTenantWithoutPermission}`,
        );

      expect(res.status).to.equal(403);
    });

    it('should return 403 when an invalid token is provided', async () => {
      const res = await request(app)
        .get('/comments')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerTokenInvalid}`);

      expect(res.status).to.equal(403);
    });
  });

  describe('PUT /comments/:id', () => {
    let updateCommentStub: sinon.SinonStub;

    const updateBody = {
      commentType: CommentType.DRAWING,
      status: CommentStatus.CLOSED,
    };

    beforeEach(() => {
      updateCommentStub = sinon.stub(commentService, 'updateComment');
    });

    it('should update the comment and return 200', async () => {
      const updatedComment = {...existingComment, ...updateBody};
      updateCommentStub.resolves(updatedComment);

      const res = await request(app)
        .put('/comments/1')
        .send(updateBody)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal({
        message: 'Comment updated successfully',
        data: updatedComment,
      });
    });

    it('should return 401 if token is not provided', async () => {
      const res = await request(app).put('/comments/1').send(updateBody);
      expect(res.status).to.equal(401);
    });
  });

  describe('PUT /comments/resolve-all', () => {
    let resolveAllCommentsStub: sinon.SinonStub;

    const queryParams = {
      entityId: 1,
      commentType: CommentType.DRAWING,
    };

    beforeEach(() => {
      resolveAllCommentsStub = sinon.stub(commentService, 'resolveAllComments');
    });

    it('should resolve all comments and return 200', async () => {
      resolveAllCommentsStub.resolves({
        error: false,
        message: 'Comment has been resolved successfully',
      });

      const res = await request(app)
        .put('/comments/resolve-all')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(200);
      expect(res.body).to.deep.equal({
        error: false,
        message: 'Comment has been resolved successfully',
      });
    });

    it('should return 400 if an error occurs', async () => {
      resolveAllCommentsStub.rejects(new Error('Something went wrong'));

      const res = await request(app)
        .put('/comments/resolve-all')
        .query(queryParams)
        .set('Authorization', `Bearer ${bearerToken}`);

      expect(res.status).to.equal(400);
    });
  });
});
