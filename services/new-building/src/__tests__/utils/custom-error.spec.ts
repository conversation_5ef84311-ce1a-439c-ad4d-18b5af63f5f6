import {expect} from 'chai';
import sinon, {SinonStub} from 'sinon';
import {Response} from 'express';
import {CustomError, handleErrors} from '../../utils/custom-error';

describe('handleErrors', () => {
  let statusStub: SinonStub;
  let jsonStub: SinonStub;
  let res: Response;

  beforeEach(() => {
    jsonStub = sinon.stub();
    statusStub = sinon.stub().returns({json: jsonStub});

    // Fully define `res` with only the used methods
    res = {
      status: statusStub,
      json: jsonStub,
    } as unknown as Response;
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should handle a CustomError and return the correct response', () => {
    const error = new CustomError('Custom error occurred', 400);

    handleErrors(error, res, '');

    expect(statusStub.calledOnceWith(400)).to.be.true;
    expect(
      jsonStub.calledOnceWith({
        status: 'error',
        message: 'Custom error occurred',
      }),
    ).to.be.true;
  });

  it('should handle a generic Error and return a 400 response', () => {
    const error = new Error('Generic error occurred');

    handleErrors(error, res, 'saving data');

    expect(statusStub.calledOnceWith(400)).to.be.true;
    expect(
      jsonStub.calledOnceWith({
        status: 'error',
        message: 'There is some error when saving data',
      }),
    ).to.be.true;
  });

  it('should handle an unknown error and return a 500 response', () => {
    const error = 'Unknown error';

    handleErrors(error as unknown as Error, res, '');

    expect(statusStub.calledOnceWith(500)).to.be.true;
    expect(
      jsonStub.calledOnceWith({
        status: 'error',
        message: 'Internal Server Error',
      }),
    ).to.be.true;
  });
});
