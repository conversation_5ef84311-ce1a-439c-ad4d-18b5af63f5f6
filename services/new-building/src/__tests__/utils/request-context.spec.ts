import { expect } from 'chai';
import { requestContext, getRequestContext, ContextType } from '../../utils/request-context';

describe('request-context', () => {
  it('should get and set context using AsyncLocalStorage', () => {
    const mockContext = {
      models: {},
      repositories: {},
      sequelize: {},
      user: { user_id: 1 },
    } as unknown as ContextType;

    let contextFromStore: ContextType | undefined;
    requestContext.run(mockContext, () => {
      contextFromStore = getRequestContext();
    });

    expect(contextFromStore).to.equal(mockContext);
  });

  it('should throw if no context is available', () => {
    // Not inside a run() context
    expect(() => getRequestContext()).to.throw('No request context available');
  });
});
