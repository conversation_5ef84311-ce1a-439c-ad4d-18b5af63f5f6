import { expect } from 'chai';
import sinon from 'sinon';
import ExcelJS from 'exceljs';
import {
  processExcelAndCSV,
  getAssetDestinationPath,
  buildSearchBaseFilters,
} from '../../utils/common.utils';
import s3HelperService from '../../services/s3-helper.service';
import { CustomError } from '../../utils/custom-error';
// Import internal helpers for direct testing
import * as commonUtils from '../../utils/common.utils';


describe('processExcelAndCSV', () => {
  afterEach(() => {
    sinon.restore();
  });

  it('should process an Excel file and return parsed data', async () => {
    const mockFileKey = 'folder/sample.xlsx';

    // Create a valid Excel file buffer
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    worksheet.addRow(['<PERSON>', 30, true]);
    worksheet.addRow(['<PERSON>', 25, false]);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    // Stub s3HelperService.getTheFileStream to return a mock stream
    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await processExcelAndCSV(mockFileKey, 1);

    expect(result).to.deep.equal([
      { 'Drawing Name*': 'John Doe', 'Drawing Number*': 30, Discipline: true },
      { 'Drawing Name*': 'Jane Doe', 'Drawing Number*': 25, Discipline: false },
    ]);
  });

  it('should process a CSV file and return parsed data', async () => {
    const mockFileKey = 'folder/sample.csv';
    const mockCSVContent = `Name,Age,Active
John Doe,30,true
Jane Doe,25,false`;

    // Stub s3HelperService.getTheFileStream to return a mock stream
    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(Buffer.from(mockCSVContent));
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await processExcelAndCSV(mockFileKey, 1);

    expect(result).to.deep.equal([
      { Name: 'John Doe', Age: '30', Active: 'true' },
      { Name: 'Jane Doe', Age: '25', Active: 'false' },
    ]);
  });

  it('should throw an error for unsupported file types', async () => {
    const mockCSVContent = `Name,Age,Active
    John Doe,30,true
    Jane Doe,25,false`;
    const mockFileKey = 'folder/sample.txt';
    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(Buffer.from(mockCSVContent));
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);
    try {
      await processExcelAndCSV(mockFileKey, 1);
      expect.fail('Expected error was not thrown');
    } catch (error) {
      expect(error).to.be.instanceOf(CustomError);
      expect((error as CustomError).message).to.equal('Unsupported file type.');
      expect((error as CustomError).code).to.equal(415);
    }
  });

  it('should throw an error if no worksheet is found in the Excel file', async () => {
    const mockFileKey = 'folder/sample.xlsx';

    // Create an empty Excel file buffer
    const workbook = new ExcelJS.Workbook();
    const mockBuffer = await workbook.xlsx.writeBuffer();

    // Stub s3HelperService.getTheFileStream to return a mock stream
    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    try {
      await processExcelAndCSV(mockFileKey, 1);
      expect.fail('Expected error was not thrown');
    } catch (error) {
      expect(error).to.be.instanceOf(Error);
      expect((error as Error).message).to.equal('No worksheet found');
    }
  });

  // Additional coverage for parseExcelFile: missing drawing headers
  it('should throw CustomError if drawing Excel file is missing required headers', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Discipline']); // Missing 'Drawing Number*'
    worksheet.addRow(['John Doe', 'Mech']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    try {
      await processExcelAndCSV(mockFileKey, 1);
      expect.fail('Expected error was not thrown');
    } catch (error) {
      expect(error).to.be.instanceOf(CustomError);
      expect((error as CustomError).message).to.include('Missing header(s): Drawing Number*');
      expect((error as CustomError).code).to.equal(422);
    }
  });

  // Additional coverage for parseExcelFile: missing inspection headers
  it('should throw CustomError if inspection Excel file is missing required headers', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Hull No', 'Submission Date', 'Due Date']); // Missing 'Time', 'Inspection Description', etc.
    worksheet.addRow(['H001', '2023-01-01', '2023-01-02']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    try {
      await processExcelAndCSV(mockFileKey, 2);
      expect.fail('Expected error was not thrown');
    } catch (error) {
      expect(error).to.be.instanceOf(CustomError);
      expect((error as CustomError).message).to.include('Missing header(s): Time');
      expect((error as CustomError).code).to.equal(422);
    }
  });

  // Remove direct convertCellValue tests and instead test via processExcelAndCSV/parseExcelFile

  it('should throw CustomError for invalid date format in Excel cell (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    // Use inspection headers for date/time validation
    worksheet.addRow(['Hull No', 'Submission Date', 'Due Date', 'Time', 'Inspection Description', 'Discipline', 'Owner', 'Class', 'Remark']);
    worksheet.addRow(['H001', 'not-a-date', '2023-01-02', '12:00', 'desc', 'Mech', 'yes', , "remark"]);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    try {
      await commonUtils.processExcelAndCSV(mockFileKey, 2);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomError);
      expect(err.message).to.include('Invalid date format');
      expect(err.code).to.equal(422);
    }
  });

  it('should throw CustomError for invalid time format in Excel cell (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Hull No', 'Submission Date', 'Due Date', 'Time', 'Inspection Description', 'Discipline', 'Owner', 'Class', 'Remark']);
    worksheet.addRow(['H001', '2023-01-01', '2023-01-02', 'notatime', 'desc', 'Mech', 'yes', , 'remark']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    try {
      await commonUtils.processExcelAndCSV(mockFileKey, 2);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomError);
      expect(err.message).to.include('Invalid time format');
      expect(err.code).to.equal(422);
    }
  });

  it('should handle object with text property in Excel cell (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    worksheet.addRow(['abc', 'D001', 'Mech']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 1);
    // Assert type to avoid 'unknown' error
    expect((result as any[])[0]['Drawing Name*']).to.equal('abc');
  });

  it('should handle Date instance in Excel cell (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    const date = new Date('2023-01-01T12:00:00Z');
    worksheet.addRow(['John Doe', 'D001', date]);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 1);
    expect((result as any[])[0]['Discipline']).to.equal(date.toISOString());
  });

  it('should handle primitive value as is if no fieldType (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    worksheet.addRow(['abc', 123, true]);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 1);
    expect((result as any[])[0]['Drawing Name*']).to.equal('abc');
    expect((result as any[])[0]['Drawing Number*']).to.equal(123);
    expect((result as any[])[0]['Discipline']).to.equal(true);
  });


  it('should handle string value as is if fieldType is not set (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    worksheet.addRow(['abc', 'def', 'ghi']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 1);
    expect((result as any[])[0]['Drawing Name*']).to.equal('abc');
    expect((result as any[])[0]['Drawing Number*']).to.equal('def');
    expect((result as any[])[0]['Discipline']).to.equal('ghi');
  });

  it('should handle string value as string if fieldType is string (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.addRow(['Drawing Name*', 'Drawing Number*', 'Discipline']);
    worksheet.addRow(['abc', 'def', 'ghi']);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 1);
    expect((result as any[])[0]['Discipline']).to.equal('ghi');
  });

  it('should handle valid time string and return ISO string (via processExcelAndCSV)', async () => {
    const mockFileKey = 'folder/sample.xlsx';
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    // Use inspection headers for time
    worksheet.addRow(['Hull No', 'Submission Date', 'Due Date', 'Time', 'Inspection Description', 'Discipline', 'Owner', 'Class', 'Remark']);
    worksheet.addRow(['H001', '2023-01-01', '2023-01-02', '12:34', 'desc', 'Mech', "yes", , "remark"]);
    const mockBuffer = await workbook.xlsx.writeBuffer();

    sinon.stub(s3HelperService, 'getTheFileStream').resolves({
      on: sinon.stub().callsFake((event, callback) => {
        if (event === 'data') callback(mockBuffer);
        if (event === 'end') callback();
      }),
    } as unknown as NodeJS.ReadableStream);

    const result = await commonUtils.processExcelAndCSV(mockFileKey, 2);
    expect((result as any[])[0]['Time']).to.be.a('string');
    expect(new Date((result as any[])[0]['Time']).getHours()).to.equal(12);
    expect(new Date((result as any[])[0]['Time']).getMinutes()).to.equal(34);
  });

  // Additional coverage for getAssetDestinationPath
  it('should build asset destination path correctly', () => {
    const path = getAssetDestinationPath(1, 2, 'folder/file.pdf');
    expect(path).to.equal('1/projects/2/images/file.pdf');
  });

  // Additional coverage for buildSearchBaseFilters: with allowed filters and date filters
  it('should build search base filters with allowed filters and date ranges', () => {
    const query = {
      discipline: 'Mech',
      createdAt: { startDate: '2023-01-01', endDate: '2023-01-31' },
      submissionDate: { startDate: '2023-02-01', endDate: '2023-02-28' },
      dueDate: { startDate: '2023-03-01', endDate: '2023-03-31' },
    };
    const filters = buildSearchBaseFilters(query, ['discipline'], true);
    expect(filters).to.have.property('discipline', 'Mech');
    expect(filters).to.have.property('createdAt');
    expect(filters).to.have.property('submissionDate');
    expect(filters).to.have.property('dueDate');
  });

  // Additional coverage for buildSearchBaseFilters: no allowed filters, no date filters
  it('should build empty search base filters if no allowed filters and no date filters', () => {
    const filters = buildSearchBaseFilters({}, [], false);
    expect(filters).to.deep.equal({});
  });

  // Additional coverage for buildSearchBaseFilters: only allowed filters, no date filters
  it('should build search base filters with only allowed filters', () => {
    const query = { discipline: 'Mech', description: 'desc' };
    const filters = buildSearchBaseFilters(query, ['discipline', 'description'], false);
    expect(filters).to.have.property('discipline', 'Mech');
    expect(filters).to.have.property('description', 'desc');
  });

  // Additional coverage for buildSearchBaseFilters: only date filters, no allowed filters
  it('should build search base filters with only date filters', () => {
    const query = {
      createdAt: { startDate: '2023-01-01', endDate: '2023-01-31' },
    };
    const filters = buildSearchBaseFilters(query, [], false);
    expect(filters).to.have.property('createdAt');
  });

  // Additional coverage for buildSearchBaseFilters: allowDueDateFilter false
  it('should not include dueDate filter if allowDueDateFilter is false', () => {
    const query = {
      dueDate: { startDate: '2023-03-01', endDate: '2023-03-31' },
    };
    const filters = buildSearchBaseFilters(query, [], false);
    expect(filters).to.not.have.property('dueDate');
  });

  // Additional coverage for buildSearchBaseFilters: allowDueDateFilter true
  it('should include dueDate filter if allowDueDateFilter is true', () => {
    const query = {
      dueDate: { startDate: '2023-03-01', endDate: '2023-03-31' },
    };
    const filters = buildSearchBaseFilters(query, [], true);
    expect(filters).to.have.property('dueDate');
  });
});
