import { expect } from 'chai';
import sinon from 'sinon';
import sharp from 'sharp';
import * as SharpUtils from '../../utils/sharp.utils';
import { logger } from '../../utils/logger.utils';

describe('Sharp Utils', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('generateThumbnails', () => {
    it('should generate thumbnails for the specified sizes', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px', '256px-256px'];
      const mockFileDetails = { fileName: 'test.jpg', fileSize: 1024 };
      const mockMetadata = { format: 'jpeg', width: 500, height: 500 };

      // Stub sharp metadata and toBuffer methods
      const metadataStub = sinon
        .stub(sharp.prototype, 'metadata')
        .resolves(mockMetadata as any);
      const toBufferStub = sinon
        .stub(sharp.prototype, 'toBuffer')
        .resolves(Buffer.from('mock-thumbnail-data'));

      const result = await SharpUtils.generateThumbnails(
        mockImageBuffer,
        mockSizes,
        mockFileDetails,
        {
          format: 'jpeg',
          quality: 80,
        },
      );

      // Assertions
      expect(metadataStub.calledOnce).to.be.true;
      expect(toBufferStub.calledTwice).to.be.true; // Called for each size
      expect(result).to.have.length(2);
      expect(result[0]).to.include({
        size: '126px-126px',
        format: 'jpeg',
        width: 126,
        height: 126,
      });
      expect(result[1]).to.include({
        size: '256px-256px',
        format: 'jpeg',
        width: 256,
        height: 256,
      });
    });

    it('should log an error and throw if thumbnail generation fails', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.jpg', fileSize: 1024 };

      // Stub sharp to throw an error
      sinon.stub(sharp.prototype, 'toBuffer').rejects(new Error('Sharp error'));
      sinon.stub(logger, 'error');

      try {
        await SharpUtils.generateThumbnails(
          mockImageBuffer,
          mockSizes,
          mockFileDetails,
        );
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          'Failed to generate thumbnails: Input buffer contains unsupported image format',
        );
      }
    });

    it('should throw an error if sizes array is empty', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes: string[] = [];
      const mockFileDetails = { fileName: 'test.jpeg', fileSize: 1024 };

      try {
        await SharpUtils.generateThumbnails(
          mockImageBuffer,
          mockSizes,
          mockFileDetails,
        );
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.be.equal(
          'Failed to generate thumbnails: Input buffer contains unsupported image format',
        );
      }
    });

    it('should throw an error if image buffer is invalid', async () => {
      const mockImageBuffer = Buffer.from('');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.jpg', fileSize: 1024 };

      try {
        await SharpUtils.generateThumbnails(
          mockImageBuffer,
          mockSizes,
          mockFileDetails,
        );
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          'Failed to generate thumbnails: Input Buffer is empty',
        );
      }
    });

    it('should use default format and quality if not provided', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.jpg', fileSize: 1024 };
      const mockMetadata = { format: 'jpeg', width: 500, height: 500 };

      sinon.stub(sharp.prototype, 'metadata').resolves(mockMetadata as any);
      const toBufferStub = sinon
        .stub(sharp.prototype, 'toBuffer')
        .resolves(Buffer.from('mock-thumbnail-data'));

      const result = await SharpUtils.generateThumbnails(
        mockImageBuffer,
        mockSizes,
        mockFileDetails,
      );

      expect(toBufferStub.calledOnce).to.be.true;
      expect(result[0].format).to.equal('jpeg');
    });

    it('should use png format if specified', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.png', fileSize: 1024 };
      const mockMetadata = { format: 'png', width: 500, height: 500 };

      sinon.stub(sharp.prototype, 'metadata').resolves(mockMetadata as any);
      // Only .png() should be called
      const pngStub = sinon.stub(sharp.prototype, 'png').returnsThis();
      sinon.stub(sharp.prototype, 'toBuffer').resolves(Buffer.from('mock-thumbnail-data'));

      const result = await SharpUtils.generateThumbnails(
        mockImageBuffer,
        mockSizes,
        mockFileDetails,
        { format: 'png' },
      );

      expect(pngStub.calledOnce).to.be.true;
      expect(result[0].format).to.equal('png');
    });

    it('should use webp format if specified', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.webp', fileSize: 1024 };
      const mockMetadata = { format: 'webp', width: 500, height: 500 };

      sinon.stub(sharp.prototype, 'metadata').resolves(mockMetadata as any);
      // Only .webp() should be called
      const webpStub = sinon.stub(sharp.prototype, 'webp').returnsThis();
      sinon.stub(sharp.prototype, 'toBuffer').resolves(Buffer.from('mock-thumbnail-data'));

      const result = await SharpUtils.generateThumbnails(
        mockImageBuffer,
        mockSizes,
        mockFileDetails,
        { format: 'webp' },
      );

      expect(webpStub.calledOnce).to.be.true;
      expect(result[0].format).to.equal('webp');
    });

    it('should use default jpeg if unknown format is provided', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.unknown', fileSize: 1024 };
      const mockMetadata = { format: 'jpeg', width: 500, height: 500 };

      sinon.stub(sharp.prototype, 'metadata').resolves(mockMetadata as any);
      const jpegStub = sinon.stub(sharp.prototype, 'jpeg').returnsThis();
      sinon.stub(sharp.prototype, 'toBuffer').resolves(Buffer.from('mock-thumbnail-data'));

      const result = await SharpUtils.generateThumbnails(
        mockImageBuffer,
        mockSizes,
        mockFileDetails,
        { format: 'unknown' as any },
      );

      expect(jpegStub.calledOnce).to.be.true;
      expect(result[0].format).to.equal('unknown');
    });



    it('should log and throw error if sharp throws in outer catch', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSizes = ['126px-126px'];
      const mockFileDetails = { fileName: 'test.jpg', fileSize: 1024 };

      // Simulate error in sharp metadata
      sinon.stub(sharp.prototype, 'metadata').throws(new Error('outer sharp error'));


      try {
        await SharpUtils.generateThumbnails(
          mockImageBuffer,
          mockSizes,
          mockFileDetails,
          { format: 'jpeg' },
        );
        expect.fail('Should have thrown');
      } catch (err: any) {
        expect(err.message).to.include('Failed to generate thumbnails');
      }

    });
  });

  describe('generateThumbnail', () => {
    it('should log an error and throw if single thumbnail generation fails', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSize = '126px-126px';

      // Stub generateThumbnails to throw an error
      sinon.stub().rejects(new Error('Sharp error'));
      sinon.stub(logger, 'error');

      try {
        await SharpUtils.generateThumbnail(mockImageBuffer, mockSize);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          'Failed to generate thumbnail: Failed to generate thumbnails: Input buffer contains unsupported image format',
        );
      }
    });

    it('should throw an error if size string is invalid', async () => {
      const mockImageBuffer = Buffer.from('mock-image-data');
      const mockSize = 'invalid-size';

      try {
        await SharpUtils.generateThumbnail(mockImageBuffer, mockSize);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          'Failed to generate thumbnail: Failed to generate thumbnails: Input buffer contains unsupported image format',
        );
      }
    });
  });

  describe('parseSizeString', () => {
    it('should parse a valid size string into width and height', () => {
      const sizeStr = '126px-126px';
      const result = SharpUtils.parseSizeString(sizeStr);

      // Assertions
      expect(result).to.deep.equal({ width: 126, height: 126 });
    });

    it('should throw an error for an invalid size string', () => {
      const sizeStr = 'invalid-size';

      try {
        SharpUtils.parseSizeString(sizeStr);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        // Assertions
        expect((error as Error).message).to.equal(
          "Invalid size format: invalid-size. Expected format: 'widthpx-heightpx'",
        );
      }
    });

    it('should throw an error if size string is empty', () => {
      const sizeStr = '';

      try {
        SharpUtils.parseSizeString(sizeStr);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          "Invalid size format: . Expected format: 'widthpx-heightpx'",
        );
      }
    });

    it('should throw an error if size string contains non-numeric values', () => {
      const sizeStr = 'abcpx-defpx';

      try {
        SharpUtils.parseSizeString(sizeStr);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect((error as Error).message).to.equal(
          "Invalid size format: abcpx-defpx. Expected format: 'widthpx-heightpx'",
        );
      }
    });
  });
});
