import {expect} from 'chai';
import {
  getFormatedLoggerMessage,
  getFormatedErrorMessage,
  getFormatedApiResponse,
} from '../../utils/formated-message.utils';

describe('Formated Message Utils', () => {
  describe('getFormatedLoggerMessage', () => {
    it('should format the logger message correctly', () => {
      const user = {
        tenantKey: 'tenant-123',
        tenantId: 123,
      } as any;
      const data = {
        action: 'create',
        status: 'success',
      };

      const result = getFormatedLoggerMessage(user, data);

      expect(result).to.deep.equal({
        'tenant-123': {
          tenantId: 123,
          action: 'create',
          status: 'success',
        },
      });
    });

    it('should handle missing tenantKey gracefully', () => {
      const user = {
        tenantKey: null,
        tenantId: 123,
      } as any;
      const data = {
        action: 'delete',
      };

      const result = getFormatedLoggerMessage(user, data);

      expect(result).to.deep.equal({
        null: {
          tenantId: 123,
          action: 'delete',
        },
      });
    });

    it('should handle missing tenantId gracefully', () => {
      const user = {
        tenantKey: 'tenant-123',
        tenantId: null,
      } as any;
      const data = {
        action: 'update',
      };

      const result = getFormatedLoggerMessage(user, data);

      expect(result).to.deep.equal({
        'tenant-123': {
          tenantId: null,
          action: 'update',
        },
      });
    });
  });

  describe('getFormatedErrorMessage', () => {
    it('should format the error message correctly without details', () => {
      const message = 'An error occurred';

      const result = getFormatedErrorMessage(message);

      expect(result).to.deep.equal({
        error: true,
        message: 'An error occurred',
      });
    });

    it('should format the error message correctly with details', () => {
      const message = 'Validation failed';
      const details = {
        field: 'email',
        issue: 'Invalid format',
      };

      const result = getFormatedErrorMessage(message, details);

      expect(result).to.deep.equal({
        error: true,
        message: 'Validation failed',
        details: {
          field: 'email',
          issue: 'Invalid format',
        },
      });
    });

    it('should handle empty details gracefully', () => {
      const message = 'Error occurred';
      const details = {};

      const result = getFormatedErrorMessage(message, details);

      expect(result).to.deep.equal({
        error: true,
        message: 'Error occurred',
        details: {},
      });
    });
  });

  describe('getFormatedApiResponse', () => {
    it('should format the API response correctly without pagination', () => {
      const data = {
        id: 1,
        name: 'Test Item',
      };
      const message = 'Item retrieved successfully';

      const result = getFormatedApiResponse(data, message);

      expect(result).to.deep.equal({
        data: {
          id: 1,
          name: 'Test Item',
        },
        message: 'Item retrieved successfully',
        error: false,
      });
    });

    it('should format the API response correctly with pagination', () => {
      const data = {
        id: 1,
        name: 'Test Item',
      };
      const message = 'Items retrieved successfully';
      const pagination = {
        page: 1,
        limit: 10,
        total: 100,
      };

      const result = getFormatedApiResponse(data, message, pagination);

      expect(result).to.deep.equal({
        data: {
          id: 1,
          name: 'Test Item',
        },
        message: 'Items retrieved successfully',
        error: false,
        pagination: {
          page: 1,
          limit: 10,
          total: 100,
        },
      });
    });

    it('should use default message if none is provided', () => {
      const data = {
        id: 1,
        name: 'Test Item',
      };

      const result = getFormatedApiResponse(data);

      expect(result).to.deep.equal({
        data: {
          id: 1,
          name: 'Test Item',
        },
        message: 'Success',
        error: false,
      });
    });

    it('should handle empty data gracefully', () => {
      const data = {};
      const message = 'No data available';

      const result = getFormatedApiResponse(data, message);

      expect(result).to.deep.equal({
        data: {},
        message: 'No data available',
        error: false,
      });
    });
  });
});
