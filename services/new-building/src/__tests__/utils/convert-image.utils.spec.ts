import {expect} from 'chai';
import sinon from 'sinon';
import {Readable} from 'stream';

describe('convertHeicToJpegAndReplace', () => {
  const region = 'ap-south-1';
  const bucket = 'test-bucket';
  const heicKey = 'test-image.heic';
  const jpegKey = 'test-image.jpeg';

  let s3SendStub: sinon.SinonStub;
  let s3ClientStub: sinon.SinonStub;
  let heicConvertStub: sinon.SinonStub;
  let sharpStub: sinon.SinonStub;
  let sharpResizeStub: sinon.SinonStub;
  let sharpToBufferStub: sinon.SinonStub;
  let convertHeicToJpegAndReplace: any;

  beforeEach(() => {
    // Clear module cache to ensure fresh imports
    delete require.cache[require.resolve('../../utils/convert-image.utils')];

    // Create stubs
    s3SendStub = sinon.stub();
    s3ClientStub = sinon.stub().returns({send: s3SendStub});
    heicConvertStub = sinon.stub();
    sharpToBufferStub = sinon.stub();
    sharpResizeStub = sinon.stub().returns({toBuffer: sharpToBufferStub});
    sharpStub = sinon.stub().returns({resize: sharpResizeStub});

    // Mock AWS SDK
    const awsSdkMock = {
      S3Client: s3ClientStub,
      GetObjectCommand: sinon.stub().callsFake((params) => ({...params, _commandName: 'GetObjectCommand'})),
      PutObjectCommand: sinon.stub().callsFake((params) => ({...params, _commandName: 'PutObjectCommand'})),
      DeleteObjectCommand: sinon.stub().callsFake((params) => ({...params, _commandName: 'DeleteObjectCommand'})),
    };

    // Mock heic-convert - it exports as default
    const heicConvertMock = heicConvertStub;
    (heicConvertMock as any).default = heicConvertStub;

    // Mock sharp
    const sharpMock = sharpStub;
    (sharpMock as any).default = sharpStub;

    // Use require.cache to inject mocks
    (require.cache as any)[require.resolve('@aws-sdk/client-s3')] = {
      exports: awsSdkMock,
      loaded: true,
      id: require.resolve('@aws-sdk/client-s3'),
      children: [],
      parent: null,
      filename: require.resolve('@aws-sdk/client-s3'),
      paths: [],
      isPreloading: false,
      path: '',
      require: require,
    };

    (require.cache as any)[require.resolve('heic-convert')] = {
      exports: heicConvertMock,
      loaded: true,
      id: require.resolve('heic-convert'),
      children: [],
      parent: null,
      filename: require.resolve('heic-convert'),
      paths: [],
      isPreloading: false,
      path: '',
      require: require,
    };

    (require.cache as any)[require.resolve('sharp')] = {
      exports: sharpMock,
      loaded: true,
      id: require.resolve('sharp'),
      children: [],
      parent: null,
      filename: require.resolve('sharp'),
      paths: [],
      isPreloading: false,
      path: '',
      require: require,
    };

    // Import the module under test
    const module = require('../../utils/convert-image.utils');
    convertHeicToJpegAndReplace = module.convertHeicToJpegAndReplace;
  });

  afterEach(() => {
    sinon.restore();
    // Clear module cache
    delete require.cache[require.resolve('../../utils/convert-image.utils')];
    delete require.cache[require.resolve('@aws-sdk/client-s3')];
    delete require.cache[require.resolve('heic-convert')];
    delete require.cache[require.resolve('sharp')];
  });

  it('should throw error if key does not end with .heic', async () => {
    try {
      await convertHeicToJpegAndReplace(region, bucket, 'file.png');
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('Provided key is not a .heic file');
    }
  });

  it('should throw if S3 object body is invalid', async () => {
    // Mock S3 GetObject to return invalid body
    s3SendStub.resolves({Body: null});

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('Invalid S3 object body');
    }
  });

  it('should throw if S3 object body is not a Readable stream', async () => {
    // Mock S3 GetObject to return non-stream body
    s3SendStub.resolves({Body: 'not a stream'});

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('Invalid S3 object body');
    }
  });

  it('should successfully convert HEIC to JPEG and replace in S3', async () => {
    // Create a readable stream for S3 GetObject response
    const fakeHeicBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeHeicBuffer);
    fakeStream.push(null);

    // Mock S3 operations
    s3SendStub.onFirstCall().resolves({Body: fakeStream}); // GetObject
    s3SendStub.onSecondCall().resolves({}); // PutObject
    s3SendStub.onThirdCall().resolves({}); // DeleteObject

    // Mock heic-convert
    const jpegBuffer = Buffer.from('converted-jpeg-data');
    heicConvertStub.resolves(jpegBuffer);

    // Mock sharp
    const processedBuffer = Buffer.from('processed-jpeg-data');
    sharpToBufferStub.resolves(processedBuffer);

    const result = await convertHeicToJpegAndReplace(region, bucket, heicKey);

    expect(result).to.equal(jpegKey);
    expect(s3SendStub.callCount).to.equal(3);

    // Verify GetObjectCommand was called correctly
    const getObjectCall = s3SendStub.firstCall.args[0];
    expect(getObjectCall.Bucket).to.equal(bucket);
    expect(getObjectCall.Key).to.equal(heicKey);
    expect(getObjectCall._commandName).to.equal('GetObjectCommand');

    // Verify PutObjectCommand was called correctly
    const putObjectCall = s3SendStub.secondCall.args[0];
    expect(putObjectCall.Bucket).to.equal(bucket);
    expect(putObjectCall.Key).to.equal(jpegKey);
    expect(putObjectCall.Body).to.equal(processedBuffer);
    expect(putObjectCall.ContentType).to.equal('image/jpeg');
    expect(putObjectCall._commandName).to.equal('PutObjectCommand');

    // Verify DeleteObjectCommand was called correctly
    const deleteObjectCall = s3SendStub.thirdCall.args[0];
    expect(deleteObjectCall.Bucket).to.equal(bucket);
    expect(deleteObjectCall.Key).to.equal(heicKey);
    expect(deleteObjectCall._commandName).to.equal('DeleteObjectCommand');

    // Verify heic-convert was called
    expect(heicConvertStub.calledOnce).to.be.true;
    const heicConvertArgs = heicConvertStub.firstCall.args[0];
    expect(heicConvertArgs.buffer).to.deep.equal(fakeHeicBuffer);
    expect(heicConvertArgs.format).to.equal('JPEG');
    expect(heicConvertArgs.quality).to.equal(1);

    // Verify sharp was called
    expect(sharpStub.calledOnce).to.be.true;
    expect(sharpStub.firstCall.args[0]).to.equal(jpegBuffer);
    expect(sharpResizeStub.calledWith(800)).to.be.true;
    expect(sharpToBufferStub.calledOnce).to.be.true;
  });

  it('should handle stream reading errors', async () => {
    // Create a stream that emits an error
    const errorStream = new Readable();
    errorStream._read = () => {
      errorStream.emit('error', new Error('Stream read error'));
    };

    s3SendStub.resolves({Body: errorStream});

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('Stream read error');
    }
  });

  it('should handle heic-convert errors', async () => {
    const fakeBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeBuffer);
    fakeStream.push(null);

    s3SendStub.resolves({Body: fakeStream});
    heicConvertStub.rejects(new Error('HEIC conversion failed'));

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('HEIC conversion failed');
    }
  });

  it('should handle sharp processing errors', async () => {
    const fakeBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeBuffer);
    fakeStream.push(null);

    const jpegBuffer = Buffer.from('converted-jpeg-data');

    s3SendStub.resolves({Body: fakeStream});
    heicConvertStub.resolves(jpegBuffer);
    sharpToBufferStub.rejects(new Error('Sharp processing failed'));

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('Sharp processing failed');
    }
  });

  it('should handle S3 PutObject errors', async () => {
    const fakeBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeBuffer);
    fakeStream.push(null);

    const jpegBuffer = Buffer.from('converted-jpeg-data');
    const processedBuffer = Buffer.from('processed-jpeg-data');

    s3SendStub.onFirstCall().resolves({Body: fakeStream}); // GetObject succeeds
    s3SendStub.onSecondCall().rejects(new Error('S3 upload failed')); // PutObject fails

    heicConvertStub.resolves(jpegBuffer);
    sharpToBufferStub.resolves(processedBuffer);

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('S3 upload failed');
    }
  });

  it('should handle S3 DeleteObject errors', async () => {
    const fakeBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeBuffer);
    fakeStream.push(null);

    const jpegBuffer = Buffer.from('converted-jpeg-data');
    const processedBuffer = Buffer.from('processed-jpeg-data');

    s3SendStub.onFirstCall().resolves({Body: fakeStream}); // GetObject succeeds
    s3SendStub.onSecondCall().resolves({}); // PutObject succeeds
    s3SendStub.onThirdCall().rejects(new Error('S3 delete failed')); // DeleteObject fails

    heicConvertStub.resolves(jpegBuffer);
    sharpToBufferStub.resolves(processedBuffer);

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('S3 delete failed');
    }
  });

  it('should handle S3 GetObject errors', async () => {
    s3SendStub.rejects(new Error('S3 GetObject failed'));

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err.message).to.include('S3 GetObject failed');
    }
  });

  it('should handle empty stream data', async () => {
    // Create an empty stream
    const emptyStream = new Readable();
    emptyStream.push(null); // End immediately

    s3SendStub.resolves({Body: emptyStream});

    try {
      await convertHeicToJpegAndReplace(region, bucket, heicKey);
      expect.fail('Should have thrown');
    } catch (err: any) {
      // Should handle empty buffer gracefully
      expect(err).to.be.an('error');
    }
  });

  it('should handle large stream data', async () => {
    // Create a large buffer
    const largeBuffer = Buffer.alloc(10 * 1024 * 1024, 'a'); // 10MB
    const largeStream = new Readable();
    largeStream.push(largeBuffer);
    largeStream.push(null);

    const jpegBuffer = Buffer.from('converted-jpeg-data');
    const processedBuffer = Buffer.from('processed-jpeg-data');

    s3SendStub.onFirstCall().resolves({Body: largeStream});
    s3SendStub.onSecondCall().resolves({});
    s3SendStub.onThirdCall().resolves({});

    heicConvertStub.resolves(jpegBuffer);
    sharpToBufferStub.resolves(processedBuffer);

    const result = await convertHeicToJpegAndReplace(region, bucket, heicKey);
    expect(result).to.equal(jpegKey);

    // Verify heic-convert was called with large buffer
    expect(heicConvertStub.calledOnce).to.be.true;
    const heicConvertArgs = heicConvertStub.firstCall.args[0];
    expect(heicConvertArgs.buffer).to.deep.equal(largeBuffer);
  });

  it('should handle different HEIC file extensions', async () => {
    const testCases = [
      {input: 'file.heic', expected: 'file.jpeg'},
      {input: 'file.HEIC', expected: 'file.jpeg'},
      {input: 'file.Heic', expected: 'file.jpeg'},
      {input: 'file.HeiC', expected: 'file.jpeg'},
    ];

    for (const testCase of testCases) {
      const fakeBuffer = Buffer.from('fake-heic-data');
      const fakeStream = new Readable();
      fakeStream.push(fakeBuffer);
      fakeStream.push(null);

      const jpegBuffer = Buffer.from('converted-jpeg-data');
      const processedBuffer = Buffer.from('processed-jpeg-data');

      s3SendStub.reset();
      s3SendStub.onFirstCall().resolves({Body: fakeStream});
      s3SendStub.onSecondCall().resolves({});
      s3SendStub.onThirdCall().resolves({});

      heicConvertStub.reset();
      heicConvertStub.resolves(jpegBuffer);

      sharpToBufferStub.reset();
      sharpToBufferStub.resolves(processedBuffer);

      const result = await convertHeicToJpegAndReplace(region, bucket, testCase.input);
      expect(result).to.equal(testCase.expected);
    }
  });

  it('should handle stream with multiple chunks', async () => {
    // Create a stream with multiple chunks
    const chunk1 = Buffer.from('chunk1');
    const chunk2 = Buffer.from('chunk2');
    const chunk3 = Buffer.from('chunk3');
    const expectedBuffer = Buffer.concat([chunk1, chunk2, chunk3]);

    const multiChunkStream = new Readable();
    multiChunkStream.push(chunk1);
    multiChunkStream.push(chunk2);
    multiChunkStream.push(chunk3);
    multiChunkStream.push(null);

    const jpegBuffer = Buffer.from('converted-jpeg-data');
    const processedBuffer = Buffer.from('processed-jpeg-data');

    s3SendStub.onFirstCall().resolves({Body: multiChunkStream});
    s3SendStub.onSecondCall().resolves({});
    s3SendStub.onThirdCall().resolves({});

    heicConvertStub.resolves(jpegBuffer);
    sharpToBufferStub.resolves(processedBuffer);

    const result = await convertHeicToJpegAndReplace(region, bucket, heicKey);
    expect(result).to.equal(jpegKey);

    // Verify heic-convert was called with concatenated buffer
    expect(heicConvertStub.calledOnce).to.be.true;
    const heicConvertArgs = heicConvertStub.firstCall.args[0];
    expect(heicConvertArgs.buffer).to.deep.equal(expectedBuffer);
  });

  it('should handle invalid file extensions', async () => {
    const invalidExtensions = [
      'file.jpg',
      'file.png',
      'file.gif',
      'file.bmp',
      'file.webp',
      'file.tiff',
      'file.svg',
      'file.txt',
      'file',
      'file.heic.backup',
      'heic.file',
    ];

    for (const invalidKey of invalidExtensions) {
      try {
        await convertHeicToJpegAndReplace(region, bucket, invalidKey);
        expect.fail(`Should have thrown for key: ${invalidKey}`);
      } catch (err: any) {
        expect(err.message).to.include('Provided key is not a .heic file');
      }
    }
  });

  it('should handle S3Client instantiation', async () => {
    // Verify S3Client is instantiated with correct region
    const fakeBuffer = Buffer.from('fake-heic-data');
    const fakeStream = new Readable();
    fakeStream.push(fakeBuffer);
    fakeStream.push(null);

    s3SendStub.resolves({Body: fakeStream});
    heicConvertStub.resolves(Buffer.from('jpeg'));
    sharpToBufferStub.resolves(Buffer.from('processed'));

    await convertHeicToJpegAndReplace(region, bucket, heicKey);

    expect(s3ClientStub.calledOnce).to.be.true;
    expect(s3ClientStub.firstCall.args[0]).to.deep.equal({region});
  });
});
