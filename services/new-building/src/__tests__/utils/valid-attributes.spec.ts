import { expect } from 'chai';
import { getValidModelAttributes } from '../../utils/valid-attributes';

describe('getValidModelAttributes', () => {
  it('should return attribute names for a Sequelize model', () => {
    // Mock a Sequelize model with getAttributes static method
    const mockAttributes = {
      id: {},
      name: {},
      createdAt: {},
      updatedAt: {},
    };
    const mockModel = {
      getAttributes: () => mockAttributes,
    } as any;

    const result = getValidModelAttributes(mockModel);
    expect(result).to.deep.equal(['id', 'name', 'createdAt', 'updatedAt']);
  });

  it('should return an empty array if model has no attributes', () => {
    const mockModel = {
      getAttributes: () => ({}),
    } as any;

    const result = getValidModelAttributes(mockModel);
    expect(result).to.deep.equal([]);
  });
});
