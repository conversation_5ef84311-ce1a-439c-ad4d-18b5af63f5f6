import {Request, Response, NextFunction} from 'express';
import * as jwt from 'jsonwebtoken';
import {AnyObject} from '../types';
import {getTenantDb} from '../configs';
import {createRepositories} from '../repositories';
import {requestContext} from '../utils';

/**
 * Middleware to verify the JSON Web Token (JWT) and optionally check for required permissions.
 *
 * @param requiredPermissions - Array of permissions required to perform the action.
 * If no permissions are passed, it will only verify the token.
 */
export default function verifyToken(
  requiredPermissions?: string[],
): (req: Request, res: Response, next: NextFunction) => void {
  return (req: Request, res: Response, next: NextFunction) => {
    void (async () => {
      const authHeader = req.headers['authorization'];
      const token = authHeader?.split(' ')[1];

      if (!token) {
        return res.status(401).json({error: 'No token provided'});
      }

      try {
        const algorithm =
          process.env.JWT_PUBLIC_KEY === 'true' ? 'RS256' : 'HS256';
        const user = jwt.verify(token, String(process.env.JWT_SECRET), {
          algorithms: [algorithm],
          issuer: process.env.JWT_ISSUER,
        }) as AnyObject;

        console.log({tenantKey: user['tenantKey'], tenantId: user['tenantId']});
        // Users with valid tenantId or internal user role can access the API
        if (!user['tenantKey'] || !user['tenantId']) {
          res.status(403).json({error: 'Tenant Id not provided'});
          return;
        }

        console.log('After JWT verification, user:');

        const {sequelize, models} = await getTenantDb(user.tenantKey);
        console.log('After sequelize');
        const repositories = createRepositories(models);

        console.log('After repositories');

        // Attach the user object to the request
        (req as AnyObject).user = user;

        // If required permissions are provided, check if the user has them
        if (!hasRequiredPermissions(user, requiredPermissions)) {
          return res.status(403).json({
            error:
              'Forbidden: You do not have the required permissions to perform this action.',
          });
        }

        requestContext.run({models, repositories, sequelize, user}, () => {
          next();
        });
      } catch (error) {
        return res.status(403).json({error: 'Invalid token'});
      }
    })();
  };
}

/**
 * Checks if the given user has at least one of the required permissions.
 *
 * @param user - The user object, expected to contain a `realm_access.roles` array.
 * @param requiredPermissions - An optional array of permission strings to check against the user's roles.
 * @returns `true` if no required permissions are specified or if the user has at least one of the required permissions; otherwise, `false`.
 */
function hasRequiredPermissions(
  user: AnyObject,
  requiredPermissions?: string[],
): boolean {
  if (!requiredPermissions || requiredPermissions.length === 0) return true;
  const userRoles: string[] = user?.realm_access?.roles ?? [];
  return requiredPermissions.some(permission => userRoles.includes(permission));
}
