import {Request, Response, NextFunction} from 'express';
import <PERSON><PERSON> from 'joi';
import * as Validators from '../models/dto';

type ValidatorKey = keyof typeof Validators;

/**
 * Middleware function for validating request data using predefined Joi schemas.
 *
 * @param validator - The key representing the validator to be used. Must exist in the `Validators` object.
 *
 * @throws {Error} If the specified validator does not exist in the `Validators` object.
 *
 * @returns An asynchronous middleware function that validates the `body`, `query`, and `params` of the request
 *          against the corresponding Joi schemas defined in the `Validators` object.
 *
 * The middleware performs the following:
 * - Validates the request data (`body`, `query`, `params`) using the specified Joi schemas.
 * - Updates the request object with the validated data.
 * - If validation fails, responds with a 400 status code and a detailed error message.
 * - If an unexpected error occurs, passes the error to the next middleware.
 *
 * Example response for validation errors:
 * ```json
 * {
 *   "status": "error",
 *   "message": "Incorrect values entered. Please check and update",
 *   "details": [
 *     {
 *       "message": "Validation error message"
 *     }
 *   ]
 * }
 * ```
 */
export default function validatorMiddleware(validator: ValidatorKey) {
  // If validator does not exist, throw error
  if (!Validators.hasOwnProperty(validator)) {
    throw new Error(`'${validator}' validator does not exist`);
  }

  return async function (
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const schemaGroup = Validators[validator] as unknown as Partial<
        Record<'body' | 'query' | 'params', Joi.ObjectSchema>
      >;

      const targets: Array<keyof typeof schemaGroup> = [
        'body',
        'query',
        'params',
      ];

      for (const key of targets) {
        const schema = schemaGroup[key];
        if (schema) {
          const validated = await schema.validateAsync(req[key], {
            abortEarly: false,
          });
          req[key] = validated;
        }
      }
      next();
    } catch (err: any) {
      if (err.isJoi) {
        res.status(400).json({
          status: 'error',
          message: 'Incorrect values entered. Please check and update',
          details: err.details.map((detail: any) => ({
            message: detail.message,
          })),
        });
        return;
      }
      next(err);
    }
  };
}
