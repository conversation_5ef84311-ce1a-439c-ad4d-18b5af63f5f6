import {Request, Response, NextFunction} from 'express';
import {getRequestContext} from '../utils/request-context';

/**
 * Middleware function to verify the existence of a hull in the repository.
 *
 * This middleware checks if a hull with the given `id` exists in the repository.
 * If the hull exists, it adds the `createdAt` field from the hull to the request body.
 * If the hull does not exist, it responds with a 404 status and an error message.
 * In case of an internal server error, it responds with a 500 status and an error message.
 *
 * @returns A middleware function that processes the request, response, and next function.
 *
 * @throws {404} If the hull with the specified `id` does not exist.
 * @throws {500} If an internal server error occurs during the process.
 */
export default function verifyHullMiddleware(): (
  req: Request,
  res: Response,
  next: NextFunction,
) => void {
  return (req: Request, res: Response, next: NextFunction): void => {
    const {id} = req.params;

    (async () => {
      try {
        // Check if the hull exists in the repository
        const context = getRequestContext();
        const {repositories} = context;

        // Check if the hull exists in the repository
        const hull = await repositories.hullRepository.findById(Number(id));

        if (!hull) {
          return res.status(404).json({
            error: true,
            message: `Hull with hullNo ${id} does not exist`,
          });
        }

        // Add the `createdAt` field to the request body
        req.body.createdAt = hull.dataValues.createdAt;

        next();
      } catch (error) {
        return res.status(500).json({
          error: true,
          message: `Internal server error`,
        });
      }
    })();
  };
}
