export enum ProjectPermissions {
  CREATE = 'nbp|prj|add',
  EDIT = 'nbp|prj|edit',
  VIEW = 'nbp|prj|view',
  DELETE = 'nbp|prj|del',
}

export enum HullPermissions {
  CREATE = 'nbp|hull|add',
  EDIT = 'nbp|hull|edit',
  VIEW = 'nbp|hull|view',
  DELETE = 'nbp|hull|del',
}

export enum DrawingCommentsPermissions {
  CREATE = 'nbp|comm|add',
  VIEW = 'nbp|comm|view',
}

export enum DrawingPlanApprovalPermissions {
  CREATE = 'nbp|drwp|add',
  VIEW = 'nbp|drwp|view',
  DELETE = 'nbp|drwp|del',
}

export enum DrawingVersionCreationPermissions {
  CREATE = 'nbp|drwv|upld',
  VIEW = 'nbp|drwv|view',
  DELETE = 'nbp|drwv|del',
}

export enum CommentsResolvePermissions {
  COMMENT_All = 'nbp|admin|all',
  COMMENT_SELF = 'nbp|comm|rslv',
}

export enum InspectionPermissions {
  CREATE = 'nbp|insp|add',
  EDIT = 'nbp|insp|edit',
  VIEW = 'nbp|insp|view',
  DELETE = 'nbp|insp|del',
}

export enum ObservationPermissions {
  CREATE = 'nbp|obsv|add',
  EDIT = 'nbp|obsv|edit',
  VIEW = 'nbp|obsv|view',
  DELETE = 'nbp|obsv|del',
}
