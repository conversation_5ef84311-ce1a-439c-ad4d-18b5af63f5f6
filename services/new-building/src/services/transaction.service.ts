import { Transaction as SequelizeTransactionType } from 'sequelize';
import { TransactionStatus, TransactionType } from '../enums';
import { logger, ContextType } from '../utils';
import { AnyObject } from '../types';

/**
 * Creates transaction records for generating thumbnails for a collection of assets.
 *
 * @param assets - Array of Sequelize asset models that need thumbnails generated
 * @param parameters - Object containing project and tenant context for the thumbnail generation
 * @param t - Active Sequelize transaction to ensure database consistency
 * @param context - The request context containing repositories, models, etc.
 * @returns Promise resolving to the created transaction records
 */
export async function addImageThumbnailTransaction(
  assets: AnyObject[],
  parameters: { projectId: number; tenantId: number },
  t: SequelizeTransactionType,
  context: ContextType,
) {
  const { repositories } = context;
  const bulkCreatePayload = assets.map(asset => ({
    type: TransactionType.IMAGE_THUMBNAIL,
    status: TransactionStatus.PENDING,
    parameters: {
      ...parameters,
      assetId: asset.getDataValue('id'),
      assetPath: asset.getDataValue('assetPath'),
    },
  }));

  const result = await repositories.transactionRepository.createAll(
    bulkCreatePayload,
    {
      transaction: t,
    },
  );

  return result;
}

/**
 * Finds and claims a pending image thumbnail transaction for processing.
 *
 * @param context - The request context containing repositories, models, etc.
 * @returns Promise resolving to the claimed transaction record, or undefined if no pending transactions exist
 */
export async function getPendingImageThumbnailTransactions(
  context: ContextType,
) {
  const { repositories, models } = context;
  logger.info(
    '[Transaction Service] Fetching pending image thumbnail transactions',
  );
  const found = await repositories.transactionRepository.findOne({
    status: TransactionStatus.PENDING,
    type: TransactionType.IMAGE_THUMBNAIL,
  });
  logger.info('[Transaction Service] Found pending transaction:', found);

  if (found) {
    const [, updated] = await models.Transaction.update(
      { status: TransactionStatus.IN_PROGRESS },
      { where: { uuid: found.get('uuid') as string }, returning: true },
    );
    return updated[0];
  }
}
