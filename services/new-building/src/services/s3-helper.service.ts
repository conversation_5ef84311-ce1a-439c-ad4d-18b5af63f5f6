import {
  S3Client,
  DeleteObjectCommand,
  GetObjectCommand,
  S3ClientConfig,
  PutObjectCommand,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';
import {getSignedUrl} from '@aws-sdk/s3-request-presigner';
import {createPresignedPost} from '@aws-sdk/s3-presigned-post';
import {v4 as uuidv4} from 'uuid';
import {CustomError, logger} from '../utils';
import contentDisposition from 'content-disposition';

export namespace S3Constants {
  export const OriginalNameKey = 'originalname';
  export const ReferenceIdKey = 'referenceid';
  export const ReferenceTypeKey = 'referencetype';
  export const MimeTypeKey = 'mimetype';
  export const VersionKey = 'version';
  export const UserIdKey = 'userid';
}

export interface UploadParams {
  fileName?: string;
  contentType: string;
  fileSize?: number;
  metadata?: Record<string, string>;
}

export interface PresignedUrlResponse {
  fileKey: string;
  bucketName: string;
  expiresIn: number;
}

export interface PresignedPostResponse {
  url: string;
  fields: Record<string, string>;
  fileKey: string;
  bucketName: string;
  expiresIn: number;
}

const bucketName = process.env.AWS_S3_BUCKET_NAME!;

export class S3Service {
  private readonly s3Client: S3Client;

  constructor(config: S3ClientConfig) {
    this.s3Client = new S3Client(config);
  }

  /**
   * Generate a presigned POST for uploading a file to S3 (POST method with form fields)
   */
  async generatePresignedPost(
    params: UploadParams,
  ): Promise<PresignedPostResponse> {
    const {fileName, contentType, metadata} = params;

    // Generate a unique file key
    const parts = fileName?.split('/') ?? [];
    const filePath = parts.slice(0, -1).join('/');
    const extension = fileName?.includes('.')
      ? `.${fileName.split('.').pop()}`
      : '';
    const basePath = filePath || 'uploads';
    const fileKey = `${basePath}/${uuidv4()}${extension}`;

    // Set the expiration time for the presigned URL (in seconds)
    const expiresIn = Number(process.env.AWS_S3_SIGNED_URL_EXPIRY);

    const presignedPost = await createPresignedPost(this.s3Client, {
      Bucket: bucketName,
      Key: fileKey,
      Conditions: [
        // ['content-length-range', 0, 10485760], // e.g., 10MB max
        ['eq', '$Content-Type', contentType],
      ],
      Fields: {
        acl: 'private',
        'Content-Type': contentType,
        ...(metadata && {'x-amz-meta-custom': JSON.stringify(metadata)}),
      },
      Expires: expiresIn,
    });

    return {
      url: presignedPost.url,
      fields: presignedPost.fields,
      fileKey,
      bucketName,
      expiresIn,
    };
  }

  /**
   * Generate a presigned URL for viewing/downloading a file from S3
   */
  async generatePresignedDownloadUrl(
    fileKey: string,
    fileName: string,
    expiresIn: number = 60 * 60,
  ): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      ResponseContentDisposition: contentDisposition(fileName),
    });

    return getSignedUrl(this.s3Client, command, {expiresIn});
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(fileKey: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    await this.s3Client.send(command);
  }

  /**
   * Retrieves metadata for a file stored in an S3 bucket.
   *
   * @param fileKey - The key of the file in the S3 bucket.
   * @returns A promise that resolves to the metadata of the file.
   * @throws Will throw an error if the S3 client fails to retrieve the file metadata.
   */
  async getFileMetadata(fileKey: string) {
    logger.info('[S3Service] getFileMetadata', {fileKey});
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await this.s3Client.send(command);
    return response.Metadata;
  }

  /**
   * Extracts and validates metadata based on the required keys.
   *
   * @template T - A string literal type representing the required keys.
   * @param requiredKey - An array of keys that must be present in the metadata.
   * @param metadata - A record containing metadata as key-value pairs.
   * @returns The metadata object cast to a record with the required keys.
   * @throws {Error} If any of the required keys are missing in the metadata.
   */
  extractMetadata<T extends string>(
    requiredKey: T[],
    metadata: Record<string, string>,
  ) {
    // check if all the required keys are present
    requiredKey.forEach(key => {
      if (metadata[key] === undefined) {
        throw new Error(`${key} is not available on meta-data`);
      }
    });
    return metadata as Record<T, string>;
  }

  /**
   * Uploads project logs in JSON format to an S3 bucket.
   *
   * @param tenantId - The ID of the tenant to associate with the uploaded file.
   * @param jsonData - The JSON data to be uploaded.
   * @returns A promise that resolves to the S3 file key of the uploaded JSON file.
   * @throws An error if the upload to S3 fails.
   */
  async uploadProjectLogsToS3(
    tenantId: number,
    projectId: number,
    updates: unknown,
  ): Promise<string> {
    try {
      const expiresIn = Number(process.env.AWS_S3_LOGS_EXPIRY_DAYS);
      const oneYearInSeconds = expiresIn * 24 * 60 * 60;
      const fileKey = `${tenantId}/projects/${projectId}/${Date.now()}.json`;
      const jsonString = JSON.stringify(updates);
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: fileKey,
          Body: jsonString,
          ContentType: 'application/json',
          Metadata: {
            'x-amz-meta-expiry': `${oneYearInSeconds}`, // Custom metadata for expiry
          },
        }),
      );
      return fileKey;
    } catch (error) {
      throw new Error('Failed to upload JSON to S3');
    }
  }

  /**
   * Retrieves a readable stream for the specified file from an S3 bucket.
   *
   * @param fileKey - The key of the file to retrieve from the S3 bucket.
   * @returns A promise that resolves to a readable stream of the file's content.
   * @throws An error if the file cannot be retrieved or the S3 operation fails.
   */
  async getTheFileStream(fileKey: string): Promise<NodeJS.ReadableStream> {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await this.s3Client.send(command);
    return response.Body as NodeJS.ReadableStream;
  }

  /**
   * Fetches an object from the S3 bucket and returns its content as a Buffer.
   *
   * @param fileKey - The key (path) of the object in the S3 bucket.
   * @returns A promise that resolves to a Buffer containing the file content.
   * @throws Error if the file cannot be retrieved or if the response body is empty.
   */
  async getObject(fileKey: string): Promise<Buffer> {
    try {
      logger.info('[S3Service] getObject', {fileKey});

      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: fileKey,
      });

      const response = await this.s3Client.send(command);

      if (!response.Body) {
        throw new Error('Response body is empty');
      }

      // Use the SDK's transformToByteArray method
      const byteArray = await response.Body.transformToByteArray();
      return Buffer.from(byteArray);
    } catch (error) {
      logger.error('[S3Service] Error fetching object from S3', {
        fileKey,
        error: error,
      });
      throw new CustomError(
        `Failed to fetch object from S3: ${String(error)}`,
        500,
      );
    }
  }

  /**
   * Uploads data to an S3 bucket.
   *
   * @param {string} fileKey - The key (path) where the object should be stored in the bucket.
   * @param {Buffer | string} body - The data to be uploaded, which can be a Buffer or a string.
   * @param {Record<string, string>} metadata - Metadata associated with the uploaded object.
   * @param {string} contentType - The content type of the file (optional).
   *
   * @returns A promise that resolves to the S3 file key once the data is successfully uploaded.
   * @throws Error if the upload to S3 fails.
   */
  async putObject(
    fileKey: string,
    body: Buffer | string,
    metadata?: Record<string, string>,
    contentType?: string,
  ): Promise<string> {
    try {
      logger.info('[S3Service] putObject', {
        fileKey,
        contentLength:
          typeof body === 'string' ? Buffer.byteLength(body) : body.length,
        contentType,
      });

      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: fileKey,
          Body: body,
          ContentType: contentType,
          Metadata: metadata,
        }),
      );

      return fileKey;
    } catch (error) {
      logger.error('[S3Service] Error uploading object to S3', {
        fileKey,
        error: error,
      });
      throw new CustomError(
        `Failed to upload object to S3: ${String(error)}'}`,
        500,
      );
    }
  }

  /**
   * Copies a file from one location to another within the same S3 bucket.
   *
   * @param {string} sourcePath - The source path of the file in the S3 bucket.
   * @param {string} destinationPath - The destination path where the file should be copied to.
   * @param {boolean} preserveMetadata - Whether to preserve the metadata (defaults to true).
   *
   * @returns A promise that resolves to the destination path once the file is successfully copied.
   * @throws Error if the copy operation fails.
   */
  async copyObject(
    sourcePath: string,
    destinationPath: string,
    preserveMetadata: boolean = true,
  ): Promise<string> {
    try {
      logger.info('[S3Service] copyObject', {
        sourcePath,
        destinationPath,
        preserveMetadata,
      });

      await this.s3Client.send(
        new CopyObjectCommand({
          Bucket: bucketName,
          CopySource: `${bucketName}/${sourcePath}`,
          Key: destinationPath,
          // When preserveMetadata is true, use COPY directive, otherwise use REPLACE
          MetadataDirective: preserveMetadata ? 'COPY' : 'REPLACE',
        }),
      );

      return destinationPath;
    } catch (error) {
      logger.error('[S3Service] Error copying object in S3', {
        sourcePath,
        destinationPath,
        error: error,
      });
      throw new CustomError(
        `Failed to copy object in S3 from ${sourcePath} to ${destinationPath}: ${String(error)}`,
        500,
      );
    }
  }
}

export default new S3Service({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});
