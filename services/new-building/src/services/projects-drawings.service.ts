import {CommentType, ProjectDrawingStatus} from '../enums';
import {ProjectDrawingCreationAttributes} from '../models/project-drawing.model';
import {IFindQuery, IFindAllQuery, User} from '../types';
import {excludeKeys} from '../utils/exclude-key.utils';
import {Op, Transaction} from 'sequelize';
import {resolveAllComments, S3HelperService} from '.';
import {CustomError, logger, ContextType} from '../utils';

class ProjectDrawingsService {
  /**
   * Creates a new project drawing and associates it with the specified drawing list.
   * Validates the provided hull numbers and ensures they are associated with the project.
   * Generates a new version for the drawing and creates corresponding asset records.
   *
   * @param drawingListId - The ID of the drawing list to associate the new drawing with.
   * @param data - The details of the drawing to be created, including:
   *   - `name`: The name of the drawing.
   *   - `dueDate`: The due date for the drawing.
   *   - `assetType`: The type of the asset associated with the drawing.
   *   - `assetPath`: The path to the asset file.
   *   - `status`: The status of the drawing.
   *   - `hullNoId` (optional): An array of hull numbers to validate and associate with the drawing.
   * @param user - The user creating the drawing, containing user details such as `user_id`.
   *
   * @throws {Error} If the provided drawing list ID is invalid.
   * @throws {Error} If any of the provided hull numbers are not associated with the project.
   *
   * @returns A promise that resolves to the created project drawing record.
   */
  async createProjectDrawings(
    drawingListId: number,
    data: {
      dueDate: Date;
      assetName: string;
      assetType: string;
      assetPath: string;
      status: ProjectDrawingStatus;
      hullNoId?: number[];
    },
    user: User,
    context: ContextType,
  ) {
    const {repositories, models, sequelize} = context;
    const transaction = await sequelize.transaction();
    const drawingList =
      await repositories.drawingListRepository.findById(drawingListId);
    if (!drawingList) {
      throw new CustomError('Invalid drawing information shared', 404);
    }
    try {
      const currentLatestDrawing = await models.ProjectDrawing.findOne({
        where: {drawingListId, deletedAt: null},
        attributes: ['version', 'status'],
        order: [['version', 'DESC']],
        transaction,
      });
      if (
        currentLatestDrawing?.dataValues.status ===
        ProjectDrawingStatus.APPROVED
      ) {
        throw new CustomError(
          "You can't upload more drawings after approval",
          403,
        );
      }
      let isAllHullsValid = true;
      if (data?.hullNoId?.length) {
        isAllHullsValid = await this.checkIsValidHullId(
          data.hullNoId,
          drawingList.dataValues.projectId,
          transaction,
          context,
        );
      }
      if (!isAllHullsValid) {
        throw new CustomError(
          'Some given hulls are not associated with this project',
          404,
        );
      }
      let updatedVersion = 0;
      if (currentLatestDrawing) {
        updatedVersion = currentLatestDrawing.dataValues.version + 1;
      }
      const result = await repositories.projectDrawingRepository.create(
        {
          name: drawingList.dataValues.name,
          dueDate: data.dueDate,
          createdBy: user.user_id,
          version: updatedVersion,
          status: data.status,
          assetType: data.assetType,
          assetPath: data.assetPath,
          assetName: data.assetName,
          drawingListId: drawingListId,
          tenantId: user?.tenantId,
        } as unknown as ProjectDrawingCreationAttributes,
        {transaction},
      );
      if (data.status === ProjectDrawingStatus.APPROVED) {
        await resolveAllComments(
          Number(drawingListId),
          CommentType.DRAWING,
          user,
          context,
          transaction,
        );
      }
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      logger.error(
        '[ProjectDrawingService] Error in createProjectDrawings',
        error,
      );
      if (error instanceof CustomError) throw error;
      throw new CustomError(
        'There is some error in creating project drawings',
        500,
      );
    }
  }

  /**
   * Finds a drawing by its ID and retrieves associated assets, if available.
   * If an asset is associated with the drawing, a pre-signed download URL
   * for the asset is generated and included in the response.
   *
   * @param id - The unique identifier of the drawing to retrieve.
   * @returns A promise that resolves to the drawing details along with its associated assets.
   * @throws An error if no drawing is found with the given ID.
   */
  async findDrawingsById(id: number, context: ContextType) {
    const {repositories} = context;
    const result = await repositories.projectDrawingRepository.findById(
      id,
      true,
      {},
    );
    if (!result) {
      throw new CustomError('Drawings not available with the given id', 404);
    }
    if (result?.dataValues.assetPath) {
      const url = await S3HelperService.generatePresignedDownloadUrl(
        result?.dataValues.assetPath,
        result?.dataValues.assetName,
      );
      result.dataValues.assetPath = url;
    }
    return result;
  }

  async checkIsValidHullId(
    hullNoIds: number[],
    projectId: number,
    transaction: Transaction,
    context: ContextType,
  ): Promise<boolean> {
    const {repositories} = context;
    const hullsFound = await repositories.projectHullRepository.findAll(
      [],
      false,
      {
        hullId: {[Op.in]: hullNoIds},
        projectId: projectId,
      },
      [],
      transaction,
    );
    return hullsFound.length === hullNoIds.length;
  }

  /**
   * Retrieves project drawings based on the provided query parameters, including pagination
   * and filtering by drawing list ID. Each drawing is enriched with its associated assets,
   * and the asset paths are replaced with pre-signed URLs for secure access.
   *
   * @param query - The query parameters for retrieving project drawings.
   * @param query.page - The page number for pagination (default is 1).
   * @param query.limit - The number of items per page for pagination (default is 10).
   * @param query.drawingListId - The ID of the drawing list to filter the drawings.
   *
   * @returns An object containing the retrieved drawings with their associated assets and
   *          pagination details:
   *          - `data`: An array of drawings, each enriched with its associated assets.
   *          - `pagination`: An object containing pagination details:
   *              - `totalItems`: The total number of drawings matching the query.
   *              - `totalPages`: The total number of pages.
   *              - `page`: The current page number.
   *              - `pageSize`: The number of items per page.
   *
   * @throws An error if no drawings are found matching the given specifications.
   */
  async getAllDrawings(query: IFindAllQuery, context: ContextType) {
    const {repositories} = context;
    const {page = 1, limit = 100, drawingListId} = query;
    const offset = (page - 1) * limit;
    const findQuery: IFindQuery = {
      orderBy: [],
      limit,
      offset,
      page,
      where: {} as Record<string, any>,
      attributes: [],
    };
    if (drawingListId && Number(drawingListId) > 0) {
      findQuery.where = {...findQuery.where, ...{drawingListId: drawingListId}};
    }
    const result =
      await repositories.projectDrawingRepository.findAndCountAll(findQuery);
    const totalPages = Math.ceil(result.count / limit);
    return {
      data: result.rows,
      pagination: {
        totalItems: result.count,
        totalPages,
        page: page,
        pageSize: limit,
      },
    };
  }

  /**
   * Deletes a drawing by its ID within a specified drawing list.
   *
   * @param drawingListId - The ID of the drawing list containing the drawing to be deleted.
   * @param id - The ID of the drawing to be deleted.
   * @param user - The user performing the deletion operation.
   *
   * @returns An object containing a success message upon successful deletion.
   *
   * @throws CustomError - Throws an error if:
   * - No drawings are found with the given drawing list ID.
   * - The drawing is already approved or deleted.
   * - The provided drawing ID is greater than the latest drawing ID.
   * - The provided drawing ID does not match the latest drawing ID.
   * - Attempting to delete a previous drawing version.
   *
   * @throws Error - Throws a generic error if the transaction fails.
   */
  async deleteDrawingsById(
    drawingListId: number,
    id: number,
    user: User,
    context: ContextType,
  ) {
    const {repositories, sequelize} = context;
    const transaction = await sequelize.transaction();
    try {
      const currentLatestDrawing =
        await repositories.projectDrawingRepository.findOne(
          {drawingListId},
          excludeKeys,
          false,
          [],
          {
            order: [['id', 'DESC']],
            transaction,
          },
        );

      if (!currentLatestDrawing) {
        throw new CustomError('Drawings not available with the given id', 404);
      }

      if (
        currentLatestDrawing.dataValues.status ===
          ProjectDrawingStatus.APPROVED ||
        currentLatestDrawing.dataValues.deletedAt
      ) {
        throw new CustomError(
          `Drawing with ID ${id} is already approved/deleted`,
          409,
        );
      }

      if (id > currentLatestDrawing.dataValues.id) {
        throw new CustomError('Drawing not found', 404);
      }

      if (currentLatestDrawing.dataValues.id !== id) {
        throw new CustomError(
          'You can’t delete a previous drawing version.',
          409,
        );
      }

      const deleted = {
        deletedAt: new Date(),
        deletedBy: user.user_id,
      };

      await repositories.projectDrawingRepository.updateAll(
        deleted,
        {id: id},
        transaction,
      );
      await transaction.commit();
      return {message: 'Drawing deleted successfully'};
    } catch (error) {
      await transaction.rollback();
      logger.error(
        '[ProjectDrawingService] Error in deleteProjectDrawings',
        error,
      );
      throw error;
    }
  }
}

export default new ProjectDrawingsService();
