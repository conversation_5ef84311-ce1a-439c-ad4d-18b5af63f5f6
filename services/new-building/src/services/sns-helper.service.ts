import {
  SNSClient,
  ConfirmSubscriptionCommand,
  ConfirmSubscriptionCommandOutput,
} from '@aws-sdk/client-sns';
import {logger} from '../utils';
export class SNSHelperService {
  private readonly snsClient: SNSClient;
  constructor() {
    this.snsClient = new SNSClient({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });
  }
  /**
   * The function confirms a subscription to a topic using the provided topic ARN and token.
   * @param {string} topicARN - The topicARN parameter is a string that represents the Amazon Resource
   * Name (ARN) of the topic to which the subscription confirmation request is being sent. The ARN
   * uniquely identifies the topic in Amazon Simple Notification Service (SNS).
   * @param {string} token - The `token` parameter is a unique identifier that is provided by the
   * Amazon Simple Notification Service (SNS) when a subscription is created. It is used to confirm the
   * subscription and verify the endpoint.
   * @returns a Promise that resolves to a ConfirmSubscriptionCommandOutput object.
   */
  async confirmSubscription(
    topicARN: string,
    token: string,
  ): Promise<ConfirmSubscriptionCommandOutput> {
    const command = new ConfirmSubscriptionCommand({
      TopicArn: topicARN,
      Token: token,
    });
    return this.snsClient.send(command);
  }
  /**
   * The function `confirmSnsSubscription` confirms a subscription to an SNS topic and logs the
   * confirmation status.
   * @param {AnyObject} body - The `body` parameter is an object that contains the following
   * properties:
   * @returns a Promise that resolves to boolean.
   */
  async confirmSnsSubscription(body: any): Promise<boolean> {
    console.log({body})
    const {TopicArn, Token, UnsubscribeURL} = body;
    if (UnsubscribeURL) {
      return true;
    }
    try {
      const response = await this.confirmSubscription(TopicArn, Token);
      // Check if the response indicates a successful confirmation
      if (response.SubscriptionArn) {
        logger.info(
          'Subscription confirmed successfully:',
          response.SubscriptionArn,
        );
      } else {
        logger.info(
          'Subscription confirmation response:',
          JSON.stringify(response),
        );
      }
      return false;
    } catch (error) {
      logger.error('Error confirming subscription:', (error as any)?.message);
      return false;
    }
  }
}
export default new SNSHelperService();