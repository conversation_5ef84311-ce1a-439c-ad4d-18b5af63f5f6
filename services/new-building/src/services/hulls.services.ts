import { CreationAttributes, Model, Op, Transaction } from 'sequelize';
import {
  excludeKeys,
  includesHullDataWithExcludedKeys,
  includesProjectDataExcludedKeys,
} from '../utils/exclude-key.utils';
import {
  HullClassMapping,
  IFindQuery,
  IFindAllQuery,
  ProjectHullMapping,
  User,
  MasterData,
} from '../types';
import { CustomError, logger, ContextType } from '../utils';
import { S3HelperService, vesselMasterDataService } from '.';
import { HullClassCreationAttributes } from '../models/hull.class.model';
import { HullStatus } from '../enums';
import { HullInstance } from '../models/hull.model';

// Get the instance type of the Hull model
type HullInput = CreationAttributes<HullInstance> & {
  vesselClasses?: string[];
};

/**
 * Retrieves a paginated list of hulls with optional search filtering.
 *
 * @param query - Query parameters for pagination and filtering.
 * @param query.page - Page number for pagination (default is 1).
 * @param query.limit - Number of items per page (default is 10).
 * @param query.search - Optional search keyword to filter hulls by hull number.
 *
 * @returns An object containing:
 *  - `data`: Array of hulls matching the criteria.
 *  - `pagination`: Metadata including total items, total pages, current page, and page size.
 */
export async function getAllHulls(query: IFindAllQuery, context: ContextType) {
  const { repositories, user } = context;
  const { page = 1, limit = 10, search } = query;

  const offset = (page - 1) * limit;

  const findQuery: IFindQuery = {
    orderBy: [],
    limit,
    offset,
    page,
    where: { tenantId: user.tenantId } as Record<string, any>,
    attributes: ['id', 'hullNo'],
  };

  if (search && typeof search === 'string') {
    findQuery.where = {
      ...findQuery.where,
      [Op.or]: [{ hullNo: { [Op.iLike]: `%${search}%` } }],
    };
  }

  const { count, rows } =
    await repositories.hullRepository.findAndCountAll(findQuery);

  const totalPages = Math.ceil(count / limit);

  return {
    data: rows,
    pagination: {
      totalItems: count,
      totalPages,
      page: page,
      pageSize: limit,
    },
  };
}

/**
 * Retrieves a single hull by its ID with detailed related data and asset information.
 *
 * Features:
 * - Includes related models (`shipType`, `flags`, `vesselclass`, and associated `projects`)
 *   with excluded metadata fields (`createdBy`, `updatedBy`, etc.).
 * - Retrieves associated asset (if any) based on hull ID and `AssetsReferenceType.HULL`.
 * - If asset exists and has a valid `assetPath`, generates a pre-signed S3 URL for secure access.
 * - Merges hull and asset data into a single response object.
 *
 * @param {number} id - The unique identifier of the hull.
 * @returns {Promise<Object | null>} Returns the hull and asset data or null if the hull is not found.
 */
export async function findByIdHull(id: number, context: ContextType) {
  const { repositories, models } = context;
  const includes = [
    ...includesHullDataWithExcludedKeys(models),
    {
      model: models.Project,
      as: 'projects',
      attributes: {
        exclude: excludeKeys,
      },
      through: { attributes: [] },
      required: false,
      includes: [...includesProjectDataExcludedKeys(models)],
    },
  ];

  const hull = await repositories.hullRepository.findById(id, true, {
    include: includes,
  });
  if (!hull) throw new CustomError('Hull id not found', 404);
  if (hull?.dataValues?.assetPath) {
    const url = await S3HelperService.generatePresignedDownloadUrl(
      hull.dataValues.assetPath,
      hull.dataValues.assetName!,
    );
    hull.dataValues.assetPath = url;
  }

  const data = {
    ...hull?.dataValues,
  };

  return data;
}

/**
 * Creates a new hull associated with a specific project, optionally attaching an asset.
 *
 * Steps:
 * 1. Validates the given `projectId` and master data (`shipTypeId`, `flagId`, `classId`).
 * 2. Creates the hull entry in the database.
 * 3. Establishes a link between the created hull and the specified project using `createProjectHull`.
 * 4. If asset information is provided (`assetName` and `referenceType`), creates an asset record
 *    associated with the hull using `createAssets`.
 * 5. Logs success messages based on whether an asset was created.
 *
 * Error Handling:
 * - Logs and throws a descriptive error message if any validation or creation step fails.
 *
 * @param {number} projectId - The ID of the project the hull should be associated with.
 * @param {HullInput} data - The input data for creating the hull, including optional asset data.
 * @returns {Promise<Model>} - Returns the created hull instance.
 */
export async function createHull(
  projectId: number,
  data: HullInput,
  context: ContextType,
) {
  const { sequelize } = context;
  const t = await sequelize.transaction();
  try {
    await checkForValidProjectData(projectId, context);
    await checkValidHullNo(data.hullNo, projectId, context);
    const masterData = await vesselMasterDataService.getVesselMasterData();
    validateMasterData(masterData.data, data.vesselClasses, data.flag);
    const { createdHulls } = await createHullList(projectId, [data], t, context);
    await t.commit();
    return createdHulls[0];
  } catch (error) {
    await t.rollback();
    logger.error('[HullService] Error in checkForValidData', error);
    throw error;
  }
}

/**
 * Creates multiple hulls and associates them with a project, optionally creating assets
 * for hulls that contain asset data.
 *
 * Steps:
 * 1. Uses `Hull.bulkCreate` to insert multiple hull records into the database within
 *    the provided transaction (`t`), and returns the created hulls.
 * 2. Maps over the created hulls to create an array of `projectHullData` objects, linking
 *    each hull to the specified `projectId` and associating it with the current user via
 *    the `createdBy` field.
 * 3. Uses `ProjectHull.bulkCreate` to associate all the hulls with the project in a
 *    single database transaction.
 * 4. Iterates over the hulls and their corresponding input data to create an array of
 *    `assetData` objects for hulls that contain both an `assetName` and `referenceType`.
 * 5. Filters out `null` values (i.e., hulls without asset data) and returns a list of
 *    valid asset data.
 *
 * @param {number} projectId - The ID of the project to which the hulls will be linked.
 * @param {HullInput[]} hulls - An array of input data for the hulls being created, which
 *                              may include asset information.
 * @param {Transaction} t - The Sequelize transaction to ensure all database operations
 *                           are executed atomically.
 * @returns {Promise<AssetData[]>} - Returns an array of asset data objects to be created
 *                                  for hulls with associated asset information.
 */
export async function createHullList(
  projectId: number,
  hulls: HullInput[],
  t: Transaction,
  context: ContextType,
) {
  const { models, user } = context;
  const classSet = new Set<string>();
  const hullNos: string[] = [];
  hulls.forEach(({ vesselClasses, hullNo }) => {
    if (hullNos.includes(hullNo)) {
      throw new CustomError(
        'The entered hull number already exists. Please provide a unique hull number.',
        409,
      );
    }
    hullNos.push(hullNo);
    if (Array.isArray(vesselClasses)) {
      vesselClasses.forEach(data => classSet.add(data));
    }
  });
  const finalHullsToInsert = hulls.map(hull => {
    const { vesselClasses, ...rest } = hull;
    return {
      ...rest,
      createdBy: user.user_id,
      tenantId: Number(user.tenantId), // Ensure tenantId is a number
    };
  });

  const createdHulls = await models.Hull.bulkCreate(finalHullsToInsert, {
    returning: true,
    transaction: t,
  });

  const [projectHullData, hullClassMappings] = createdHulls.reduce(
    ([projectData, classMappings], hull, index) => {
      const input = hulls[index];
      const createdBy = hull.dataValues.createdBy ?? '';
      projectData.push({
        projectId,
        hullId: Number(hull.dataValues.id),
        createdBy,
      });
      const classes = input.vesselClasses;
      classes?.forEach(vesselClass => {
        classMappings.push({
          hullId: hull.dataValues.id,
          vesselClass,
          createdBy: createdBy,
        });
      });
      return [projectData, classMappings];
    },
    [[], []] as [ProjectHullMapping[], HullClassMapping[]],
  );

  await Promise.all([
    models.ProjectHull.bulkCreate(projectHullData, { transaction: t }),
    models.HullClass.bulkCreate(hullClassMappings, { transaction: t }),
  ]);

  return { createdHulls };
}

/**
 * Updates the details of an existing hull in the database. If the hull is found,
 * the function will validate the master data, apply the updates, and return the
 * updated hull record.
 *
 * Steps:
 * 1. Attempts to find the hull by the provided `id`. If the hull does not exist,
 *    it logs an error and throws an exception.
 * 2. Validates the master data using `checkForValidMasterData` based on the updated
 *    `shipTypeId`, `flagId`, and `classId`.
 * 3. Uses `hullRepository.update` to apply the updates to the hull record in the database.
 * 4. Retrieves the updated hull from the database to return the updated details.
 * 5. Logs the update event, including the `user_id` of the user making the change and
 *    the associated `tenantId`.
 *
 * @param {number} id - The ID of the hull to be updated.
 * @param {Partial<CreationAttributes<HullInstance>>} updates - The updates to be applied
 *                                                           to the hull. Only the provided fields
 *                                                           will be updated.
 * @param {User} user - The user who is making the update, used for logging.
 * @returns {Promise<HullInstance>} - Returns the updated hull record.
 * @throws {Error} - Throws an error if the hull is not found or if the update operation fails.
 */
export async function updateHull(
  id: number,
  projectId: number,
  updates: HullInput,
  user: User,
  context: ContextType,
) {
  const { repositories, models, sequelize } = context;
  const t = await sequelize.transaction();
  try {
    const existingHull = await repositories.hullRepository.findById(id);
    validateExistingHull(id, existingHull);

    const projectHullData = await repositories.projectHullRepository.findOne({
      hullId: id,
      projectId: projectId,
      deletedAt: null,
    });

    if (!projectHullData) {
      throw new CustomError(`Hull is not associated with project`, 409);
    }

    const { vesselClasses, flag, hullNo } = updates;
    const masterData = await vesselMasterDataService.getVesselMasterData();
    validateMasterData(masterData.data, vesselClasses, flag);
    if (existingHull?.dataValues.hullNo !== hullNo) {
      await checkValidHullNo(hullNo, projectId, context);
    }

    await repositories.hullRepository.update(id, updates, t);

    await repositories.hullClassRepository.updateAll(
      { deletedBy: user.user_id, deletedAt: new Date() },
      {
        hullId: id,
        vesselClass: { [Op.notIn]: vesselClasses },
      },
      t,
    );

    const existingClassNames = (await models.HullClass.findAll({
      where: {
        hullId: id,
        vesselClass: { [Op.in]: vesselClasses },
        deletedAt: null,
      },
      attributes: ['hullId', 'vesselClass'],
      raw: true,
    })) as unknown as Array<{ hullId: number; vesselClass: string }>;

    console.log({ existingClassNames });
    const existingSet = new Set(
      existingClassNames.map(item => item.vesselClass),
    );
    const newClassNames = vesselClasses?.filter(
      vesselClass => !existingSet.has(vesselClass),
    );

    const hullClassData = newClassNames?.map(vesselClass => ({
      vesselClass,
      hullId: id,
      createdBy: user.user_id,
      deletedAt: null,
    })) as unknown as HullClassCreationAttributes[];

    if (hullClassData) {
      await models.HullClass.bulkCreate(hullClassData, { transaction: t });
    }

    await t.commit();
    const returnHull = await repositories.hullRepository.findById(id);
    logger.info('Hull update triggered', {
      updatedBy: user.user_id,
      tenantId: Number(user['tenantId']),
    });
    return returnHull;
  } catch (error) {
    await t.rollback();
    logger.error('[HullService] Error in update hull', error);
    throw error;
  }
}

/**
 * Marks a hull as deleted by updating its `deleted` status, along with the `deletedAt`
 * timestamp and `deletedBy` user ID. This is a soft delete, meaning the hull is not
 * actually removed from the database, but flagged as deleted.
 *
) {
 * Steps:
 * 1. The hull is found by its `id` and the `deleted` status is set to `true`.
 * 2. The `deletedAt` field is set to the current timestamp, and `deletedBy` is set to
 *    the ID of the user performing the deletion.
 * 3. The hull record is updated with the new information.
 *
 * @param {number} hullId - The ID of the hull to be deleted.
 * @param {User} user - The user who is performing the deletion, used for logging.
 * @returns {Promise<[number, HullInstance[]]>} - Returns the result of the update operation,
 *                                                which includes the number of affected rows
 *                                                and the updated hull data.
 */
export async function deleteHull(
  hullId: number,
  user: User,
  context: ContextType,
) {
  const { repositories, models, sequelize } = context;
  const t = await sequelize.transaction();
  try {
    const existingHull = await repositories.hullRepository.findById(hullId);
    validateExistingHull(hullId, existingHull);

    const deleted = {
      deletedAt: new Date(),
      deletedBy: user.user_id,
    };

    await Promise.all([
      models.Hull.update(deleted, {
        where: { id: hullId },
        transaction: t,
      }),
      models.HullClass.update(deleted, {
        where: { hullId: hullId },
        transaction: t,
      }),
      models.ProjectHull.update(deleted, {
        where: { hullId: hullId },
        transaction: t,
      }),
    ]);

    await t.commit();
    return { message: 'Hull deleted successfully' };
  } catch (error) {
    await t.rollback();
    throw error;
  }
}

/**
 * Validates the provided project ID by checking its existence in the project repository.
 * Throws an error if the project ID is invalid.
 *
 * @param projectID - The ID of the project to validate.
 * @throws {CustomError} Throws a `CustomError` with a status code of 400 if the project ID is invalid.
 * @returns {Promise<void>} Resolves if the project ID is valid.
 */
export async function checkForValidProjectData(
  projectID: number,
  context: ContextType,
): Promise<void> {
  const { repositories } = context;
  const project = await repositories.projectRepository.findById(projectID);
  if (!project) {
    throw new CustomError(`Invalid project ID provided: ${projectID}`, 400);
  }
}

/**
 * Checks if the given hull number is valid for the specified project.
 * Ensures that the hull number does not already exist in the database for the given project.
 * Throws an error if a duplicate hull number is found.
 *
 * @param hullNo - The hull number to validate.
 * @param projectId - The ID of the project to check against.
 * @returns A promise that resolves if the hull number is valid, or rejects with an error if invalid.
 * @throws CustomError - If the hull number already exists for the specified project.
 */
export async function checkValidHullNo(
  hullNo: string,
  projectId: number,
  context: ContextType,
): Promise<void> {
  const { repositories, models } = context;
  let include = [
    {
      model: models.Project,
      as: 'projects',
      attributes: {
        exclude: excludeKeys,
      },
      where: {
        id: projectId,
        deletedAt: null,
      },
      through: { attributes: [] },
      required: true,
    },
  ];

  const hullsFound = await repositories.hullRepository.findAll(
    [],
    false,
    { hullNo: hullNo },
    include,
  );

  if (hullsFound.length !== 0) {
    throw new CustomError(
      'The entered hull number already exists. Please provide a unique hull number.',
      409,
    );
  }
}

/**
 * Validates the existence and status of a hull based on its ID and attributes.
 *
 * @param id - The unique identifier of the hull to validate.
 * @param existingHull - The hull object to validate, which can either be an instance of the Hull model
 *                       with its attributes or `null` if the hull does not exist.
 *
 * @throws {CustomError} If the hull does not exist, a `CustomError` with a 404 status code is thrown.
 * @throws {CustomError} If the hull is already completed or deleted, a `CustomError` with a 409 status code is thrown.
 */
function validateExistingHull(id: number, existingHull: HullInstance | null) {
  if (!existingHull) {
    logger.error(`[HullService] Hull with ID ${id} not found`);
    throw new CustomError(`Hull with ID ${id} not found`, 404);
  }

  if (
    existingHull.dataValues.status === HullStatus.COMPLETED ||
    existingHull.dataValues.deletedAt
  ) {
    logger.error(
      `[HullService] Hull with ID ${id} is already completed/deleted`,
    );
    throw new CustomError(
      `Hull with ID ${id} is already completed/deleted`,
      409,
    );
  }
}

/**
 * Validates the master data against provided vessel classes and flag.
 * Throws an error if any of the provided vessel classes or flag is invalid.
 *
 * @param masterData - The master data object containing available vessel classes and flags.
 * @param vesselClasses - An optional array of vessel class identifiers to validate.
 * @param flag - An optional flag identifier to validate.
 * @throws {CustomError} If any of the provided vessel classes are not present in the master data.
 * @throws {CustomError} If the provided flag is not present in the master data.
 */
function validateMasterData(
  masterData: MasterData,
  vesselClasses?: string[],
  flag?: string,
): void {
  if (vesselClasses) {
    const availableClasses = masterData.vesselClasss;
    const classValues = availableClasses.map(item => item.value);
    const allClassesPresent = vesselClasses.every(cls =>
      classValues.includes(cls),
    );
    if (!allClassesPresent)
      throw new CustomError('Invalid vessel class provided', 400);
  }

  if (flag) {
    const availableFlags = masterData.flags;
    const flagValues = availableFlags.map(item => item.value);
    const isFlagPresent = flagValues.includes(flag);
    if (!isFlagPresent)
      throw new CustomError(`Invalid flag provided: ${flag}`, 400);
  }
}
