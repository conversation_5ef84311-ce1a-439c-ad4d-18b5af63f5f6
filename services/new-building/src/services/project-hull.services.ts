import {CreationAttributes} from 'sequelize';
import {ContextType} from '../utils';
import {User} from '../types';
import {excludeKeys} from '../utils/exclude-key.utils';

type ProjectHullInstance = InstanceType<ContextType['models']['ProjectHull']>;

/**
 * Creates a new project hull record in the database.
 *
 * @param data - The attributes required to create a new instance of `ProjectHullInstance`.
 * @returns A promise that resolves to the created `ProjectHullInstance`.
 */
export async function createProjectHull(
  data: CreationAttributes<ProjectHullInstance>,
  context: ContextType,
) {
  return context.repositories.projectHullRepository.create(data);
}

export async function getProjectHull(
  where: Record<string, any>,
  context: ContextType,
) {
  return context.repositories.projectHullRepository.findOne(where, excludeKeys);
}

export async function deleteProjectHull(
  id: number,
  user: User,
  context: ContextType,
) {
  return context.repositories.projectHullRepository.update(id, {
    deletedAt: new Date(),
    deletedBy: user.user_id,
  });
}
