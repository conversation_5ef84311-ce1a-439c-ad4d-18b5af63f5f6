

// export class SQSHelperService {
//   private readonly sqsClient: SQSClient;
//   constructor() {
//     this.sqsClient = new SQSClient({
//       region: process.env.AWS_REGION,
//       credentials: {
//         accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
//         secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
//       },
//     });
//   }
//   /**
//    * The function receives messages from an SQS queue and processes them.
//    * @param {string} QUEUE_URL - The `QUEUE_URL` parameter is a string that represents the URL of the
//    * Amazon SQS queue from which messages will be received. It is used to identify the specific queue
//    * from which messages will be retrieved.
//    * @returns a Promise that resolves to void.
//    */
//   async receiveSqsMessages(QUEUE_URL: string): Promise<string | undefined> {
//     try {
//       const command = new ReceiveMessageCommand({
//         QueueUrl: QUEUE_URL,
//         MaxNumberOfMessages: 10,
//         WaitTimeSeconds: 20, // Long polling
//       });
//       const response = await this.sqsClient.send(command);
//       let tenantKey;
//       console.log({response}, 'sqs response full');
//       if (response.Messages) {
//         for (const message of response.Messages) {
//           // Process your message here
//           console.log({message}, 'sqs message full');
//           console.log({body: message.Body}, 'sqs message body');
//           if (typeof message.Body === 'string') {
//             try {
//               // Attempt to parse the message body as JSON
//               const decoded = Buffer.from(message.Body, 'base64').toString(
//                 'utf-8',
//               );
//               const tenant = JSON.parse(decoded);
//               tenantKey = tenant.key;
//               console.log({tenant}, 'sqs message tenant');
//               console.log({tenantKey});
//               console.log(
//                 {receipt: message.ReceiptHandle},
//                 'sqs message receipt handle',
//               );
//             } catch (parseError) {
//               // If parsing fails, log the error and continue
//               logger.error(
//                 '[SQS] Error parsing message body as JSON:',
//                 parseError,
//               );
//               if (message.ReceiptHandle) {
//                 await this.deleteSqsMessage(QUEUE_URL, message.ReceiptHandle);
//               }
//             }
//           } else {
//             logger.warn(
//               'Received SQS message with undefined or non-string Body:',
//               message,
//             );
//           }
//           if (message.ReceiptHandle) {
//             await this.deleteSqsMessage(QUEUE_URL, message.ReceiptHandle);
//           }
//         }
//       }

//       return tenantKey;
//     } catch (error) {
//       logger.error('[SQS] Error receiving messages:', error);
//     }
//   }

//   async deleteSqsMessage(
//     QUEUE_URL: string,
//     receiptHandle: string,
//   ): Promise<void> {
//     try {
//       const command = new DeleteMessageCommand({
//         QueueUrl: QUEUE_URL,
//         ReceiptHandle: receiptHandle,
//       });
//       await this.sqsClient.send(command);
//       logger.info('[SQS] Message deleted successfully');
//     } catch (error) {
//       logger.error('[SQS] Error deleting message:', error);
//     }
//   }
// }



// src/services/sqs-helper.ts
import {
  SQSClient,
  ReceiveMessageCommand,
  DeleteMessageCommand,
  ReceiveMessageCommandInput,
  DeleteMessageCommandInput,
  ReceiveMessageCommandOutput,
} from '@aws-sdk/client-sqs';

import { logger } from '../utils';

type Tenant = {
  tenantId: number;
  companyName: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  key: string;
};

export default class SqsHelperService {
  private readonly sqs: SQSClient;

  constructor() {
    this.sqs = new SQSClient({
      region: process.env.AWS_REGION ?? 'ap-southeast-1',
    });
  }

  /**
   * Receive and yield messages from an SQS queue.
   */
  async *receiveMessages(
    params: ReceiveMessageCommandInput,
  ): AsyncIterableIterator<{
    records: Tenant[];
    sqsMessage: Pick<
      Exclude<ReceiveMessageCommandOutput['Messages'], undefined>[0],
      'MessageId' | 'ReceiptHandle'
    >;
  }> {
    const response = await this.sqs.send(new ReceiveMessageCommand(params));

    for (const msg of response.Messages ?? []) {
      if (msg.Body) {
        try {
          const decoded = Buffer.from(msg.Body, 'base64').toString('utf-8');
          console.log({ decoded }, 'Decoded SQS message body');
          const parsed = JSON.parse(decoded);

          const records = Array.isArray(parsed) ? parsed : [parsed];
          console.log({ parsed }, 'Parsed records from SQS message body');
          yield {
            records,
            sqsMessage: {
              MessageId: msg.MessageId!,
              ReceiptHandle: msg.ReceiptHandle!,
            },
          };
        } catch (error) {
          logger.error('Failed to parse SQS message body', {
            error,
            body: msg.Body,
          });
        }
      }
    }
  }

  /**
   * Delete a message from the SQS queue.
   */
  async deleteMessage(params: DeleteMessageCommandInput): Promise<void> {
    try {
      await this.sqs.send(new DeleteMessageCommand(params));
      logger.info('Message deleted from SQS:', {
        receiptHandle: params.ReceiptHandle,
      });
    } catch (error) {
      logger.error('Error deleting SQS message', { error, params });
    }
  }
}
