import {
  CreationAttributes,
  Includeable,
  Op,
  Order,
  WhereOptions,
} from 'sequelize';
import { createHullList, S3HelperService, vesselMasterDataService } from '.';
import { AnyObject, IFindQuery, IFindAllQuery, User, MasterData } from '../types';
import {
  ContextType,
  CustomError,
  isUserHasPermission,
  logger,
} from '../utils';
import {
  HullPermissions,
  HullStatus,
  ProjectDrawingStatus,
  ProjectStatus,
} from '../enums';
import {
  includesProjectData,
  includesProjectDataExcludedKeys,
  excludeKeys,
  includesHullDataWithExcludedKeys,
} from '../utils/exclude-key.utils';
import { ProjectInstance } from '../models/project.model';
import { HullInstance } from '../models/hull.model';


/**
 * Represents a project with associated hulls and their respective asset details.
 * This type is used to create a project and its related hulls in one operation,
 * while optionally specifying asset information for both the project and its hulls.
 */
type ProjectWithHullInput = CreationAttributes<ProjectInstance> & {
  hulls: Array<
    CreationAttributes<HullInstance> & {
      vesselClasses?: string[];
    }
  >;
};

/**
 * The function `getAllProjects` retrieves projects based on specified query parameters, including
 * pagination, filtering, sorting, and search criteria.
 * @param {IFindAllQuery} query - The `getAllProjects` function you provided is an asynchronous
 * function that retrieves projects based on the specified query parameters. Here is an explanation of
 * the parameters used in the function:
 * @returns The function `getAllProjects` returns an object with two properties:
 * 1. `data`: An array of projects that match the query criteria.
 * 2. `pagination`: An object containing information about pagination, including:
 *    - `totalItems`: The total number of projects that match the query.
 *    - `totalPages`: The total number of pages based on the limit.
 *    - `page`: The default value is 1
/**
 * Retrieves a paginated list of projects based on the provided query parameters.
 * This function allows filtering, sorting, and searching for projects and includes related models like Hull and ProjectType.
 *
 * @param {IFindAllQuery} query - The query parameters for filtering, sorting, and pagination.
 * @returns {Promise} - A promise that resolves with an object containing the list of projects and pagination details.
 *
 * @throws {Error} - Throws an error if the database query encounters issues.
 */
export async function findAllProjects({ repositories }: ContextType) {
  return repositories.projectRepository.findAll();
}

export async function getAllProjects(
  query: IFindAllQuery,
  { repositories, models, user }: ContextType,
) {
  const {
    page = 1,
    limit = 10,
    fields,
    sortBy = 'createdAt',
    sortOrder = 'DESC',
  } = query;

  const offset = (page - 1) * limit;

  let order: Order = [];
  let include: Includeable[] = [...includesProjectData(models)];

  let fieldArray = undefined;
  if (fields) {
    fieldArray = Array.isArray(fields) ? fields : [fields];
  }

  const findQuery: IFindQuery = {
    orderBy: order,
    limit,
    offset,
    page,
    attributes: fieldArray,
    where: buildWhereClause(query, user),
  };

  const joinSortMap = {
    projectType: {
      model: models.ProjectType,
      as: 'projectType',
      sortByField: 'name',
    },
    fuelType: { model: models.FuelType, as: 'fuelType', sortByField: 'name' },
  };

  if (sortBy in joinSortMap) {
    const { model, as, sortByField } =
      joinSortMap[sortBy as keyof typeof joinSortMap];
    order.push([{ model, as }, sortByField, sortOrder]);
  } else {
    order.push([sortBy, sortOrder]);
  }

  findQuery.include = include;
  const { count, rows: projects } =
    await repositories.projectRepository.findAndCountAll(findQuery);

  const totalPages = Math.ceil(count / limit);

  return {
    data: projects,
    pagination: {
      totalItems: count,
      totalPages,
      page: page,
      pageSize: limit,
    },
  };
}

/**
 * Builds a Sequelize `WhereOptions` object based on the provided query parameters.
 * This function filters the query parameters to include only allowed filters and constructs
 * conditions for filtering, searching, and date ranges.
 *
 * @param query - An object containing the query parameters for filtering and searching.
 *
 * @returns A `WhereOptions` object that can be used in Sequelize queries.
 *
 * ### Query Parameters:
 * - `projectTypeId`, `fuelTypeId`, `shipType`, `engineType`, `status`, `currency`:
 *   These parameters are directly mapped to the `where` clause if present in the query.
 * - `createdAt`: An object containing `startDate` and/or `endDate` for filtering by creation date.
 *   - `startDate`: Filters records created on or after this date.
 *   - `endDate`: Filters records created on or before this date.
 * - `search`: A string used for performing a case-insensitive search across multiple fields.
 *   - If `searchParam` is provided, the search is restricted to the specified field.
 *   - Otherwise, the search is performed across predefined fields such as `name`, `projectDescription`,
 *     `owner`, `engineType`, `shipType`, `currency`, and related fields like `projectType.name` and `fuelType.name`.
 *
 * ### Notes:
 * - The function uses Sequelize operators like `$gte`, `$lte`, and `Op.iLike` for constructing conditions.
 * - The `Op.or` operator is used for combining multiple search conditions.
 */
function buildWhereClause(query: IFindAllQuery, user: User): WhereOptions {
  const allowedFilters = [
    'projectTypeId',
    'fuelTypeId',
    'shipType',
    'engineType',
    'status',
    'currency',
  ];

  const where: WhereOptions = { tenantId: user.tenantId };

  for (const filter of allowedFilters) {
    if (query[filter]) {
      where[filter] = query[filter];
    }
  }

  if (query.createdAt?.startDate || query.createdAt?.endDate) {
    where.createdAt = {};
    if (query.createdAt.startDate) {
      where.createdAt['$gte'] = new Date(query.createdAt.startDate);
    }
    if (query.createdAt.endDate) {
      where.createdAt['$lte'] = new Date(query.createdAt.endDate);
    }
  }

  if (query.search && typeof query.search === 'string') {
    if (query.searchParam) {
      where[query.searchParam] = { [Op.iLike]: `%${query.search}%` };
    } else {
      Object.assign(where, {
        [Op.or]: [
          { name: { [Op.iLike]: `%${query.search}%` } },
          { projectDescription: { [Op.iLike]: `%${query.search}%` } },
          { owner: { [Op.iLike]: `%${query.search}%` } },
          { engineType: { [Op.iLike]: `%${query.search}%` } },
          { shipType: { [Op.iLike]: `%${query.search}%` } },
          { currency: { [Op.iLike]: `%${query.search}%` } },
          { '$projectType.name$': { [Op.iLike]: `%${query.search}%` } },
          { '$fuelType.name$': { [Op.iLike]: `%${query.search}%` } },
        ],
      });
    }
  }

  return where;
}

/**
 * Finds a project by its ID and returns its data, including related hulls if the user has the appropriate permissions.
 * 
 * - Checks if the user has `HullPermissions.VIEW` permission to determine if hull data should be included.
 * - Includes related models and hulls in the query as needed.
 * - Generates presigned S3 download URLs for asset paths in both the project and its hulls, if present.
 * - Throws a `CustomError` if the project is not found.
 * 
 * @param id - The unique identifier of the project to find.
 * @param user - The user requesting the project, used for permission checks and tenant scoping.
 * @param context - The context object containing repositories and models.
 * @returns The project data, with asset paths replaced by presigned S3 URLs where applicable.
 * @throws CustomError if the project is not found.
 */
export async function findByIdProject(
  id: number,
  user: User,
  { repositories, models }: ContextType,
) {
  const isHullViewPermission = isUserHasPermission(user, [
    HullPermissions.VIEW,
  ]);
  const includes = [
    ...includesProjectDataExcludedKeys(models),
    ...(isHullViewPermission
      ? [
        {
          model: models.Hull,
          as: 'hulls',
          attributes: {
            exclude: excludeKeys,
          },
          through: { attributes: [] },
          required: false,
          where: { deletedAt: null },
          include: [...includesHullDataWithExcludedKeys(models)],
        },
      ]
      : []),
  ];
  const project = await repositories.projectRepository.findOne(
    { id: id, tenantId: user.tenantId },
    [],
    false,
    includes,
  );
  if (!project) throw new CustomError('Project not found', 404);
  if (isHullViewPermission) {
    const typedProject = project as ProjectInstance & { hulls?: HullInstance[] };

    for (const data of typedProject?.hulls ?? []) {
      if (data?.dataValues.assetPath) {
        const url = await S3HelperService.generatePresignedDownloadUrl(
          data.dataValues.assetPath,
          data.dataValues.assetName!,
        );
        data.dataValues.assetPath = url;
      }
    }
  }
  if (project?.dataValues?.assetPath) {
    const url = await S3HelperService.generatePresignedDownloadUrl(
      project.dataValues.assetPath,
      project.dataValues.assetName!,
    );

    project.dataValues.assetPath = url;
  }

  const data = {
    ...project?.dataValues,
  };

  return data;
}

/**
 * Creates a new project along with optional hulls, performing necessary permission checks and master data validation.
 *
 * @param data - The project data, potentially including hulls and related information.
 * @param user - The user performing the operation, used for permission checks.
 * @param context - The context object containing repositories, models, and sequelize instance.
 * @returns The created Project instance.
 * @throws {CustomError} If the user lacks permission to create hulls.
 * @throws {Error} If master data validation fails or any database operation fails.
 *
 * @remarks
 * - Rolls back the transaction if any error occurs during project or hull creation.
 * - Logs success and failure events.
 */
export async function createProject(
  data: ProjectWithHullInput,
  user: User,
  { repositories, models, sequelize }: ContextType,
) {
  const t = await sequelize.transaction();
  try {
    if (data.hulls && !isUserHasPermission(user, [HullPermissions.CREATE])) {
      throw new CustomError(
        'You do not have permission to perform this action.',
        403,
      );
    }

    const masterData = await vesselMasterDataService.getVesselMasterData();
    checkForValidMasterData(
      masterData.data,
      data.currency,
      data.isShipTypeCustom ? null : data.shipType,
      data.isEngineTypeCustom ? null : data.engineType,
    );
    const createdProject = await models.Project.create(data, {
      returning: true,
      transaction: t,
    });
    const projectId = createdProject.getDataValue('id');

    if (data.hulls) {
      data.hulls.forEach(({ vesselClasses, flag }) => {
        checkForValidMasterData(
          masterData.data,
          undefined,
          undefined,
          undefined,
          vesselClasses,
          flag,
        );
      });
      await createHullList(Number(projectId), data.hulls, t, { repositories, models, sequelize, user });
    }
    await t.commit();
    logger.info('[ProjectService] Project created succesfully');
    return createdProject;
  } catch (error) {
    await t.rollback();
    logger.error(
      '[ProjectService] insertBulkHullsWithProject Transaction failed:',
      error,
    );

    throw error;
  }
}

/**
 * Updates an existing project with the provided updates.
 *
 * @param id - The ID of the project to update.
 * @param updates - Partial object containing the fields to update in the project.
 * @param user - The user performing the update operation.
 * @param context - The context object containing repositories, models, and sequelize instance.
 * @returns The updated project instance.
 * @throws Error if the project with the given ID is not found.
 *
 * @remarks
 * - Validates master data before updating the project.
 * - Uploads project logs to S3 after the update.
 * - Logs the update operation with user and tenant information.
 */
export async function updateProject(
  id: number,
  updates: Partial<CreationAttributes<ProjectInstance>>,
  user: AnyObject,
  { repositories, models, sequelize }: ContextType,
) {
  const existingProject = await repositories.projectRepository.findById(id);
  if (!existingProject) {
    logger.error(`[ProjectService] Project with ID ${id} not found`);
    throw new Error(`Project with ID ${id} not found`);
  }

  if (
    existingProject.dataValues.status === ProjectStatus.CLOSED ||
    existingProject.dataValues.deletedAt
  ) {
    throw new CustomError(
      `Project with ID ${id} is already completed/deleted`,
      409,
    );
  }

  const masterData = await vesselMasterDataService.getVesselMasterData();
  checkForValidMasterData(
    masterData.data,
    updates.currency,
    updates.isShipTypeCustom ? null : updates.shipType,
    updates.isEngineTypeCustom ? null : updates.engineType,
  );

  await repositories.projectRepository.update(id, updates);
  await S3HelperService.uploadProjectLogsToS3(
    Number(user['tenantId']),
    Number(id),
    existingProject,
  );
  const returnProject = await repositories.projectRepository.findById(id);
  logger.info('Project update triggered', {
    updatedBy: user.user_id,
    tenantId: Number(user['tenantId']),
  });
  return returnProject;
}

/**
 * Completes a project by updating its status to CLOSED, setting the completion date,
 * and optionally updating expenses and currency. This function performs several validation
 * checks to ensure the project can be closed, such as verifying that all hulls are completed
 * and all approval drawings have an approved version. If any validation fails, a `CustomError`
 * is thrown. The function performs all updates within a transaction and rolls back in case of errors.
 *
 * @param id - The unique identifier of the project to complete.
 * @param projectData - An object containing project completion data, such as completionDate, expenses, and currency.
 * @param context - The context object containing repositories, models, and sequelize instance.
 * @returns An object indicating the success of the operation, including a message, projectId, and error flag.
 * @throws {CustomError} If the project is not found, already completed/deleted, has currency mismatches,
 *         or if there are pending hulls or unapproved drawings.
 */
export async function completeProject(
  id: number,
  projectData: AnyObject,
  { repositories, models, sequelize }: ContextType,
) {
  const t = await sequelize.transaction();

  const updatedObject: {
    status: ProjectStatus;
    completionDate: Date;
    expenses?: number | null;
    currency?: string;
  } = {
    status: ProjectStatus.CLOSED,
    completionDate: projectData.completionDate,
  };

  updatedObject.expenses = null;
  if (projectData.expenses) {
    updatedObject.expenses = projectData.expenses;
  }

  if (projectData.currency) {
    updatedObject.currency = projectData.currency;
  }

  const existingProject = await repositories.projectRepository.findById(id);

  if (!existingProject) {
    throw new CustomError(`Project with ID ${id} not found`, 404);
  }

  if (
    existingProject.dataValues.status === ProjectStatus.CLOSED ||
    existingProject.dataValues.deletedAt
  ) {
    throw new CustomError(
      `Project with ID ${id} is already completed/deleted`,
      409,
    );
  }

  if (
    projectData.currency &&
    existingProject.dataValues.currency &&
    projectData.currency !== existingProject.dataValues.currency
  ) {
    throw new CustomError(`Project currency is mismatched.`, 409);
  }

  if (
    projectData.expenses &&
    !existingProject.dataValues.currency &&
    !projectData.currency
  ) {
    throw new CustomError(
      `Project currency is not set. Set the currency before closing the project.`,
      409,
    );
  }

  try {
    const hullList = await repositories.projectHullRepository.findAll(
      [],
      false,
      {
        projectId: id,
      },
      [],
      t,
    );

    const hullIds = hullList
      ?.map(hull => hull.dataValues.id)
      .filter((id): id is number => id !== undefined);

    const hulls = await repositories.hullRepository.findAll(
      [],
      false,
      {
        id: hullIds,
        status: {
          [Op.in]: [HullStatus.ON_GOING, HullStatus.ON_HOLD],
        },
      },
      [],
      t,
    );

    const projectDrawingList = await repositories.drawingListRepository.findAll(
      [],
      false,
      { projectId: id },
      [],
      t,
    );

    const projectDrawingsListIds = projectDrawingList
      ?.map(drawingList => drawingList.dataValues.id)
      .filter((id): id is number => id !== undefined);

    const projectDrawings = await repositories.projectDrawingRepository.findAll(
      [],
      false,
      {
        drawingListId: { [Op.in]: projectDrawingsListIds },
        status: ProjectDrawingStatus.APPROVED,
      },
      [],
      t,
    );

    const validationRules = [
      {
        condition:
          hulls.length > 0 &&
          projectDrawingsListIds.length !== projectDrawings.length,
        message:
          'Project cannot be closed as there are pending hulls and approval drawings without an approved Original/Version.',
      },
      {
        condition: hulls.length > 0,
        message:
          'There are pending hulls that have not been marked as Completed. Please update their status before closing the project.',
      },
      {
        condition: projectDrawingsListIds.length !== projectDrawings.length,
        message:
          'Some approval drawings are missing an approved Original/Version. Please ensure all drawings have an approved document before proceeding.',
      },
    ];

    for (const rule of validationRules) {
      if (rule.condition) {
        throw new CustomError(rule.message, 409);
      }
    }

    await repositories.projectRepository.update(id, updatedObject, t);

    await models.Hull.update(
      {
        status: HullStatus.COMPLETED,
      },
      {
        where: { id: hullIds },
        transaction: t,
      },
    );

    await t.commit();

    return {
      message: 'Project completed successfully',
      projectId: id,
      error: false,
    };
  } catch (error) {
    await t.rollback();
    logger.error('[ProjectService] Error in completing project', error);
    throw error;
  }
}

/**
 * Soft deletes a project and its related entities (hulls, hull classes, and project hulls) by setting
 * the `deletedAt` and `deletedBy` fields. The operation is performed within a transaction to ensure atomicity.
 *
 * @param projectId - The unique identifier of the project to be deleted.
 * @param user - The user performing the deletion, used to set the `deletedBy` field.
 * @param context - The context object containing repositories, models, and sequelize instance.
 * @returns An object containing a success message upon successful deletion.
 * @throws {CustomError} If the project is not found or is already completed/deleted.
 * @throws {Error} If any other error occurs during the deletion process.
 */
export async function deleteProject(
  projectId: number,
  user: User,
  { repositories, models, sequelize }: ContextType,
) {
  const t = await sequelize.transaction();
  try {
    const project = await repositories.projectRepository.findById(projectId);
    if (!project) {
      throw new CustomError('Project not found', 404);
    }

    if (
      project.dataValues.status === ProjectStatus.CLOSED ||
      project.dataValues.deletedAt
    ) {
      throw new CustomError(
        `Project with ID ${projectId} is already completed/deleted`,
        409,
      );
    }

    const deleted = {
      deletedAt: new Date(),
      deletedBy: user.user_id,
    };

    const projectHulls = await models.ProjectHull.findAll({
      where: { projectId: projectId, deletedAt: null },
      transaction: t,
    });

    const hullIds = projectHulls.map(ph => ph.dataValues.hullId);

    await Promise.all([
      models.Project.update(deleted, {
        where: { id: projectId },
        transaction: t,
      }),
      models.Hull.update(deleted, {
        where: { id: hullIds },
        transaction: t,
      }),
      models.HullClass.update(deleted, {
        where: { hullId: hullIds },
        transaction: t,
      }),

      models.ProjectHull.update(deleted, {
        where: { projectId: projectId },
        transaction: t,
      }),
    ]);

    await t.commit();
    return { message: 'Project deleted successfully' };
  } catch (error) {
    await t.rollback();
    logger.error('[ProjectService] Error in deleting project', error);
    throw error;
  }
}

/**
 * The function counts the number of projects based on their status and returns the counts in an
 * object.
 * @returns An object is being returned, where the keys are the project statuses (as strings or
 * numbers) and the values are the counts of projects with that status.
 */
export async function countProjectsByStatus({ repositories, user }: ContextType) {
  return await repositories.projectRepository.getCountByStatus(user.tenantId);
}

export function checkForValidMasterData(
  masterData: MasterData,
  currency?: string | null,
  shipType?: string | null,
  engineType?: string | null,
  vesselClass?: string[] | null,
  flag?: string | null,
): void {
  if (currency) {
    const miscCurrencies = masterData.miscCurrencys;
    const values = miscCurrencies.map((item: any) => item.value);
    if (!values.includes(currency)) {
      throw new CustomError(`Invalid currency provided: ${currency}`, 400);
    }
  }

  if (shipType) {
    const shipTypes = masterData.vesselTypes;
    const values = shipTypes.map((item: any) => item.value);
    if (!values.includes(shipType)) {
      throw new CustomError(`Invalid shipType provided: ${shipType}`, 400);
    }
  }

  if (engineType) {
    const engineTypes = masterData.miscEngines;
    const values = engineTypes.map((item: any) => item.value);
    if (!values.includes(engineType)) {
      throw new CustomError(`Invalid engineType provided: ${engineType}`, 400);
    }
  }

  if (vesselClass) {
    const availableClasses = masterData.vesselClasss;
    const classValues = availableClasses.map(item => item.value);
    const allClassesPresent = vesselClass.every(cls =>
      classValues.includes(cls),
    );
    if (!allClassesPresent)
      throw new CustomError('Invalid vessel class provided', 400);
  }

  if (flag) {
    const availableFlags = masterData.flags;
    const flagValues = availableFlags.map(item => item.value);
    const isFlagPresent = flagValues.includes(flag);
    if (!isFlagPresent)
      throw new CustomError(`Invalid flag provided: ${flag}`, 400);
  }
}
