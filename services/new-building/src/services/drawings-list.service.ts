import { CustomError, isUserHasPermission, logger, ContextType } from '../utils';
import { Includeable, Op, Order } from 'sequelize';
import { IFindQuery, IFindAllQuery, User } from '../types';
import { excludeKeys } from '../utils/exclude-key.utils';
import { DrawingVersionCreationPermissions } from '../enums';
import {
  buildSearchBaseFilters,
  processExcelAndCSV,
} from '../utils/common.utils';
import { Models } from '../models';

type DrawingInput = {
  'Drawing Number*': string;
  'Drawing Name*': string;
  Discipline: string;
};

class DrawingsService {
  keyDrawingNo = 'Drawing Number*';
  keyDrawingName = 'Drawing Name*';

  /**
   * Processes an Excel or CSV file to create a list of drawings for a specific project.
   *
   * @param data - An object containing the file key and project ID.
   * @param data.fileKey - The key of the file stored in S3.
   * @param data.projectId - The ID of the project for which the drawings are being created.
   *
   * @throws {Error} If the file type is unsupported.
   * @throws {Error} If the file is empty or contains invalid data.
   * @throws {Error} If the file is missing required headers ("Drawing Number*" or "Drawing Name*").
   *
   * @returns A promise that resolves to the result of creating the drawing list.
   */
  async createDrawingFromExcel(
    data: { fileKey: string; projectId: number },
    user: User,
    context: ContextType,
  ) {
    const { repositories, models } = context;
    const existingProject = await repositories.projectRepository.findById(
      data.projectId,
    );
    if (!existingProject) {
      logger.error(
        `[DrawingListService] Project with ID ${data.projectId} not found`,
      );
      throw new CustomError(`Project with ID ${data.projectId} not found`, 404);
    }
    const fileData = await processExcelAndCSV(data.fileKey, 1);
    if (!fileData || fileData.length === 0) {
      throw new CustomError('The file is empty or contains invalid data.', 415);
    }

    const result = await models.DrawingSource.create({
      projectId: data.projectId,
      sourceFilePath: data.fileKey,
    });

    return await this.createDrawingList(
      fileData as DrawingInput[],
      data.projectId,
      user,
      result.dataValues.id,
      context,
    );
  }

  /**
   * Creates a list of drawings for a given project by processing the provided file data.
   * It checks for duplicates based on the combination of project ID, drawing name, and drawing number.
   * New drawings are added to the database, while duplicates are identified and excluded from creation.
   *
   * @param fileData - An array of drawing input objects containing details about the drawings to be created.
   *                   Each object should include the following properties:
   *                   - 'Drawing Number*': The unique identifier for the drawing.
   *                   - 'Drawing Name*': The name of the drawing.
   *                   - Discipline: The discipline associated with the drawing.
   * @param projectId - The ID of the project to which the drawings belong.
   * @returns An object containing:
   *          - `createdDrawings`: An array of successfully created drawing instances.
   *          - `duplicatedItems`: An array of drawing instances that were identified as duplicates.
   */
  async createDrawingList(
    fileData: DrawingInput[],
    projectId: number,
    user: User,
    sourceFileId: number | undefined,
    context: ContextType,
  ) {
    const { repositories, models } = context;
    const existingDrawings = await repositories.drawingListRepository.findAll(
      [],
      false,
      { projectId: projectId, tenantId: user?.tenantId },
      [],
    );

    const existingDrawingsMap = new Map();
    existingDrawings.forEach((drawing: any) => {
      const key = `${drawing.dataValues.projectId}-${drawing.dataValues.name}-${drawing.dataValues.drawingNo}`;
      existingDrawingsMap.set(key, drawing);
    });

    const newDrawings: any[] = [];
    const duplicateItems: any[] = [];

    fileData.forEach((drawing: DrawingInput) => {
      const mappedDrawing = {
        drawingNo: drawing['Drawing Number*'],
        name: drawing['Drawing Name*'],
        discipline:
          drawing.Discipline?.length > 4
            ? drawing.Discipline.slice(0, 4)
            : drawing.Discipline,
        projectId: projectId,
        projectDrawingListSourceFileId: sourceFileId,
        createdBy: user?.user_id,
        tenantId: user?.tenantId,
      };

      if (!mappedDrawing.drawingNo) {
        throw new CustomError(
          'Drawing number is missing in one or more entries of the uploaded drawing list.',
          422,
        );
      }

      if (!mappedDrawing.name) {
        throw new CustomError(
          'Drawing name is missing in one or more entries of the uploaded drawing list.',
          422,
        );
      }

      const key = `${projectId}-${mappedDrawing.name}-${mappedDrawing.drawingNo}`;
      if (mappedDrawing.drawingNo && mappedDrawing.name) {
        if (existingDrawingsMap.has(key)) {
          duplicateItems.push(mappedDrawing);
        } else {
          newDrawings.push(mappedDrawing);
        }
      }
    });
    if (duplicateItems.length > 0) {
      throw new CustomError(
        'The uploaded drawing details already exist. Please verify and upload the correct data',
        422,
      );
    }

    const result = await this.optimizedBulkCreate(newDrawings, models, user?.tenantId);
    logger.info(
      `Created ${result.length} new drawings. Found ${duplicateItems.length} duplicates.`,
    );

    return {
      createdDrawings: result,
      duplicatedItems: duplicateItems,
    };
  }

  /**
   * Retrieves a drawing by its ID, including related project drawings if the user has view permissions.
   *
   * @param id - The unique identifier of the drawing to retrieve.
   * @param user - The user requesting the drawing, used for permission checks and tenant scoping.
   * @param context - The context object containing repositories and models for database access.
   * @returns A promise that resolves to the drawing entity, potentially including associated project drawings.
   * @throws {CustomError} If no drawing is found with the given ID.
   */
  async findDrawingsById(id: number, user: User, context: ContextType) {
    const { repositories, models } = context;
    const isProjectDrawingViewPermission = isUserHasPermission(user, [
      DrawingVersionCreationPermissions.VIEW,
    ]);

    const result = await repositories.drawingListRepository.findOne(
      { id, tenantId: user?.tenantId },
      [],
      true,
      [
        ...(isProjectDrawingViewPermission
          ? [
            {
              model: models.ProjectDrawing,
              as: 'projectDrawings',
              attributes: {
                exclude: excludeKeys,
              },
              required: false,
              where: {
                deletedAt: null,
              },
            },
          ]
          : []),
      ],
    );
    if (!result) {
      throw new CustomError('Drawings not available with the given id', 404);
    }
    return result;
  }

  /**
   * Finds and retrieves a list of drawings associated with a specific project ID.
   *
   * @param id - The unique identifier of the project for which drawings are to be retrieved.
   * @returns A promise that resolves to the list of drawings, including associated project drawings and assets.
   * @throws An error if no drawings are found for the given project ID.
   */
  async getAllDrawings(query: IFindAllQuery, user: User, context: ContextType) {
    const { repositories, models } = context;
    const isProjectDrawingViewPermission = isUserHasPermission(user, [
      DrawingVersionCreationPermissions.VIEW,
    ]);

    const {
      page = 1,
      limit = 100,
      projectId,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      search,
    } = query;

    const offset = (page - 1) * limit;
    const where = buildSearchBaseFilters(query, [
      'name',
      'discipline',
      'drawingNo',
    ]);
    const include = this.buildIncludeSection(
      query,
      isProjectDrawingViewPermission,
      models,
    );

    if (typeof search === 'string' && search.trim()) {
      Object.assign(where, {
        [Op.or]: this.buildSearchFilters(search.trim()),
      });
    }

    if (projectId && Number(projectId) > 0) {
      where.projectId = projectId;
    }

    let order: Order = [];

    if (sortBy === 'name') {
      order = [['id', sortOrder]];
    } else {
      order = [[sortBy, sortOrder]];
    }
    where.tenantId = user?.tenantId;
    const findQuery: IFindQuery = {
      orderBy: order,
      limit,
      offset,
      page,
      attributes: [],
      where,
      include,
      distinct: true,
    };
    let result =
      await repositories.drawingListRepository.findAndCountAll(findQuery);
    const totalPages = Math.ceil(result.count / limit);
    result.rows = result.rows.map((drawingList: any) => {
      const plainData = drawingList.toJSON();
      if (plainData.projectDrawings && plainData.projectDrawings.length > 0) {
        plainData.projectDrawings.sort(
          (a: any, b: any) => (b.version ?? 0) - (a.version ?? 0),
        );
      }
      return plainData;
    });

    return {
      data: result.rows,
      pagination: {
        totalItems: result.count,
        totalPages,
        page: page,
        pageSize: limit,
      },
    };
  }

  /**
   * Optimized bulk create method that handles large datasets efficiently.
   * Automatically manages indexes and uses batch processing for optimal performance.
   *
   * @param data - Array of records to insert
   * @param models - Sequelize models
   * @param tenantId - Tenant ID for logging context
   * @returns Array of created records
   */
  async optimizedBulkCreate(data: any[], models: any, tenantId?: number | string): Promise<any[]> {
    if (data.length === 0) return [];

    const sequelize = models.DrawingsList.sequelize;
    const isLargeBulkInsert = data.length > 5000; // Threshold for optimization

    const startTime = Date.now();
    logger.info(`Starting bulk insert for ${data.length} drawings (tenant: ${tenantId})`);

    if (!isLargeBulkInsert) {
      // For smaller datasets, use standard bulk create
      const result = await models.DrawingsList.bulkCreate(data);
      const duration = Date.now() - startTime;
      logger.info(`Standard bulk insert completed in ${duration}ms`);
      return result;
    }

    // For large datasets, use optimized approach
    const transaction = await sequelize!.transaction();

    try {
      // Step 1: Temporarily drop the problematic index
      await sequelize!.query(
        'DROP INDEX IF EXISTS main.idx_project_drawing_list_active',
        { transaction }
      );

      // Step 2: Batch processing for optimal performance
      const batchSize = 1000;
      const results: any[] = [];

      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const batchResult = await models.DrawingsList.bulkCreate(batch, {
          transaction,
          validate: false, // Skip validation for performance
          ignoreDuplicates: false,
        });
        results.push(...batchResult);

        const batchNum = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(data.length / batchSize);
        logger.info(`Processed batch ${batchNum}/${totalBatches} (${batch.length} records)`);
      }

      // Step 3: Recreate the index
      await sequelize!.query(
        `CREATE INDEX idx_project_drawing_list_active
         ON main.project_drawing_list (project_id, tenant_id)
         WHERE deleted_at IS NULL`,
        { transaction }
      );

      // Commit transaction
      await transaction.commit();

      const duration = Date.now() - startTime;
      logger.info(`Optimized bulk insert completed in ${duration}ms. Created ${results.length} records.`);

      return results;

    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();

      // Ensure index is recreated even on error
      try {
        await sequelize!.query(
          `CREATE INDEX IF NOT EXISTS idx_project_drawing_list_active
           ON main.project_drawing_list (project_id, tenant_id)
           WHERE deleted_at IS NULL`
        );
        logger.info('Index recreated after error');
      } catch (indexError) {
        logger.error('Failed to recreate index after error:', indexError);
      }

      throw error;
    }
  }

  /**
   * Builds an array of Sequelize `Includeable` objects to be used in a query,
   * based on the provided filters and permissions.
   *
   * @param query - An object containing query filters such as `dueDate` and `status`.
   * @param hasPermission - A boolean indicating whether the user has permission
   *                        to include the `projectDrawings` model in the query.
   * @returns An array of `Includeable` objects to be used in Sequelize queries.
   *          Returns an empty array if `hasPermission` is `false`.
   *
   * The function applies the following filters:
   * - `dueDate`: Filters `projectDrawings` by a date range (`startDate` and/or `endDate`).
   * - `status`: Filters `projectDrawings` by a specific status.
   * - Ensures that only non-deleted records (`deletedAt = null`) are included.
   *
   * The `required` property of the `Includeable` object is set to `true` if
   * either `due_date` or `status` filters are applied.
   */
  buildIncludeSection(
    query: IFindAllQuery,
    hasPermission: boolean,
    models: Models,
  ): Includeable[] {
    if (!hasPermission) return [];

    const include: Includeable[] = [];
    const projectDrawingsWhere: Record<string, unknown> = {};

    if (query.dueDate?.startDate || query.dueDate?.endDate) {
      const dueDateFilter: Record<symbol, Date> = {};
      if (query.dueDate?.startDate) {
        dueDateFilter[Op.gte] = new Date(query.dueDate.startDate);
      }
      if (query.dueDate?.endDate) {
        dueDateFilter[Op.lte] = new Date(query.dueDate.endDate);
      }
      projectDrawingsWhere.due_date = dueDateFilter;
    }
    if (query.status) {
      projectDrawingsWhere.status = query.status;
    }
    projectDrawingsWhere.deletedAt = null;
    include.push({
      model: models.ProjectDrawing,
      as: 'projectDrawings',
      required:
        'due_date' in projectDrawingsWhere || 'status' in projectDrawingsWhere,
      where: projectDrawingsWhere,
    });

    return include;
  }

  /**
   * Constructs an array of search filter objects based on the provided search string.
   * Each filter object corresponds to a specific field and uses a case-insensitive
   * "like" operator to match the search string.
   *
   * @param search - The search string used to filter results.
   * @returns An array of filter objects, where each object represents a condition
   *          for filtering based on the fields `name`, `drawingNo`, and `discipline`.
   */
  buildSearchFilters(search: string): Record<string, object>[] {
    return [
      { name: { [Op.iLike]: `%${search}%` } },
      { drawingNo: { [Op.iLike]: `%${search}%` } },
      { discipline: { [Op.iLike]: `%${search}%` } },
    ];
  }

  /**
   * Deletes a drawing by its ID. This method performs several checks before deletion:
   * - Ensures the drawing exists in the repository.
   * - Verifies the drawing is not already marked as deleted.
   * - Ensures no files are linked to the drawing number.
   *
   * If any of these conditions are not met, an appropriate error is thrown.
   *
   * @param id - The unique identifier of the drawing to be deleted.
   * @param user - The user performing the deletion operation.
   * @throws {CustomError} If the drawing does not exist (404).
   * @throws {CustomError} If the drawing is already deleted (409).
   * @throws {CustomError} If files are linked to the drawing number (409).
   * @returns An object containing a success message and the ID of the deleted drawing.
   */
  async deleteDrawingsById(id: number, user: User, context: ContextType) {
    const { repositories, models } = context;
    const result = await repositories.drawingListRepository.findById(id, true, {
      include: [
        {
          model: models.ProjectDrawing,
          as: 'projectDrawings',
          attributes: {
            exclude: excludeKeys,
          },
          required: false,
          where: {
            deletedAt: null,
          },
        },
      ],
    });

    if (!result) {
      throw new CustomError('Drawings not available with the given id', 404);
    }

    if (result.dataValues.deletedAt) {
      throw new CustomError(`Drawing with ID ${id} is already deleted`, 409);
    }

    if ((result as any).projectDrawings.length > 0) {
      throw new CustomError(
        'Cannot delete drawing: one or more files are linked to this drawing number.',
        409,
      );
    }
    await repositories.drawingListRepository.update(id, {
      deletedAt: new Date(),
      deletedBy: user.user_id,
    });
    return {
      error: false,
      message: 'Drawing has been deleted successfully',
      data: { id },
    };
  }

  /**
   * Fetches unique values for a specified key from the drawing list repository.
   *
   * @param key - The key for which unique values need to be fetched.
   *              Valid keys include 'type', 'time', 'description', 'discipline', 'remark', and 'status'.
   * @returns An object containing:
   *          - `error`: A boolean indicating whether an error occurred (always `false` in this case).
   *          - `message`: A success message indicating the key for which values were fetched.
   *          - `data`: The unique values corresponding to the specified key, filtered by the condition `deleted: false`.
   */
  async getOptionsByKey(key: string, projectId: number, context: ContextType) {
    const { repositories } = context;
    const validKeys = ['name', 'discipline', 'drawing_no', 'status'];

    return {
      error: false,
      message: `Unique values for key '${key}' fetched successfully`,
      data: await repositories.drawingListRepository.getOptionsByKey(
        key,
        validKeys,
        {
          deletedAt: null,
          projectId: projectId,
          tenantId: context.user?.tenantId,
        },
      ),
    };
  }
}

export default new DrawingsService();
