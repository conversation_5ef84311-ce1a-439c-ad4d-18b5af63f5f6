import {CustomError, logger, ContextType} from '../utils';
import {AnyObject} from '../types';

/**
 * Modifies an existing asset based on the provided type, ID, and changed fields.
 *
 * @param type - The type of the asset to be modified. Possible values are 'project' or 'hull'.
 * @param id - The unique identifier of the asset to be modified.
 * @param changedFields - An object containing the fields to be updated and their new values.
 * @param context - The request context containing repositories, models, etc.
 * @returns An object indicating the success or failure of the operation, with an error flag and a message.
 * @throws CustomError - Throws an error if the update operation fails.
 */
export async function modifyExistingAsset(
  type: string,
  id: number,
  changedFields: AnyObject,
  context: ContextType,
) {
  const {repositories} = context;
  try {
    switch (type) {
      case 'project':
        await repositories.projectRepository.update(id, changedFields);
        break;
      case 'hull':
        await repositories.hullRepository.update(id, changedFields);
        break;
      default:
        logger.error(`[AssetService] Asset with ID ${id} not found`);
        break;
    }
    return {error: false, message: 'Asset updated successfully'};
  } catch (error) {
    logger.error('Error updating asset:', JSON.stringify(error));
    throw new CustomError('Internal Server Error', 500);
  }
}
