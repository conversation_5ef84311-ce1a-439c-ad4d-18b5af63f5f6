import axios from 'axios';

import qs from 'qs';
import {logger} from '../utils';

class VesselMasterDataService {
  /**
   * Retrieves an access token from the authentication server using OpenID Connect.
   *
   * This method sends a POST request to the OpenID Connect token endpoint with the required
   * credentials and client information. The credentials and client details are sourced from
   * environment variables. The response contains an access token, which is returned upon
   * successful authentication.
   *
   * @returns {Promise<string | undefined>} A promise that resolves to the access token as a string
   * if the request is successful, or `undefined` if an error occurs.
   *
   * @throws {Error} Logs an error message to the console if the request fails.
   *
   * @example
   * ```typescript
   * const token = await getAccessToken();
   * ```
   */
  getAccessToken = async () => {
    const data = qs.stringify({
      username: process.env.OPEN_ID_USERNAME,
      password: process.env.OPEN_ID_PASSWORD,
      client_secret: process.env.OPEN_ID_CLIENT_SECRET,
      grant_type: process.env.OPEN_ID_GRANT_TYPE,
      client_id: process.env.OPEN_ID_CLIENT_ID,
    });
    const config = {
      method: 'post',
      url: `${process.env.PARIS_AUTH_BASE_URL}/auth/realms/paris2/protocol/openid-connect/token`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: data,
    };
    try {
      const response = await axios.request(config);
      const token = response.data.access_token;

      return token;
    } catch (error) {
      logger.error('Error fetching access token:', error);
      return;
    }
  };

  /**
   * Fetches vessel master data from the Paris API using a GraphQL query.
   *
   * @param token - The authorization token to access the Paris API.
   * @returns A promise that resolves to the response of the API request.
   *
   * The GraphQL query retrieves the following data:
   * - `miscCurrencys`: A list of miscellaneous currencies with their IDs and values.
   * - `owners`: A list of vessel owners with their IDs and values.
   * - `vesselTypes`: A list of vessel types with their IDs and values.
   * - `miscEngines`: A list of miscellaneous engines with their IDs and values.
   *
   * @throws An error if the API request fails.
   */
  getVesselMasterData = async () => {
    const token = await this.getAccessToken();
    try {
      const config = {
        method: 'post',
        url: `${process.env.PARIS_BASE_URL}/vessel/query-graphql`,
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        data: {
          query: `{
                      miscCurrencys { id, value, updated_at },
                      owners { id, value, updated_at },
                      vesselTypes { id, value, updated_at },
                      miscEngines { id, value, updated_at },
                      flags { id, value, updated_at },
                      vesselClasss { id, value, updated_at }
                  }`,
        },
      };
      const resposne = await axios.request(config);
      return resposne.data;
    } catch (error) {
      logger.error('Error fetching vessel master data:', error);
      throw new Error('Failed to fetch vessel master data');
    }
  };

  /**
   * Fetches user details for the given user IDs.
   *
   * @param userIds - A string containing the user IDs to fetch details for.
   * @returns A promise that resolves to the user details.
   * @throws Will throw an error if unable to fetch the access token or if the request fails.
   */
  getUserDetails = async (userIds: string[]): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    const queryString = userIds.map(id => `userId=${id}`).join('&');

    const config = {
      method: 'get',
      url: `${process.env.PARIS_BASE_URL}/keycloak-admin/users?${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      logger.error('Error fetching user details:', error);
      throw new Error('Failed to fetch user details');
    }
  };
}

export default new VesselMasterDataService();
