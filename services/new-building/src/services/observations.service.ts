import {
  ContextType,
  CustomError,
  logger,
  convertHeicToJpegAndReplace,
} from '../utils';
import {User} from '../types';

class ObservationService {
  /**
   * Creates observations for a given inspection by associating assets with it.
   *
   * @param inspectionId - The ID of the inspection to associate the observations with.
   * @param assets - An array of asset objects containing the file key, asset name, and asset type.
   * @param user - The user performing the operation, containing user details such as `user_id`.
   * @param context - The request context containing repositories, models, etc.
   * @returns A promise that resolves to the created assets.
   * @throws {CustomError} If the request payload is invalid or the inspection is not found.
   */
  async createObservation(
    inspectionId: number,
    assets: {fileKey: string; assetName: string; assetType: string}[],
    user: User,
    context: ContextType,
  ) {
    const {repositories, models} = context;

    if (!inspectionId || !Array.isArray(assets) || assets.length === 0) {
      throw new CustomError('Invalid request payload', 422);
    }
    const isValidInspection = await repositories.inspectionRepository.findOne({
      id: inspectionId,
    });
    if (!isValidInspection) {
      throw new CustomError('Inspection not found', 404);
    }
    const highestVersionAsset = await models.InspectionObservation.findOne({
      where: {
        inspectionId: inspectionId,
      },
      attributes: ['version'],
      order: [['version', 'DESC']],
    });
    let currentVersion = highestVersionAsset?.dataValues?.version ?? 0;

    const assetsToCreate = await Promise.all(
      assets.map(async asset => {
        currentVersion++;
        let assetType = asset.assetType;
        let assetPath = asset.fileKey;
        let assetName = asset.assetName;
        if (asset.assetType === 'image/heic') {
          assetPath = await convertHeicToJpegAndReplace(
            process.env.AWS_REGION!,
            process.env.AWS_S3_BUCKET_NAME!,
            asset.fileKey,
          );
          assetType = 'image/jpeg';
          assetName = assetName.replace(/\.heic$/i, '.jpeg');
        }

        return {
          assetName: assetName,
          inspectionId: inspectionId,
          assetPath: assetPath,
          assetType: assetType,
          version: currentVersion,
          createdBy: user.user_id,
        };
      }),
    );

    const createdAssets =
      await repositories.inspectionObservationRepository.createAll(
        assetsToCreate,
      );
    logger.info('[ObservationService] Assets created successfully', {
      createdAssets,
    });
    return createdAssets;
  }
}

export default new ObservationService();
