import {AnyObject, IFindQ<PERSON>y, IFindAll<PERSON><PERSON>y, User} from '../types';
import {CustomError, isUserHasPermission, logger, ContextType} from '../utils';
import {
  CreationAttributes,
  Includeable,
  literal,
  Op,
  Order,
  WhereOptions,
} from 'sequelize';
import {InspectionStatus, InspectionType} from '../enums/inspection.enum';
import {
  buildSearchBaseFilters,
  processExcelAndCSV,
} from '../utils/common.utils';
import {S3HelperService} from '.';
import {InspectionObservationCreationAttributes} from '../models/inspection-observation.model';
import {
  CommentStatus,
  HullPermissions,
  ObservationPermissions,
  ProjectPermissions,
} from '../enums';
import {
  InspectionInput,
  ProjectHullAttributes,
} from '../types/inspection.types';

type InspectionInstance = InstanceType<ContextType['models']['Inspection']>;

class InspectionService {
  /**
   * Creates inspections from an Excel or CSV file.
   *
   * @param data - An object containing the following properties:
   *   - `fileKey`: The key or path to the uploaded file.
   *   - `projectId`: The ID of the project to associate the inspections with.
   *   - `hullId`: The ID of the hull to associate the inspections with.
   * @param user - The user object containing user details, such as `user_id`.
   * @returns A promise that resolves to the created inspection list.
   * @throws {CustomError} If the project with the given `projectId` is not found.
   * @throws {CustomError} If the hull with the given `hullId` is not found.
   * @throws {CustomError} If the file is empty or contains invalid data.
   * @throws {CustomError} If the file is missing required headers.
   */
  async createInspectionFromExcel(
    data: {fileKey: string; projectId: number; hullId: number},
    user: AnyObject,
    context: ContextType,
  ) {
    const {repositories, models} = context;

    const isProjectExistOnTenant = await repositories.projectRepository.findOne(
      {
        id: data.projectId,
        tenantId: user.tenantId,
      },
    );
    if (!isProjectExistOnTenant) {
      throw new CustomError('Project not found', 404);
    }
    const hullData = await repositories.projectHullRepository.findOne(
      {
        hullId: data.hullId,
        projectId: data.projectId,
      },
      [],
      false,
      [
        {
          model: models.Hull,
          as: 'hull',
          attributes: ['id', 'hullNo'],
          required: true,
          where: {deletedAt: null},
        },
      ],
    );
    if (!hullData) {
      logger.error(
        `[InspectionService] Hull not matched ${data.projectId} not found tenantId: ${user?.['tenantId']}`,
      );
      throw new CustomError(`Hull not found ${data.projectId} not found`, 404);
    }
    const fileData = await processExcelAndCSV(data.fileKey, 2);
    if (!fileData || fileData.length === 0) {
      throw new CustomError('The file is empty or contains invalid data.', 415);
    }

    const result = await repositories.inspectionSourceRepository.create({
      hullId: data.hullId,
      projectId: data.projectId,
      sourceFilePath: data.fileKey,
    });
    return await this.createInspectionList(
      fileData as InspectionInput[],
      user,
      hullData as unknown as ProjectHullAttributes,
      result.dataValues.id,
      context,
    );
  }

  /**
   * Creates a list of inspections based on the provided input data.
   *
   * This method processes the input file data to create new inspections for a specific project and hull.
   * It validates the input data, checks for duplicates, and ensures required fields are present.
   * If all validations pass, it bulk creates the new inspections in the database.
   *
   * @param fileData - An array of inspection input data containing details such as submission date, due date, time, description, discipline, and more.
   * @param projectId - The ID of the project to which the inspections belong.
   * @param hullId - The ID of the hull to which the inspections belong.
   * @param userId - The ID of the user creating the inspections.
   *
   * @returns A promise that resolves to an object containing the created inspections.
   *
   * @throws {CustomError} If one or more required fields are missing in the input data.
   * @throws {CustomError} If the submission date is not within the last 10 days.
   * @throws {CustomError} If the due date is not greater than or equal to the submission date.
   * @throws {CustomError} If an inspection with the same description already exists.
   * @throws {Error} If any other error occurs during the process.
   */
  async createInspectionList(
    fileData: InspectionInput[],
    user: AnyObject,
    hullData: ProjectHullAttributes,
    sourceFileId: number | undefined,
    context: ContextType,
  ) {
    const {repositories, sequelize} = context;
    const transaction = await sequelize.transaction();

    try {
      const newInspections: CreationAttributes<InspectionInstance>[] = [];
      fileData.forEach((inspection: InspectionInput) => {
        const submissionDate = new Date(inspection['Submission Date']);
        const dueDate = inspection['Due Date']
          ? new Date(inspection['Due Date'])
          : null;
        const time = inspection['Time'] ? new Date(inspection['Time']) : null;
        const forOwner = inspection?.Owner?.toString().toLowerCase() === 'yes';
        const forClass = inspection?.Class?.toString().toLowerCase() === 'yes';
        const mappedInspection = {
          type: InspectionType.HULL,
          hullId: hullData.hullId,
          projectId: hullData.projectId,
          submissionDate: submissionDate,
          dueDate: dueDate,
          time: time,
          description: inspection['Inspection Description'],
          discipline: inspection.Discipline,
          inspectionSourceFileId: sourceFileId,
          forOwner: forOwner,
          forClass: forClass,
          remark: inspection.Remark,
          status: InspectionStatus.ONGOING,
          createdBy: user?.userId,
          tenantId: user?.tenantId,
        } as unknown as CreationAttributes<InspectionInstance>;
        this.validateInspectionInput(inspection, mappedInspection, hullData);
        newInspections.push(mappedInspection);
      });
      const result = await repositories.inspectionRepository.createAll(
        newInspections,
        {},
        transaction,
      );
      await transaction.commit();
      return {
        createdInspections: result,
      };
    } catch (error) {
      await transaction.rollback();
      logger.error(
        `[Inspection Service] Error in creating inspection for tenant: ${user?.['tenantId']}`,
        JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Validates the input for an inspection and ensures all required fields and rules are met.
   * Throws a `CustomError` if any validation fails.
   *
   * @param inspection - The inspection input object containing details about the inspection.
   * @param mappedInspection - The mapped inspection object containing attributes for creation.
   * @param hullData - The hull data object containing attributes of the project hull.
   *
   * @throws {CustomError} If the hull number does not match the given ID.
   * @throws {CustomError} If the discipline exceeds 4 characters.
   * @throws {CustomError} If both "For Owner" and "For Class" are set to "Yes".
   * @throws {CustomError} If neither "For Owner" nor "For Class" is set to "Yes".
   * @throws {CustomError} If one or more required fields are missing.
   * @throws {CustomError} If the submission date is not within the last 10 days.
   * @throws {CustomError} If the due date is not greater than or equal to the submission date.
   */
  validateInspectionInput(
    inspection: InspectionInput,
    mappedInspection: CreationAttributes<InspectionInstance>,
    hullData: ProjectHullAttributes,
  ) {
    this.validateEmptyData(mappedInspection, inspection);

    const forOwner = inspection?.Owner?.toString().toLowerCase() === 'yes';
    const forClass = inspection?.Class?.toString().toLowerCase() === 'yes';
    if (
      inspection['Hull No'] === undefined ||
      inspection['Hull No'] === null ||
      inspection['Hull No']?.trim()?.toLowerCase() === ''
    ) {
      throw new CustomError(
        'Hull number missing for one or more uploaded inspection logs.',
        422,
      );
    }

    if (
      hullData?.hull?.hullNo.toLowerCase() !==
      inspection['Hull No']?.trim()?.toLowerCase()
    ) {
      throw new CustomError(
        'Hull number is not matching with the given id',
        422,
      );
    }

    if (inspection?.Discipline.length > 4) {
      throw new CustomError(
        'Invalid value in the Discipline column of the uploaded inspection log. Please review and upload again.',
        422,
      );
    }

    if (!forOwner && !forClass) {
      throw new CustomError(
        'At least one of For Owner or For Class must be Yes',
        422,
      );
    }

    const requiredFieldsPresent = [
      mappedInspection.hullId,
      mappedInspection.submissionDate,
      mappedInspection.description,
      mappedInspection.discipline,
    ].every(Boolean);

    if (!requiredFieldsPresent) {
      throw new CustomError('One or more required fields are missing', 422);
    }

    const tenDaysAgo = new Date();
    tenDaysAgo.setHours(0, 0, 0, 0);
    tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);

    const isSubmissionDateValid =
      mappedInspection.submissionDate instanceof Date &&
      !isNaN(mappedInspection.submissionDate.getTime()) &&
      mappedInspection.submissionDate >= tenDaysAgo;

    if (!isSubmissionDateValid) {
      throw new CustomError(
        'Please verify the submission date and re-upload the inspection logs.',
        422,
      );
    }

    if (inspection['Due Date']) {
      const isDueDateValid =
        mappedInspection.dueDate instanceof Date &&
        !isNaN(mappedInspection.dueDate.getTime()) &&
        mappedInspection.dueDate >= mappedInspection.submissionDate;

      if (!isDueDateValid) {
        throw new CustomError(
          'Due date must be greater than or equal to submission date',
          422,
        );
      }
    }
  }

  /**
   * Validates the provided inspection data to ensure that all required fields are present.
   * Throws a `CustomError` with a specific message and status code (422) if any required field is missing.
   *
   * @param mappedInspection - The inspection data to validate, represented as a `CreationAttributes<InspectionInstance>`.
   *
   * @throws CustomError - If any of the following fields are missing:
   * - `hullId`: Hull number is required.
   * - `submissionDate`: Submission date is required.
   * - `dueDate`: Due date is required.
   * - `time`: Time is required.
   * - `description`: Inspection description is required.
   * - `discipline`: Discipline is required.
   * - `forOwner` or `forClass`: Either the owner or class information must be provided.
   */
  validateEmptyData(
    mappedInspection: CreationAttributes<InspectionInstance>,
    inspection: InspectionInput,
  ) {
    if (!mappedInspection.hullId) {
      throw new CustomError(
        'Hull number missing for one or more uploaded inspection logs.',
        422,
      );
    }

    if (!inspection['Submission Date']) {
      throw new CustomError(
        'Submission date missing for one or more uploaded inspection logs.',
        422,
      );
    }

    if (!mappedInspection.description) {
      throw new CustomError(
        'Inspection description is missing in the uploaded log file.',
        422,
      );
    }

    if (!mappedInspection.discipline) {
      throw new CustomError(
        'Discipline is missing for one or more uploaded inspection logs',
        422,
      );
    }

    if (!inspection?.Owner && !inspection.Class) {
      throw new CustomError(
        'Class/Owner is missing for one or more uploaded inspection logs',
        422,
      );
    }

    if (
      inspection?.Owner?.toString().toLowerCase() !== 'yes' &&
      inspection.Owner?.toString().toLowerCase() !== 'no' &&
      inspection.Class?.toString().toLowerCase() !== 'yes' &&
      inspection?.Class?.toString().toLowerCase() !== 'no'
    ) {
      throw new CustomError(
        'Class/Owner is invalid for one or more uploaded inspection logs',
        422,
      );
    }
  }

  /**
   * Retrieves a paginated list of inspections based on the provided query parameters.
   *
   * @param query - An object containing query parameters for fetching inspections.
   *   - `page` (optional): The page number to retrieve (default is 1).
   *   - `limit` (optional): The number of items per page (default is 100).
   *
   * @returns An object containing the following:
   *   - `data`: An array of inspection records.
   *   - `pagination`: An object with pagination details:
   *       - `totalItems`: The total number of inspection records.
   *       - `totalPages`: The total number of pages.
   *       - `page`: The current page number.
   *       - `pageSize`: The number of items per page.
   *
   * @throws Will throw an error if the query execution fails.
   */
  async getAllInspections(
    query: IFindAllQuery,
    user: User,
    context: ContextType,
  ) {
    const {repositories, models} = context;
    const {
      page = 1,
      limit = 100,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      search,
    } = query;
    const offset = (page - 1) * limit;

    const include = this.buildIncludeSection(query, user, models);
    const where = buildSearchBaseFilters(
      query,
      ['discipline', `description`],
      true,
    );

    if (typeof search === 'string' && search.trim()) {
      Object.assign(where, {
        [Op.or]: this.buildSearchFilters(search.trim()),
      });
    }
    where.tenantId = user?.tenantId;

    if (
      'commentStatus' in query &&
      Number(query['commentStatus']) === CommentStatus.CLOSED
    ) {
      const status = Number(query['commentStatus']);
      const whereCommentAllMatch = literal(`
        EXISTS (
          SELECT 1 FROM "main"."inspection_comment" AS comment
          WHERE comment."inspection_id" = "Inspection"."id"
          GROUP BY comment."inspection_id"
          HAVING BOOL_AND(comment."status" = ${status})
        )
      `);

      const opAnd = Op.and as unknown as keyof WhereOptions;

      where[opAnd] ??= [];

      (where[opAnd] as (WhereOptions | ReturnType<typeof literal>)[]).push(
        whereCommentAllMatch,
      );
    }

    const findQuery: IFindQuery = {
      orderBy: this.buildSortOrder(sortBy, sortOrder, models),
      limit,
      offset,
      page,
      where,
      attributes: [],
      include,
    };
    const result =
      await repositories.inspectionRepository.findAndCountAll(findQuery);
    const totalPages = Math.ceil(result.count / limit);
    return {
      data: result.rows,
      pagination: {
        totalItems: result.count,
        totalPages,
        page: page,
        pageSize: limit,
      },
    };
  }

  /**
   * Builds a sort order configuration for querying data.
   *
   * @param sortBy - The field to sort by. Defaults to 'createdAt'.
   *                 Can be a top-level field or a relation field in the format 'relation.field'.
   * @param sortOrder - The order of sorting, either 'ASC' for ascending or 'DESC' for descending. Defaults to 'DESC'.
   * @returns An `Order` configuration for sorting, which can include top-level fields or related model fields.
   *          If the `sortBy` field is invalid, defaults to sorting by 'createdAt' in descending order.
   *
   * The method supports sorting by top-level fields such as:
   * - 'createdAt'
   * - 'submissionDate'
   * - 'dueDate'
   * - 'time'
   * - 'discipline'
   * - 'description'
   * - 'forOwner'
   * - 'forClass'
   * - 'id'
   *
   * Additionally, it supports sorting by related fields through a predefined mapping:
   * - 'project.name' (mapped to the `Project` model)
   * - 'hull.hullNo' (mapped to the `Hull` model)
   *
   * Example usage:
   * - `buildSortOrder('createdAt', 'ASC')` returns `[['createdAt', 'ASC']]`.
   * - `buildSortOrder('project.name', 'DESC')` returns `[[{model: Project, as: 'project'}, 'name', 'DESC']]`.
   */
  buildSortOrder(
    sortBy: string = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    models?: ContextType['models'],
  ): Order {
    const joinSortMap: Record<
      string,
      {model: any; as: string; sortByField: string}
    > = models
      ? {
          project: {model: models.Project, as: 'project', sortByField: 'name'},
          hull: {model: models.Hull, as: 'hull', sortByField: 'hullNo'},
        }
      : {};

    const topLevelFields = new Set([
      'createdAt',
      'submissionDate',
      'dueDate',
      'time',
      'discipline',
      'description',
      'forOwner',
      'forClass',
      'id',
    ]);
    if (sortBy.includes('.')) {
      const [relation, field] = sortBy.split('.');
      const join = joinSortMap[relation];
      if (join && field === join.sortByField) {
        return [[{model: join.model, as: join.as}, field, sortOrder]];
      }
    }
    if (topLevelFields.has(sortBy)) {
      return [[sortBy, sortOrder]];
    }
    return [['createdAt', 'DESC']];
  }

  /**
   * Builds an array of Sequelize `Includeable` objects to specify related models
   * to include in a query based on user permissions and query parameters.
   *
   * @param query - The query object containing filtering parameters such as `projectId` and `hullId`.
   * @param user - The user object used to check permissions for accessing related models.
   * @param includeAssets - A boolean flag indicating whether to include assets in the query. Defaults to `false`.
   * @returns An array of `Includeable` objects specifying the models to include in the query.
   *
   * The function dynamically constructs the `include` array based on:
   * - User permissions for projects, hulls, and observations.
   * - Query parameters such as `projectId` and `hullId`.
   * - Whether assets should be included in the query.
   *
   * Each included model is filtered by its `deletedAt` property to exclude soft-deleted records.
   * If the user has the required permissions, the function adds the corresponding model to the `include` array
   * with appropriate attributes, filtering conditions, and ordering.
   */
  buildIncludeSection(
    query: IFindAllQuery,
    user: User,
    models?: ContextType['models'],
    includeAssets: boolean = false,
  ): Includeable[] {
    const include: Includeable[] = [];
    const projectWhere: Record<string, unknown> = {};
    const hasProjectPermission = isUserHasPermission(user, [
      ProjectPermissions.VIEW,
    ]);
    const hasHullPermission = isUserHasPermission(user, [HullPermissions.VIEW]);
    const hasObservationPermission = isUserHasPermission(user, [
      ObservationPermissions.VIEW,
    ]);

    if (query['projectId']) {
      projectWhere.id = query['projectId'];
    }
    projectWhere.deletedAt = null;
    if (hasProjectPermission && models) {
      include.push({
        model: models.Project,
        as: 'project',
        attributes: [
          'id',
          'name',
          'assetName',
          'assetPath',
          'assetType',
          'projectTypeId',
        ],
        required: 'id' in projectWhere,
        where: projectWhere,
        include: [
          {
            model: models.ProjectType,
            as: 'projectType',
            attributes: ['id', 'name'],
          },
        ],
      });
    }
    if (includeAssets && hasObservationPermission && models) {
      include.push({
        model: models.InspectionObservation,
        as: 'assets',
        attributes: ['id', 'assetName', 'assetPath', 'version'],
        where: {
          deletedAt: null,
        },
        separate: true,
        order: [['version', 'ASC']],
      });
    }
    const hullWhere: Record<string, unknown> = {};
    if (query['hullId']) {
      hullWhere.id = query['hullId'];
    }
    hullWhere.deletedAt = null;
    if (hasHullPermission && models) {
      include.push({
        model: models.Hull,
        as: 'hull',
        attributes: ['id', 'hullNo'],
        required: true,
        where: hullWhere,
      });
    }

    if (
      'commentStatus' in query &&
      Number(query['commentStatus']) === CommentStatus.OPEN
    ) {
      const commentWhere: Record<string, unknown> = {};
      commentWhere.status = query['commentStatus'];

      include.push({
        model: models?.InspectionComment,
        as: 'comment',
        attributes: [],
        required: true,
        where: commentWhere,
      });
    }
    return include;
  }

  /**
   * Constructs an array of search filter objects to be used in querying inspections.
   * Each filter object corresponds to a specific field and applies a case-insensitive
   * "like" operation to match the provided search term.
   *
   * @param search - The search term to filter inspections by. This term is applied
   *                  to multiple fields such as discipline, description, project name,
   *                  and hull number.
   * @returns An array of filter objects, where each object specifies a field and
   *          a case-insensitive "like" condition for matching the search term.
   */
  buildSearchFilters(search: string): Record<string, object>[] {
    return [
      {discipline: {[Op.iLike]: `%${search}%`}},
      {description: {[Op.iLike]: `%${search}%`}},
      {'$project.name$': {[Op.iLike]: `%${search}%`}},
      {'$hull.hull_no$': {[Op.iLike]: `%${search}%`}},
    ];
  }

  /**
   * Deletes an inspection by marking it as deleted in the database.
   *
   * @param inspectionId - The unique identifier of the inspection to be deleted.
   * @param user - The user performing the deletion, used to track who deleted the inspection.
   * @throws {CustomError} If the inspection with the given ID is not found.
   * @returns An object containing a success message and an error flag.
   */
  async deleteInspection(inspectionId: number, context: ContextType) {
    const {repositories, user} = context;

    const existingInspection = await repositories.inspectionRepository.findOne({
      id: inspectionId,
      tenantId: user.tenantId,
    });
    if (!existingInspection) {
      throw new CustomError('Inspection not found', 404);
    }

    if (existingInspection.dataValues.deletedAt) {
      throw new CustomError(
        `Inspection with ID ${inspectionId} is already deleted`,
        409,
      );
    }

    const existingInspectionObservation =
      await repositories.inspectionObservationRepository.findOne({
        inspectionId,
      });

    if (existingInspectionObservation) {
      throw new CustomError(
        `Inspection cannot be deleted because it has associated observations.`,
        409,
      );
    }

    const deleted = {
      deletedAt: new Date(),
      deletedBy: user.user_id,
    };
    await repositories.inspectionRepository.update(inspectionId, deleted);
    return {
      error: false,
      message: 'Inspection deleted successfully',
    };
  }

  /**
   * Counts the number of inspections grouped by their status.
   *
   * This method queries the `Inspection` model to retrieve the count of inspections
   * for each status where the `deleted` field is not true. The results are grouped
   * by the `status` field and returned as a record where the keys are the statuses
   * and the values are the counts.
   *
   * @returns {Promise<Record<string, number>>} A promise that resolves to an object
   * where the keys are inspection statuses (as strings) and the values are the counts
   * of inspections for each status.
   *
   * @throws {Error} If the database query fails or the data cannot be processed.
   */
  async countInspectionByStatus(context: ContextType) {
    const {repositories} = context;
    return {
      error: false,
      message: 'Get count successfully',
      data: await repositories.inspectionRepository.getCountByStatus(),
    };
  }

  /**
   * Retrieves an inspection by its ID, including related data based on the user's permissions.
   *
   * @param id - The unique identifier of the inspection to retrieve.
   * @param user - The user object containing information about the current user and their permissions.
   * @returns An object containing the inspection data if found, or throws an error if not found.
   * @throws {CustomError} If the inspection with the given ID is not found.
   */
  async findInspectionById(id: number, user: User, context: ContextType) {
    const {repositories, models} = context;
    const includes = this.buildIncludeSection({}, user, models, true);

    const inspection = await repositories.inspectionRepository.findOne(
      {
        id: id,
        tenantId: user?.tenantId,
      },
      [],
      false,
      includes,
    );

    if (!inspection)
      throw new CustomError(`Inspection not found id ${id}`, 404);
    const resultWithAsset =
      inspection as unknown as InspectionObservationCreationAttributes & {
        assets: InspectionObservationCreationAttributes[];
      };

    if (resultWithAsset.assets && Array.isArray(resultWithAsset.assets)) {
      for (const asset of resultWithAsset.assets) {
        if (asset.assetPath) {
          const url = await S3HelperService.generatePresignedDownloadUrl(
            asset.assetPath,
            asset.assetName,
          );
          asset.assetPath = url;
        }
      }
    }

    const inspectionWithProject = inspection as InspectionInstance & {
      project?: {assetPath?: string; assetName?: string};
    };
    if (
      inspectionWithProject.project?.assetPath &&
      inspectionWithProject.project?.assetName
    ) {
      const url = await S3HelperService.generatePresignedDownloadUrl(
        inspectionWithProject.project.assetPath,
        inspectionWithProject.project.assetName,
      );
      inspectionWithProject.project.assetPath = url;
    }

    return {
      error: false,
      message: 'Inspection found successfully',
      data: resultWithAsset,
    };
  }

  /**
   * Fetches unique values for a specified key from the inspections table.
   *
   * @param key - The key for which unique values need to be fetched.
   *              Must be one of the following valid keys:
   *              'type', 'hullId', 'projectId', 'time', 'description',
   *              'discipline', 'remark', 'status'.
   * @returns A promise that resolves to an object containing:
   *          - `error`: A boolean indicating whether an error occurred.
   *          - `message`: A string message describing the result.
   *          - `data`: An array of unique values for the specified key.
   * @throws {CustomError} If the provided key is invalid or if fetching unique values fails.
   */
  async getOptionsByKey(key: string, context: ContextType) {
    const {repositories} = context;
    const validKeys = [
      'type',
      'time',
      'description',
      'discipline',
      'remark',
      'status',
    ];

    return {
      error: false,
      message: `Unique values for key '${key}' fetched successfully`,
      data: await repositories.inspectionRepository.getOptionsByKey(
        key,
        validKeys,
        {
          deletedAt: null,
        },
      ),
    };
  }
}

export default new InspectionService();
