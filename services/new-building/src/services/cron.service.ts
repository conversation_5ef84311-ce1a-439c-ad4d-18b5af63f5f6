import {Paris2MasterItemOptions, Paris2MasterItems} from '../types';
import {logger, ContextType} from '../utils';
import {BasicStatus} from '../enums';
import {getPendingImageThumbnailTransactions} from './transaction.service';
import {generateThumbnails} from '../utils/sharp.utils';
import s3HelperService from './s3-helper.service';
import {ITransactionAttributes} from '../types/transaction.types';
import {TransactionStatus} from '../enums/transaction-status.enum';
import {getAssetDestinationPath} from '../utils/common.utils';
import {exec} from 'child_process';
import {promisify} from 'util';
import SqsHelperService from './sqs-helper.service';

class CronService {
  extractItemMasterDataByKey = async (
    vesselMasterData: Paris2MasterItems,
    key: keyof Paris2MasterItems,
    repositoryClass: any,
    context: ContextType,
  ) => {
    logger.debug(
      `[Cron Service] Extracting ${key} from vessel master data`,
      vesselMasterData?.[key],
    );
    try {
      const data = vesselMasterData[key].map((ele: Paris2MasterItemOptions) => {
        return {
          paris2_id: ele.id,
          name: ele.value,
          status: BasicStatus.ACTIVE,
          deleted: false,
          createdBy: '0000',
        };
      });
      logger.debug('[Cron Service] data to be saved', data);
      await repositoryClass.model.bulkCreate(data, {
        updateOnDuplicate: ['name'],
        conflictAttributes: ['paris2_id'],
      });
    } catch (error) {
      logger.error(`[Cron Service] Error extracting ${key}:`, error);
      throw new Error(`Failed to extract ${key}`);
    }
  };

  async processThumbnailTransactions(context: ContextType) {
    logger.info('[Cron Service] Processing thumbnail transactions');
    try {
      // Pass context to getPendingImageThumbnailTransactions
      const transaction = await getPendingImageThumbnailTransactions(context);
      if (!transaction) {
        logger.info('[Cron Service] No pending transactions found');
        return;
      }
      logger.info('transactions', transaction.dataValues.uuid);

      await this.generateAndUploadThumbnail(transaction, context);
    } catch (error) {
      logger.error('[Cron Service] Error generating thumbnail:', error);
      throw new Error('Failed to generate thumbnail');
    }
  }

  async processTenantMigrateTransactions() {
    logger.info('[Cron Service] Processing tenant migrate transactions');
    const sqsHelper = new SqsHelperService();

    for await (const message of sqsHelper.receiveMessages({
      QueueUrl: process.env.TENANT_PROVISIONED_SQS_URL!,
      MaxNumberOfMessages: 10,
      WaitTimeSeconds: 20,
    })) {
      const receiptHandle = message.sqsMessage.ReceiptHandle;
      try {
        for (const tenant of message.records) {
          if (!tenant.key) {
            logger.warn(
              '[Cron Service] No tenant key found in SQS message:',
              message.sqsMessage,
            );
            continue;
          }
          logger.info(
            '[Cron Service] Running migration script for tenant:',
            tenant.key,
          );
          console.log({tenant}, 'message from sqs on cron service');

          const execAsync = promisify(exec);

          const {stdout, stderr} = await execAsync(`npm run db:migrate`, {
            cwd: '/app/services/new-building',
            env: {
              ...process.env,
              'NEW-BUILDING-SERVICE_DB_DATABASE': tenant.key,
              'NEW-BUILDING-SERVICE_DB_HOST':
                process.env['NEW-BUILDING-SERVICE_DB_HOST'],
              'NEW-BUILDING-SERVICE_DB_PORT':
                process.env['NEW-BUILDING-SERVICE_DB_PORT'],
              'NEW-BUILDING-SERVICE_DB_USER':
                process.env['NEW-BUILDING-SERVICE_DB_USER'],
              'NEW-BUILDING-SERVICE_DB_PASSWORD':
                process.env['NEW-BUILDING-SERVICE_DB_PASSWORD'],
            },
          });

          if (stdout) {
            logger.info('[Cron Service] Migration script output:', stdout);
          }
          if (stderr) {
            logger.warn(
              '[Cron Service] Migration script warning output:',
              stderr,
            );
          }
          // Delete the message from the queue after successful processing
          await sqsHelper.deleteMessage({
            QueueUrl: process.env.TENANT_PROVISIONED_SQS_URL!,
            ReceiptHandle: receiptHandle,
          });
          logger.info('[Cron Service] Migration script executed successfully');
        }
      } catch (error: any) {
        logger.error(
          '[Cron Service] Error executing migration script:',
          error.stderr || error.message,
        );
        throw new Error('Failed to execute migration script');
      }
    }
  }

  async generateAndUploadThumbnail(
    transaction: any, // Should be the instance returned by your repository, not a static model
    context: ContextType,
    thumbnailSizes: string[] = ['126px-126px'],
  ) {
    const transactionData = transaction.dataValues as ITransactionAttributes;
    const assetPath = transactionData.parameters?.assetPath as string;
    const tenantId = transactionData.parameters?.tenantId as number;
    const projectId = transactionData.parameters?.projectId as number;

    if ([assetPath, tenantId, projectId].some(param => !param)) {
      await transaction.update({
        status: TransactionStatus.FAILED,
        result: {
          error: `Missing required parameters: ${JSON.stringify(transactionData.parameters)}`,
        },
      });
      return;
    }

    const destinationPath = getAssetDestinationPath(
      tenantId,
      projectId,
      assetPath,
    );
    logger.debug(
      '[Cron Service] Copying file to destination path:',
      destinationPath,
    );
    const [file] = await Promise.all([
      s3HelperService.getObject(assetPath),
      s3HelperService.copyObject(assetPath, destinationPath),
    ]);

    if (!file) {
      await transaction.update({
        status: TransactionStatus.FAILED,
        result: {
          error: 'File not found',
        },
      });
      return;
    }

    const thumbnailExt = 'jpeg';
    const thumbnails = await generateThumbnails(
      file,
      thumbnailSizes,
      {tenantId, projectId},
      {format: thumbnailExt, quality: 90},
    );
    if (!thumbnails || thumbnails.length === 0) {
      await transaction.update({
        status: TransactionStatus.FAILED,
        result: {
          error: 'Failed to generate thumbnails',
        },
      });
      return;
    }

    const fileNameTokens = assetPath.split('.');
    fileNameTokens.pop();
    const fileNameWithoutExtension = fileNameTokens.join('');

    const uploadPromises = thumbnails.map(thumb => {
      const updatedDestinationPath = getAssetDestinationPath(
        tenantId,
        projectId,
        fileNameWithoutExtension,
      );
      return s3HelperService.putObject(
        `${updatedDestinationPath}-${thumb.size}.${thumbnailExt}`,
        thumb.data,
        {
          'Content-Type': thumb.format,
          'Content-Length': thumb.size,
        },
      );
    });

    const uploadResults = await Promise.allSettled(uploadPromises);
    const failedUploads = uploadResults.filter(
      result => result.status === 'rejected',
    );

    if (failedUploads.length > 0) {
      await transaction.update({
        status: TransactionStatus.FAILED,
        result: {
          error: `Failed to upload thumbnails: ${JSON.stringify(
            failedUploads.map(result => result.reason),
          )}`,
        },
      });

      logger.error(
        `[Cron Service] Failed to upload thumbnails for transaction ${transactionData.uuid}: ${JSON.stringify(
          failedUploads.map(result => result.reason),
        )}`,
        transactionData.parameters,
      );
      return;
    }

    await Promise.allSettled([
      transaction.update({
        status: TransactionStatus.COMPLETED,
        result: {
          message: 'Thumbnails generated and uploaded successfully',
          thumbnailSizes,
        },
      }),
      s3HelperService.deleteFile(assetPath),
    ]);
    logger.debug(
      `[Cron Service] Transaction ${transactionData.uuid} completed successfully`,
      transactionData.parameters,
    );
  }
}

export default new CronService();
