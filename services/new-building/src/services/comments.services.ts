import {CreationAttributes, Transaction} from 'sequelize';
import {ContextType, CustomError, isUserHasPermission, logger} from '../utils';
import {ICommentQuery, User} from '../types';
import {
  CommentsResolvePermissions,
  CommentStatus,
  CommentType,
  ProjectDrawingStatus,
} from '../enums';
import {IProjectDrawingCommentInstance} from '../models/drawing-list-comment.model';

// Define input types for comments
type DrawingCommentInput =
  CreationAttributes<IProjectDrawingCommentInstance> & {
    commentType: CommentType.DRAWING;
  };
type InspectionCommentInput = {
  inspectionId: number;
  comments: string;
  status?: CommentStatus;
  commentType: CommentType.INSPECTION;
};
type CommentInput = DrawingCommentInput | InspectionCommentInput;

/**
 * Creates a new comment for either a drawing or an inspection, based on the provided input data.
 *
 * - For `DRAWING` comments:
 *   - Verifies that the drawing exists for the given tenant.
 *   - Prevents commenting on drawings that are already approved.
 *   - Creates a new drawing comment with the provided data and metadata.
 *
 * - For `INSPECTION` comments:
 *   - Verifies that the inspection exists for the given tenant.
 *   - Maps the `comment` field to `comments` and ensures the status is set (defaults to `OPEN`).
 *   - Creates a new inspection comment with the provided data and metadata.
 *
 * @param inputData - The input data for the comment, which can be either a drawing or inspection comment input.
 * @param user - The user creating the comment, containing user and tenant information.
 * @param context - The context object containing repositories and models for database access.
 * @returns A promise that resolves to the created comment entity.
 * @throws {CustomError} If the target drawing or inspection is not found, or if commenting is not allowed.
 */
export async function createComment(
  inputData: CommentInput,
  user: User,
  context: ContextType,
) {
  const {repositories, models} = context;
  switch (inputData.commentType) {
    case CommentType.DRAWING: {
      const parsedData = inputData;
      const result = await repositories.drawingListRepository.findOne({
        id: parsedData.drawingListId,
        tenantId: user?.tenantId,
      });
      if (!result) {
        throw new CustomError('Drawing not found', 404);
      }
      const currentLatestDrawing = await models.ProjectDrawing.findOne({
        where: {
          drawingListId: parsedData.drawingListId,
          tenantId: user?.tenantId,
          status: ProjectDrawingStatus.APPROVED,
        },
        attributes: ['version', 'status'],
        order: [['version', 'DESC']],
        raw: true,
      });
      if (currentLatestDrawing) {
        throw new CustomError("You can't comment on approved drawing", 403);
      }
      const updatedObject = {
        ...parsedData,
        createdBy: user.user_id,
        createdAt: new Date(),
      };
      return repositories.drawingListCommentRepository.create(updatedObject);
    }
    case CommentType.INSPECTION: {
      const inspectionData = inputData;
      const insResult = await repositories.inspectionRepository.findOne({
        id: inspectionData.inspectionId,
        tenantId: user?.tenantId,
      });
      if (!insResult) {
        throw new CustomError('Inspection not found', 404);
      }
      const inspectionObject = {
        ...inspectionData,
        comments: inspectionData.comments, // map 'comment' to 'comments'
        createdBy: user.user_id,
        createdAt: new Date(),
        status: inspectionData.status ?? CommentStatus.OPEN, // ensure status is always set
      };
      delete (inspectionObject as any).comment; // remove 'comment' property if present
      return repositories.inspectionCommentRepository.create(inspectionObject);
    }
  }
}

/**
 * Retrieves all comments based on the provided query parameters and context.
 *
 * Depending on the `commentType`, this function fetches comments either for a drawing list
 * or for an inspection, applying optional filters such as `status`, sorting, and ordering.
 *
 * @param query - The query object containing filters and sorting options for fetching comments.
 * @param context - The context object containing repositories and other dependencies.
 * @returns A promise that resolves to the list of comments matching the query.
 */
export async function getAllComments(
  query: ICommentQuery,
  context: ContextType,
) {
  const {repositories} = context;
  const {
    commentType,
    drawingListId,
    inspectionId,
    sortBy = 'createdAt',
    sortOrder = 'ASC',
    status,
  } = query;
  let result;
  switch (Number(commentType)) {
    case CommentType.DRAWING: {
      const where: Record<string, any> = {drawingListId};
      if (status) where.status = status;
      result = await repositories.drawingListCommentRepository.findAll(
        [],
        false,
        where,
        undefined,
        null,
        [[sortBy, sortOrder]],
      );
      break;
    }
    case CommentType.INSPECTION: {
      const whereInspection: Record<string, any> = {inspectionId};
      if (status) whereInspection.status = status;
      result = await repositories.inspectionCommentRepository.findAll(
        [],
        false,
        whereInspection,
        undefined,
        null,
        [[sortBy, sortOrder]],
      );
      break;
    }
  }
  return result;
}

/**
 * Validates the existence of a related entity (drawing or inspection) for a given comment.
 *
 * Depending on the comment type, this function checks whether the associated drawing or inspection exists
 * in the database for the current tenant. Throws a `CustomError` if the required entity ID is missing in the
 * comment data or if the entity cannot be found.
 *
 * @param type - The type of the comment, indicating the related entity (e.g., drawing or inspection).
 * @param existingComment - The comment object containing data values, including possible related entity IDs.
 * @param user - The user performing the operation, used to determine tenant context.
 * @param repositories - The repositories object providing access to entity repositories.
 * @throws {CustomError} If the related entity ID is missing or the entity is not found.
 */
async function validateRelatedEntity(
  type: CommentType,
  existingComment: {
    dataValues: {drawingListId?: number; inspectionId?: number};
  },
  user: User,
  repositories: ContextType['repositories'],
): Promise<void> {
  if (type === CommentType.DRAWING) {
    const drawingListId = existingComment.dataValues.drawingListId;
    if (drawingListId === undefined) {
      throw new CustomError('drawingListId is missing in comment data', 400);
    }
    const drawingFound = await repositories.drawingListRepository.findOne({
      id: drawingListId,
      tenantId: user?.tenantId,
    });
    if (!drawingFound) {
      throw new CustomError('Drawing not found', 404);
    }
  } else if (type === CommentType.INSPECTION) {
    const inspectionId = existingComment.dataValues.inspectionId;
    if (inspectionId === undefined) {
      throw new CustomError('inspectionId is missing in comment data', 400);
    }
    const inspectionFound = await repositories.inspectionRepository.findOne({
      id: inspectionId,
      tenantId: user?.tenantId,
    });
    if (!inspectionFound) {
      throw new CustomError('Inspection not found', 404);
    }
  }
}

/**
 * Updates an existing comment based on the provided ID and updated data.
 *
 * This function determines the appropriate repository based on the comment type,
 * validates the related entity and user permissions, and updates the comment if all checks pass.
 * It ensures that only users with the correct permissions (either all or self) can update the comment,
 * and prevents updates to comments that are already closed.
 * The operation is performed within a transaction to ensure data consistency.
 *
 * @param id - The unique identifier of the comment to update.
 * @param updatedComment - The new data for the comment.
 * @param user - The user attempting to update the comment.
 * @param context - The context containing repositories and the Sequelize instance.
 * @returns The updated comment object.
 * @throws {CustomError} If the comment is not found, is already closed, or the user lacks permissions.
 */
export async function updateComment(
  id: number,
  updatedComment: CommentInput,
  user: User,
  context: ContextType,
) {
  const {repositories, sequelize} = context;
  const t = await sequelize.transaction();
  let repository;
  switch (updatedComment.commentType) {
    case CommentType.DRAWING:
      repository = repositories.drawingListCommentRepository;
      break;
    case CommentType.INSPECTION:
      repository = repositories.inspectionCommentRepository;
      break;
  }

  try {
    const existingComment = await repository?.findById(id);
    if (!existingComment) {
      throw new CustomError('Comment not found', 404);
    }

    // Use the new helper function for validation
    await validateRelatedEntity(
      updatedComment.commentType,
      existingComment,
      user,
      repositories,
    );

    if (existingComment.dataValues.status === CommentStatus.CLOSED) {
      throw new CustomError(`Comment with ID ${id} is already closed`, 409);
    }

    const isCommentAllPermission = isUserHasPermission(user, [
      CommentsResolvePermissions.COMMENT_All,
    ]);
    const isCommentSelfPermission = isUserHasPermission(user, [
      CommentsResolvePermissions.COMMENT_SELF,
    ]);
    const isSelf = user.user_id === existingComment.dataValues.createdBy;

    if (!isCommentAllPermission && (!isCommentSelfPermission || !isSelf)) {
      throw new CustomError(
        'Forbidden: You do not have the required permissions to perform this action.',
        403,
      );
    }

    const updatedObject = {
      ...updatedComment,
      updatedBy: user.user_id,
      updatedAt: new Date(),
    };
    await repository?.update(id, updatedObject, t);
    await t.commit();
    return repository?.findById(id);
  } catch (error) {
    await t.rollback();
    logger.error('[CommentsService] Error in update comment', error);
    throw error;
  }
}

/**
 * Resolves (closes) all open comments for a given entity (drawing or inspection) based on the comment type.
 *
 * - For `DRAWING` comments, closes all open comments associated with the specified drawing.
 * - For `INSPECTION` comments, closes all open comments associated with the specified inspection.
 *   - If the user lacks the `COMMENT_All` permission, only comments created by the user will be closed.
 *
 * The function performs the operation within a transaction and ensures proper error handling and permission checks.
 *
 * @param entityId - The ID of the entity (drawing or inspection) whose comments are to be resolved.
 * @param commentType - The type of comment (`DRAWING` or `INSPECTION`).
 * @param user - The user performing the operation.
 * @param context - The service context containing repositories and the Sequelize instance.
 * @param transaction - Optional existing transaction to use. If not provided, a new transaction will be created.
 * @returns An object indicating success or failure, with an appropriate message.
 * @throws {CustomError} If the specified entity (drawing or inspection) is not found.
 * @throws {Error} If any other error occurs during the transaction.
 */
export async function resolveAllComments(
  entityId: number,
  commentType: CommentType,
  user: User,
  context: ContextType,
  transaction?: Transaction,
) {
  const {repositories, sequelize} = context;
  const t = transaction ?? (await sequelize.transaction());
  const shouldCommit = !transaction; // Only commit if we created the transaction
  try {
    const isCommentAllPermission = isUserHasPermission(user, [
      CommentsResolvePermissions.COMMENT_All,
    ]);
    const updates = {
      status: CommentStatus.CLOSED,
      updatedBy: user.user_id,
    };
    let where: Record<string, any>;
    let repository;
    switch (Number(commentType)) {
      case CommentType.DRAWING: {
        const drawingFound = await repositories.drawingListRepository.findOne({
          id: entityId,
          tenantId: user?.tenantId,
        });
        if (!drawingFound) {
          throw new CustomError(`Drawing not found`, 404); // Ensure proper error handling
        }
        where = {
          drawingListId: entityId,
          status: CommentStatus.OPEN,
        };
        repository = repositories.drawingListCommentRepository;
        break;
      }
      case CommentType.INSPECTION: {
        const inspectionFound = await repositories.inspectionRepository.findOne(
          {
            id: entityId,
            tenantId: user?.tenantId,
          },
        );
        if (!inspectionFound) {
          throw new CustomError(`Inspection not found`, 404); // Ensure proper error handling
        }
        where = {
          inspectionId: entityId,
          status: CommentStatus.OPEN,
        };
        repository = repositories.inspectionCommentRepository;
        break;
      }
    }

    if (!isCommentAllPermission && commentType == CommentType.INSPECTION) {
      where!.createdBy = user.user_id;
    }
    const result = await repository?.updateAll(updates, where!, t);
    if (shouldCommit) {
      await t.commit();
    }
    if (result?.[0]) {
      return {error: false, message: 'Comment has been resolved successfully'};
    } else {
      return {error: true, message: 'No pending comments to close'};
    }
  } catch (error) {
    if (shouldCommit) {
      await t.rollback();
    }
    logger.error('[CommentsService] Error in update comment', error);
    throw error;
  }
}
