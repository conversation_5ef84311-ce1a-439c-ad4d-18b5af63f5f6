import './utils/env-config.utils';
import express from 'express';
import bodyParser from 'body-parser';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { swaggerOptions } from './configs';
import routes from './routes';
import path from 'path';

dayjs.extend(utc);
dayjs.extend(timezone);

const port = process.env.PORT;
const app = express();
const cors = require('cors');

// Disable the 'x-powered-by' header to prevent information leakage
app.disable('x-powered-by');

app.use(cors());

// In your main app or a middleware

// app.use((req, res, next) => {
//   const user = (req as any).user;
//   console.log({user})
//   const {sequelize, models} = getTenantDb(user.tenantKey);
//   const repositories = createRepositories(models);

//   requestContext.run({models, repositories, sequelize, user}, () => {
//     next();
//   });
// });
app.use('/api/crons', bodyParser.text({ type: '*/*' }));

app.use(bodyParser.json());

// Tell Express to serve files from a directory called "public"
app.use(express.static(path.join(__dirname, '../public')));

app.use('/api', routes);
// Swagger setup
const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use('/explorer', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

app.get('/swagger.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerDocs);
});

// Start the application only if database connection is successful
async function startApp() {
  try {
    // // Initialize the database connection first
    // await initializeDatabase();
    // associateModels(); //----create the relationship between models
    // Only start the server if database connection succeeds
    app.listen(port, () => {
      console.log(`App listening on port ${port}`);
    });
  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

// Start the application
startApp();
