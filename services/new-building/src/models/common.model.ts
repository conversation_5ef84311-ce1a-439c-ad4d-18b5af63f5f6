import {DataTypes} from 'sequelize';

const CommonModel = {
  createdBy: {
    type: DataTypes.STRING,
    field: 'created_by',
    require: true,
    notNull: true,
  },
  updatedBy: {
    type: DataTypes.STRING,
    field: 'updated_by',
  },
  deletedAt: {
    type: DataTypes.DATE,
    field: 'deleted_at',
  },
  deletedBy: {
    type: DataTypes.STRING,
    field: 'deleted_by',
  },
};

export default CommonModel;
