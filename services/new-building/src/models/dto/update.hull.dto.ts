import Joi from 'joi';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

const minDateValidator = (fieldName: string, offsetMonths = 0) => {
  return (value: string, helpers: Jo<PERSON>.CustomHelpers) => {
    const parent = helpers.state.ancestors[0];
    const baseDate = dayjs(parent.createdAt).startOf('day').utc();

    const minDate = offsetMonths
      ? baseDate.subtract(offsetMonths, 'months')
      : baseDate;

    const inputDate = dayjs(value).utc();
    if (!inputDate.isValid()) {
      return helpers.error('date.invalid');
    }

    if (inputDate.isBefore(minDate)) {
      return helpers.error('date.minCreatedAt', {
        field: fieldName,
        minDate: minDate.format('YYYY-MM-DD'),
      });
    }

    return value;
  };
};

const updateHullValidationSchema = {
  body: Joi.object({
    projectId: Joi.number().required(),
    hullNo: Joi.string().trim().min(1).max(25).required(),
    flag: Joi.string().required(),
    createdAt: Joi.date().required(),

    deliveryDate: Joi.date()
      .required()
      .custom(minDateValidator('deliveryDate'))
      .messages({
        'date.minCreatedAt': '"{{#field}}" must be after {{#minDate}}',
        'date.invalid': '"{{#label}}" must be a valid date',
      }),

    launchDate: Joi.date()
      .required()
      .custom(minDateValidator('launchDate'))
      .messages({
        'date.minCreatedAt': '"{{#field}}" must be after {{#minDate}}',
        'date.invalid': '"{{#label}}" must be a valid date',
      }),

    seaTrialDate: Joi.date()
      .required()
      .custom(minDateValidator('seaTrialDate'))
      .messages({
        'date.minCreatedAt': '"{{#field}}" must be after {{#minDate}}',
        'date.invalid': '"{{#label}}" must be a valid date',
      }),

    steelCuttingDate: Joi.date()
      .required()
      .custom(minDateValidator('steelCuttingDate', 18))
      .messages({
        'date.minCreatedAt': '"{{#field}}" must be after {{#minDate}}',
        'date.invalid': '"{{#label}}" must be a valid date',
      }),

    keelLaidDate: Joi.date()
      .required()
      .custom(minDateValidator('keelLaidDate', 18))
      .messages({
        'date.minCreatedAt': '"{{#field}}" must be after {{#minDate}}',
        'date.invalid': '"{{#label}}" must be a valid date',
      }),

    vesselClasses: Joi.array().items(Joi.string().required()).min(1).required(),

    deadWeightGT: Joi.number()
      .min(1)
      .max(999999999999999)
      .allow(null)
      .optional(),
    deadWeightNT: Joi.number()
      .min(1)
      .max(999999999999999)
      .allow(null)
      .optional(),
    capacity: Joi.number().min(1).max(999999999999999).allow(null).optional(),
    status: Joi.number().valid(1, 2, 3).required(),
    shipName: Joi.string().trim().min(1).max(255).allow(null).optional(),
    imo: Joi.number().min(1).max(9999999).allow(null).optional(),
  })
    .custom((value, helpers) => {
      const {steelCuttingDate, keelLaidDate} = value;

      if (
        steelCuttingDate &&
        keelLaidDate &&
        dayjs(keelLaidDate).isBefore(dayjs(steelCuttingDate))
      ) {
        return helpers.error('any.invalidKeelLaid');
      }

      return value;
    })
    .messages({
      'any.invalidKeelLaid': `"keelLaidDate" must be on or after "steelCuttingDate"`,
    }),
};

export default updateHullValidationSchema;
