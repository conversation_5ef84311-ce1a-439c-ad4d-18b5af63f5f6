import Joi from 'joi';


const filterableFields = [
  'projectTypeId',
  'fuelTypeId',
  'engineType',
  'status',
  'shipType',
  'currency',
];

// Start building your query schema object
const querySchema: Record<string, Joi.Schema> = {
  page: Joi.number().integer().min(1).optional(),
  limit: Joi.number().integer().min(1).optional(),
  fields: Joi.alternatives()
    .try(Joi.string(), Joi.array().items(Joi.string()))
    .optional(),

  createdAt: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  }).optional(),
  sortBy: Joi.string()
    .valid(
      'createdAt',
      'engineType',
      'projectType',
      'owner',
      'fuelType',
      'status',
      'name',
      'shipType',
      'currency',
      'totalTimeTaken',
      'contractBudget',
      'expenses',
      'completionDate',
    )
    .optional(),

  sortOrder: Joi.string().valid('ASC', 'DESC').optional(),
  search: Joi.string().optional(),
  searchParam: Joi.string().optional(),
};

// Add dynamic filterable fields
filterableFields.forEach(field => {
  querySchema[field] = Joi.alternatives()
    .try(
      Joi.string(),
      Joi.number(),
      Joi.array().items(Joi.string(), Joi.number()),
    )
    .optional();
});

// Final schema export
const getProjectValidationSchema = {
  query: Joi.object(querySchema),
  params: Joi.object({}),
};

export default getProjectValidationSchema;
