import Joi from 'joi';
import {createHullDtoForProject} from './create.hull.dto';

const createProjectValidationSchema = {
  body: Joi.object({
    name: Joi.string().trim().max(80).required(),
    owner: Joi.string().trim().max(255).required(),
    shipType: Joi.string().required(),
    fuelTypeId: Joi.number().required(),
    engineType: Joi.string().required(),
    projectTypeId: Joi.number().required(),
    projectDescription: Joi.string().trim().max(300).required(),
    status: Joi.number().valid(1).required(),
    hulls: Joi.array().items(createHullDtoForProject.body).min(1).optional(),

    // For assets
    assetName: Joi.string().trim().optional(),
    assetType: Joi.string().trim().optional(),
    assetPath: Joi.string().trim().optional(),

    // Optional fields:
    totalTimeTaken: Joi.number().optional(),
    contractBudget: Joi.number().optional().allow(null),
    expenses: Joi.number().optional().allow(null),
    currency: Joi.string().trim().allow(null).optional(),
    isShipTypeCustom: Joi.boolean().default(false).optional(),
    isEngineTypeCustom: Joi.boolean().default(false).optional(),
  }),
};
export default createProjectValidationSchema;
