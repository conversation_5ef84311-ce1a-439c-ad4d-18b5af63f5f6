import Joi from 'joi';

// Start building your query schema object
const querySchema: Record<string, Joi.Schema> = {
  page: Joi.number().integer().min(1).optional(),
  limit: Joi.number().integer().min(1).optional(),
  search: Joi.string().trim().optional(),
};

// Final schema export
const getHullValidationSchema = {
  query: Joi.object(querySchema),
  params: Joi.object({}),
};

export default getHullValidationSchema;
