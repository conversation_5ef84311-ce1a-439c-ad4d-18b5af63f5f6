import Joi from 'joi';
import {baseQueryValidationSchema} from './get.base-search.dto';

const getDrawingsValidationSchema = {
  query: Joi.object({
    ...baseQueryValidationSchema,
    projectId: Joi.number().integer().min(1).required(),
    sortBy: Joi.string()
      .valid('createdAt', 'name', 'discipline', 'status', 'id')
      .optional()
      .default('createdAt'),
    name: Joi.string().optional(),
  }),
  params: Joi.object({}),
};

export default getDrawingsValidationSchema;
