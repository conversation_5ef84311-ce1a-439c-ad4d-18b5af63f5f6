import dayjs from 'dayjs';
import <PERSON><PERSON> from 'joi';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
const createProjectDrawingValidationSchema = {
  body: Joi.object({
    assetName: Joi.string().trim().required(),
    drawingListId: Joi.number().required(),
    assetType: Joi.string().trim().required(),
    assetPath: Joi.string().trim().required(),
    status: Joi.number().min(1).max(3).required(),
    dueDate: Joi.date()
      .min(dayjs().startOf('date').utc().toDate())
      .when('status', {
        is: Joi.valid(1, 2), // If status is 1 or 2, dueDate is required
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    hullNoId: Joi.array<number>().optional(),
  }),
};
export default createProjectDrawingValidationSchema;
