import Joi from 'joi';

// Start building your query schema object
const querySchema: Record<string, Joi.Schema> = {
  createCommentDto: Joi.number().integer().min(1).optional(),
  drawingListId: Joi.number().integer().min(1).optional(),
  sortBy: Joi.string().trim().optional(),
  sortOrder: Joi.string().trim().optional(),
  status: Joi.number().valid(1, 2).optional(),
};

// Final schema export
const getCommentValidationSchema = {
  query: Joi.object(querySchema),
  params: Joi.object({}),
};

export default getCommentValidationSchema;
