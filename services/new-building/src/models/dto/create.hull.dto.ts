import Joi from 'joi';
import dayjs from 'dayjs';
import {hullValidation} from './common.hull.dto';

/**
 * Returns a Joi validation schema for creating/updating a Hull.
 * @param isProjectIdRequired - Whether projectId should be required or optional.
 */

export function getHullSchema(isProjectIdRequired: boolean) {
  const schema = Joi.object({
    ...hullValidation,
    projectId: isProjectIdRequired
      ? Joi.number().required()
      : Joi.number().optional(),

    hullNo: Joi.string().trim().min(1).max(25).required(),
    flag: Joi.string().required(),
    deadWeightGT: Joi.number()
      .min(1)
      .max(999999999999999)
      .allow(null)
      .optional(),
    deadWeightNT: Joi.number()
      .min(1)
      .max(999999999999999)
      .allow(null)
      .optional(),

    shipName: Joi.string().trim().min(1).max(255).allow(null).optional(),
    imo: Joi.number().min(1).max(9999999).allow(null).optional(),
    capacity: Joi.number().min(1).max(999999999999999).allow(null).optional(),
    status: Joi.number().valid(1).optional(),

    assetName: Joi.string().trim().required(),
    assetType: Joi.string().trim().required(),
    assetPath: Joi.string().trim().required(),
  })
    .custom((value, helpers) => {
      if (
        value.steelCuttingDate &&
        value.keelLaidDate &&
        dayjs(value.keelLaidDate).isBefore(dayjs(value.steelCuttingDate))
      ) {
        return helpers.error('any.invalidKeelDate');
      }
      return value;
    })
    .messages({
      'any.invalidKeelDate': `"Keel Laid Date" must be on or after "Steel Cutting Date"`,
    });

  return schema;
}

const createHullDto = {body: getHullSchema(true)};
const createHullDtoForProject = {body: getHullSchema(false)};
export {createHullDto, createHullDtoForProject};
