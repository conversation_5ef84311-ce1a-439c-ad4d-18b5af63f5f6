import Joi from 'joi';
import {baseQueryValidationSchema} from './get.base-search.dto';

const getInspectionsValidationSchema = {
  query: Joi.object({
    ...baseQueryValidationSchema,
    submissionDate: Joi.object({
      startDate: Joi.date().iso().optional(),
      endDate: Joi.date().iso().optional(),
    }).optional(),
    sortBy: Joi.string()
      .valid(
        'createdAt',
        'submissionDate',
        'dueDate',
        'time',
        'discipline',
        'description',
        'forOwner',
        'forClass',
        'project.name',
        'hull.hullNo',
        'id',
      )
      .optional()
      .default('createdAt'),
    hullId: Joi.number().optional(),
    commentStatus: Joi.number().valid(1, 2).optional(),
  }),
  params: Joi.object({}),
};

export default getInspectionsValidationSchema;
