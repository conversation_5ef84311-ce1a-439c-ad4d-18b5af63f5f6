import Joi from 'joi';

export const baseQueryValidationSchema = {
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(1).optional().default(100),
  sortOrder: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
  search: Joi.string().optional(),
  createdAt: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  }).optional(),
  dueDate: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  }).optional(),
  discipline: Joi.string().optional(),
  status: Joi.number().valid(1, 2, 3).optional(),
};
