import Joi from 'joi';

const createObservationValidationSchema = Joi.object({
  inspectionId: Joi.number().integer().min(1).required(),
  assets: Joi.array()
    .items(
      Joi.object({
        fileKey: Joi.string().required(),
        assetName: Joi.string().required(),
        assetType: Joi.string().required(),
      }),
    )
    .min(1)
    .required(),
});

export default createObservationValidationSchema;
