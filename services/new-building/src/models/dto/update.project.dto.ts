import Joi from 'joi';

const updateProjectValidationSchema = {
  body: Joi.object({
    name: Joi.string().trim().max(80).required(),
    owner: Joi.string().trim().max(255).required(),
    shipType: Joi.string().required(),
    fuelTypeId: Joi.number().required(),
    engineType: Joi.string().required(),
    projectTypeId: Joi.number().required(),
    projectDescription: Joi.string().trim().max(300).required(),
    contractBudget: Joi.number().optional().allow(null),
    expenses: Joi.number().optional().allow(null),
    totalTimeTaken: Joi.number().optional(),
    currency: Joi.string().optional().allow(null),
    isEngineTypeCustom: Joi.boolean().default(false).optional(),
    isShipTypeCustom: Joi.boolean().default(false).optional(),
  }),
};
export default updateProjectValidationSchema;
