import dayjs from 'dayjs';
import <PERSON><PERSON> from 'joi';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
export const hullValidation = {
  deliveryDate: Joi.date()
    .min(dayjs().startOf('date').utc().toDate())
    .required(),
  steelCuttingDate: Joi.date()
    .min(dayjs().subtract(18, 'months').startOf('day').toDate())
    .required(),
  keelLaidDate: Joi.date()
    .min(dayjs().subtract(18, 'months').startOf('day').toDate())
    .required(),
  launchDate: Joi.date().min(dayjs().startOf('date').utc().toDate()).required(),
  seaTrialDate: Joi.date()
    .min(dayjs().startOf('date').utc().toDate())
    .required(),
  vesselClasses: Joi.array().items(Joi.string().required()).min(1).required(),
};
