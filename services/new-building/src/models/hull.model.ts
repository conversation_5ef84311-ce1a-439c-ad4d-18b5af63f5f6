import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IHullAttributes} from '../types';
import {HullStatus} from '../enums';
import CommonModel from './common.model';
import {commonAssetModel} from './common-asset.model';

export interface HullCreationAttributes
  extends Optional<IHullAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type HullInstance = Model<IHullAttributes, HullCreationAttributes>;

export function defineHullModel(sequelize: Sequelize) {
  return sequelize.define<HullInstance>(
    'Hull',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenantId: {
        type: DataTypes.INTEGER,
        field: 'tenant_id',
      },
      hullNo: {
        type: DataTypes.STRING,
        field: 'hull_no',
        allowNull: false,
      },
      shipName: {
        type: DataTypes.STRING,
        field: 'ship_name',
      },
      imo: {
        type: DataTypes.INTEGER,
        field: 'imo',
      },
      flag: {
        type: DataTypes.STRING,
        field: 'flag',
        allowNull: false,
      },
      deadWeightGT: {
        type: DataTypes.INTEGER,
        field: 'deadweight_gt',
      },
      deadWeightNT: {
        type: DataTypes.INTEGER,
        field: 'deadweight_nt',
      },
      steelCuttingDate: {
        type: DataTypes.DATE,
        field: 'steel_cutting_date',
        allowNull: false,
      },
      keelLaidDate: {
        type: DataTypes.DATE,
        field: 'keel_laid_date',
        allowNull: false,
      },
      launchDate: {
        type: DataTypes.DATE,
        field: 'launch_date',
        allowNull: false,
      },
      capacity: {
        type: DataTypes.INTEGER,
        field: 'capacity',
      },
      seaTrialDate: {
        type: DataTypes.DATE,
        field: 'sea_trial_date',
        allowNull: false,
      },
      deliveryDate: {
        type: DataTypes.DATE,
        field: 'delivery_date',
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: HullStatus.ON_GOING,
        validate: {
          isIn: [
            [HullStatus.ON_GOING, HullStatus.ON_HOLD, HullStatus.COMPLETED],
          ],
        },
      },
      ...commonAssetModel,
      ...CommonModel,
    },
    {
      modelName: 'Hull',
      tableName: 'hull',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
