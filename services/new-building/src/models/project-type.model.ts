import {Model, Optional, Sequelize} from 'sequelize';
import {IProjectTypeAttributes} from '../types/project-type.types';
import MasterDataModel from './masterdata.model';
import CommonModel from './common.model';

export interface ProjectTypeCreationAttributes
  extends Optional<IProjectTypeAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type ProjectTypeInstance = Model<
  IProjectTypeAttributes,
  ProjectTypeCreationAttributes
>;

export function defineProjectTypeModel(sequelize: Sequelize) {
  return sequelize.define<Model<ProjectTypeCreationAttributes>>(
    'ProjectType',
    {
      ...MasterDataModel,
      ...CommonModel,
    },
    {
      modelName: 'ProjectType',
      tableName: 'project_type',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
