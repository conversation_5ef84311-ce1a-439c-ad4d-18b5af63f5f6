import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IInspectionAttributes} from '../types/inspection.types';
import CommonModel from './common.model';
import {InspectionStatus, InspectionType} from '../enums/inspection.enum';

export interface InspectionCreationAttributes
  extends Optional<IInspectionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IInspectionInstance = Model<
  IInspectionAttributes,
  InspectionCreationAttributes
>;

export function defineInspectionModel(sequelize: Sequelize) {
  return sequelize.define<IInspectionInstance>(
    'Inspection',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenantId: {
        type: DataTypes.INTEGER,
        field: 'tenant_id',
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          isIn: [[InspectionType.HULL]],
        },
      },
      hullId: {
        type: DataTypes.INTEGER,
        field: 'hull_id',
        references: {
          model: 'hull',
          key: 'id',
        },
      },
      inspectionSourceFileId: {
        type: DataTypes.INTEGER,
        field: 'inspection_source_file_id',
      },
      projectId: {
        type: DataTypes.INTEGER,
        field: 'project_id',
        references: {
          model: 'project',
          key: 'id',
        },
      },
      submissionDate: {
        type: DataTypes.DATE,
        field: 'submission_date',
      },
      dueDate: {
        type: DataTypes.DATE,
        field: 'due_date',
      },
      time: {
        type: DataTypes.TIME,
      },
      description: {
        type: DataTypes.STRING,
      },
      discipline: {
        type: DataTypes.STRING,
      },
      forOwner: {
        type: DataTypes.BOOLEAN,
        field: 'for_owner',
      },
      forClass: {
        type: DataTypes.BOOLEAN,
        field: 'for_class',
      },
      remark: {
        type: DataTypes.STRING,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          isIn: [[InspectionStatus.ONGOING, InspectionStatus.CLOSED]],
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'created_at',
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'updated_at',
      },

      ...CommonModel,
    },
    {
      modelName: 'Inspection',
      tableName: 'inspection',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
