import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IInspectionSourceTypes} from '../types';

export interface InspectionSourceModelAttributes
  extends Optional<IInspectionSourceTypes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IInspectionSourceInstance = Model<
  IInspectionSourceTypes,
  InspectionSourceModelAttributes
>;

export function defineInspectionSourceModel(sequelize: Sequelize) {
  return sequelize.define<IInspectionSourceInstance>(
    'InspectionSource',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: DataTypes.INTEGER,
        field: 'project_id',
        allowNull: false,
      },
      hullId: {
        type: DataTypes.INTEGER,
        field: 'hull_id',
        allowNull: false,
      },
      sourceFilePath: {
        type: DataTypes.INTEGER,
        field: 'source_file_path',
        allowNull: false,
      },
    },
    {
      modelName: 'InspectionSource',
      tableName: 'inspection_source_file',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
