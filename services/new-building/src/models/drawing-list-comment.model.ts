import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IProjectDrawingCommentAttributes} from '../types';
import {CommonCommentModel} from './common.comment.model';

export interface ProjectDrawingCommentCreationAttributes
  extends Optional<
    IProjectDrawingCommentAttributes,
    'id' | 'createdAt' | 'updatedAt'
  > {}

export type IProjectDrawingCommentInstance = Model<
  IProjectDrawingCommentAttributes,
  ProjectDrawingCommentCreationAttributes
>;

export function defineDrawingListCommentModel(sequelize: Sequelize) {
  return sequelize.define<IProjectDrawingCommentInstance>(
    'DrawingListComment',
    {
      ...CommonCommentModel,
      drawingListId: {
        type: DataTypes.INTEGER,
        field: 'project_drawing_list_id',
        allowNull: false,
      },
    },
    {
      modelName: 'DrawingListComment',
      tableName: 'project_drawing_list_comment',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
