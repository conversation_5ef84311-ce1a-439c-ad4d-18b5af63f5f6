import {DataTypes, Model, Sequelize} from 'sequelize';
import CommonModel from './common.model';

export class Tenant extends Model {}

export function defineTenantModel(sequelize: Sequelize) {
  Tenant.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      companyName: {
        type: DataTypes.STRING,
        field: 'company_name',
        allowNull: false,
      },
      firstName: {
        type: DataTypes.STRING,
        field: 'first_name',
      },
      lastName: {
        type: DataTypes.STRING,
        field: 'last_name',
      },
      email: DataTypes.STRING,
      key: DataTypes.STRING,
      address: DataTypes.STRING,
      city: DataTypes.STRING,
      state: {
        type: DataTypes.CHAR(2),
      },
      zip: DataTypes.INTEGER,
      country: DataTypes.STRING,
      tenantConfig: {
        type: DataTypes.JSON,
        field: 'tenant_config',
      },
      status: DataTypes.STRING,
      ...CommonModel,
    },
    {
      sequelize,
      modelName: 'Tenant',
      tableName: 'tenants',
      schema: 'main',
      timestamps: true, // or false, depending on your CommonModel
    },
  );
  return Tenant;
}
