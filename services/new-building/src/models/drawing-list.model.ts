import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import CommonModel from './common.model';
import {IDrawingListAttributes} from '../types';
import {DrawingListStatus} from '../enums';

export interface DrawingListCreationAttributes
  extends Optional<IDrawingListAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IDrawingListInstance = Model<
  IDrawingListAttributes,
  DrawingListCreationAttributes
>;

export function defineDrawingListModel(sequelize: Sequelize) {
  return sequelize.define<IDrawingListInstance>(
    'DrawingList',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenantId: {
        type: DataTypes.INTEGER,
        field: 'tenant_id',
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      projectDrawingListSourceFileId: {
        type: DataTypes.NUMBER,
        field: 'project_drawing_list_source_file_id',
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: DrawingListStatus.ONGOING,
        validate: {
          isIn: [[DrawingListStatus.ONGOING, DrawingListStatus.COMPLETED]],
        },
      },
      projectId: {
        type: DataTypes.INTEGER,
        field: 'project_id',
        allowNull: false,
      },
      discipline: {
        type: DataTypes.STRING,
        field: 'discipline',
        allowNull: true,
      },
      drawingNo: {
        type: DataTypes.STRING,
        field: 'drawing_no',
        allowNull: false,
      },
      ...CommonModel,
    },
    {
      modelName: 'DrawingList',
      tableName: 'project_drawing_list',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
