import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IProjectAttributes} from '../types';
import {ProjectStatus} from '../enums';
import CommonModel from './common.model';
import {commonAssetModel} from './common-asset.model';

export interface ProjectCreationAttributes
  extends Optional<IProjectAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type ProjectInstance = Model<
  IProjectAttributes,
  ProjectCreationAttributes
>;

export function defineProjectModel(sequelize: Sequelize) {
  return sequelize.define<ProjectInstance>(
    'Project',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tenantId: {
        type: DataTypes.INTEGER,
        field: 'tenant_id',
        references: {
          model: 'tenants',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      owner: DataTypes.STRING,
      shipType: {
        type: DataTypes.STRING,
        field: 'ship_type',
      },
      fuelTypeId: {
        type: DataTypes.INTEGER,
        field: 'fuel_type_id',
        references: {
          model: 'fuel_type',
          key: 'id',
        },
      },
      engineType: {
        type: DataTypes.STRING,
        field: 'engine_type',
      },
      projectTypeId: {
        type: DataTypes.INTEGER,
        field: 'project_type_id',
        references: {
          model: 'project_type',
          key: 'id',
        },
      },
      projectDescription: {
        type: DataTypes.TEXT,
        field: 'project_description',
      },
      totalTimeTaken: {
        type: DataTypes.BIGINT,
        field: 'total_time_taken',
      },
      contractBudget: {
        type: DataTypes.BIGINT,
        field: 'contract_budget',
      },
      expenses: DataTypes.BIGINT,
      currency: {
        type: DataTypes.STRING,
        field: 'currency',
      },
      completionDate: {
        type: DataTypes.DATE,
        field: 'completion_date',
      },
      isEngineTypeCustom: {
        type: DataTypes.BOOLEAN,
        field: 'is_engine_type_custom',
      },
      isShipTypeCustom: {
        type: DataTypes.BOOLEAN,
        field: 'is_ship_type_custom',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: ProjectStatus.ON_GOING,
        validate: {
          isIn: [[ProjectStatus.ON_GOING, ProjectStatus.CLOSED]],
        },
      },
      ...commonAssetModel,
      ...CommonModel,
    },
    {
      modelName: 'Project',
      tableName: 'project',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
