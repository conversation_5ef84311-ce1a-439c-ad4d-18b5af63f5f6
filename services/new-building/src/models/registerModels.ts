// models/registerModels.ts
import {Sequelize} from 'sequelize';
import {defineProjectModel} from './project.model';
import {defineHullModel} from './hull.model';
import {defineProjectTypeModel} from './project-type.model';
import {defineFuelTypeModel} from './fuel-type.model';
import {defineProjectHullModel} from './project-hull.model';
import {defineInspectionObservationModel} from './inspection-observation.model';
import {defineDrawingListModel} from './drawing-list.model';
import {defineTransactionModel} from './transaction.model';
import {defineDrawingListCommentModel} from './drawing-list-comment.model';
import {defineInspectionCommentModel} from './inspection-comment.model';
import {defineHullClassModel} from './hull.class.model';
import {defineInspectionModel} from './inspection.model';
import {defineInspectionSourceModel} from './inspection-source.model';
import {defineDrawingSourceModel} from './drawing-source.model';
import {defineProjectDrawingModel} from './project-drawing.model';
import associateModels from './associations';
// ...import all other model factories

export interface Models {
  Project: ReturnType<typeof defineProjectModel>; // This is a Sequelize ModelCtor<ProjectInstance>
  Hull: ReturnType<typeof defineHullModel>;
  ProjectType: ReturnType<typeof defineProjectTypeModel>;
  FuelType: ReturnType<typeof defineFuelTypeModel>;
  ProjectHull: ReturnType<typeof defineProjectHullModel>;
  HullClass: ReturnType<typeof defineHullClassModel>;

  ProjectDrawing: ReturnType<typeof defineProjectDrawingModel>;
  DrawingsList: ReturnType<typeof defineDrawingListModel>;
  DrawingListComment: ReturnType<typeof defineDrawingListCommentModel>;

  InspectionObservation: ReturnType<typeof defineInspectionObservationModel>;

  Transaction: ReturnType<typeof defineTransactionModel>;

  InspectionComment: ReturnType<typeof defineInspectionCommentModel>;

  InspectionSource: ReturnType<typeof defineInspectionSourceModel>;
  Inspection: ReturnType<typeof defineInspectionModel>;
  InspectionSourceFile: ReturnType<typeof defineInspectionSourceModel>;
  DrawingSource: ReturnType<typeof defineDrawingSourceModel>;

  // ...other models
}

export function registerModels(sequelize: Sequelize) {
  const models = {
    Project: defineProjectModel(sequelize),
    Hull: defineHullModel(sequelize),
    ProjectType: defineProjectTypeModel(sequelize),
    FuelType: defineFuelTypeModel(sequelize),
    ProjectHull: defineProjectHullModel(sequelize),
    InspectionObservation: defineInspectionObservationModel(sequelize),
    DrawingsList: defineDrawingListModel(sequelize),
    Transaction: defineTransactionModel(sequelize),
    DrawingListComment: defineDrawingListCommentModel(sequelize),
    InspectionComment: defineInspectionCommentModel(sequelize),
    HullClass: defineHullClassModel(sequelize),
    InspectionSource: defineInspectionSourceModel(sequelize),
    Inspection: defineInspectionModel(sequelize),
    InspectionSourceFile: defineInspectionSourceModel(sequelize),
    DrawingSource: defineDrawingSourceModel(sequelize),
    ProjectDrawing: defineProjectDrawingModel(sequelize),

    // Add other models here
    // ...add all other models
  };
  // If you have associations, call them here, passing `models` as needed
  // associateModels(models);

  associateModels(models);

  return models;
}
