import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IInspectionCommentAttributes} from '../types/inspection-comment.types';
import {CommonCommentModel} from './common.comment.model';

export interface InspectionCommentCreationAttributes
  extends Optional<
    IInspectionCommentAttributes,
    'id' | 'createdAt' | 'updatedAt'
  > {}

export type IInspectionCommentInstance = Model<
  IInspectionCommentAttributes,
  InspectionCommentCreationAttributes
>;

export function defineInspectionCommentModel(sequelize: Sequelize) {
  return sequelize.define<IInspectionCommentInstance>(
    'InspectionListComment',
    {
      ...CommonCommentModel,
      inspectionId: {
        type: DataTypes.INTEGER,
        field: 'inspection_id',
        allowNull: false,
      },
    },
    {
      modelName: 'InspectionListComment',
      tableName: 'inspection_comment',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
