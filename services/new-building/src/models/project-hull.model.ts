import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IProjectHullAttributes} from '../types';
import CommonModel from './common.model';

export interface ProjectHullCreationAttributes
  extends Optional<IProjectHullAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IProjectHullInstance = Model<
  IProjectHullAttributes,
  ProjectHullCreationAttributes
>;

export function defineProjectHullModel(sequelize: Sequelize) {
  return sequelize.define<IProjectHullInstance>(
    'ProjectHull',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: DataTypes.INTEGER,
        field: 'project_id',
        references: {
          model: 'project',
          key: 'id',
        },
      },
      hullId: {
        type: DataTypes.INTEGER,
        field: 'hull_id',
        references: {
          model: 'hull',
          key: 'id',
        },
      },
      ...CommonModel,
    },
    {
      modelName: 'ProjectHull',
      tableName: 'project_hull',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
