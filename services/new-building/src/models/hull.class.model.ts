import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IHullClassAttributes} from '../types';
import CommonModel from './common.model';

export interface HullClassCreationAttributes
  extends Optional<IHullClassAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IHullClassInstance = Model<
  IHullClassAttributes,
  HullClassCreationAttributes
>;

export function defineHullClassModel(sequelize: Sequelize) {
  return sequelize.define<IHullClassInstance >(
    'HullClass',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      vesselClass: {
        type: DataTypes.STRING,
        field: 'vessel_class',
        allowNull: false,
      },
      hullId: {
        type: DataTypes.INTEGER,
        field: 'hull_id',
        allowNull: false,
      },
      ...CommonModel,
    },
    {
      modelName: 'HullClass',
      tableName: 'hull_class',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
