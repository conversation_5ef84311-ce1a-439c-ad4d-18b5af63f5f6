import {DataTypes} from 'sequelize';
import {CommentStatus} from '../enums';
import CommonModel from './common.model';

export const CommonCommentModel = {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  comments: {
    type: DataTypes.STRING,
    field: 'comments',
    allowNull: false,
  },
  status: {
    type: DataTypes.INTEGER,
    field: 'status',
    allowNull: false,
    defaultValue: CommentStatus.OPEN,
    validate: {
      isIn: [[CommentStatus.OPEN, CommentStatus.CLOSED]],
    },
  },

  ...CommonModel,
};
