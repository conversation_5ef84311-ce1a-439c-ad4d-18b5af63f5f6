import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import CommonModel from './common.model';
import {IProjectDrawingsAttributes} from '../types/project-drawings.types';
import {ProjectDrawingStatus} from '../enums';
import {commonAssetModel} from './common-asset.model';

export interface ProjectDrawingCreationAttributes
  extends Optional<
    IProjectDrawingsAttributes,
    'id' | 'createdAt' | 'updatedAt'
  > {}

export type IProjectDrawingsInstance = Model<
  IProjectDrawingsAttributes,
  ProjectDrawingCreationAttributes
>;

export function defineProjectDrawingModel(sequelize: Sequelize) {
  return sequelize.define<IProjectDrawingsInstance>(
    'ProjectDrawings',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      tenantId: {
        type: DataTypes.INTEGER,
        field: 'tenant_id',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          isIn: [
            [
              ProjectDrawingStatus.REFERENCE_ONLY,
              ProjectDrawingStatus.APPROVED,
              ProjectDrawingStatus.UNDER_REVIEW,
            ],
          ],
        },
      },
      drawingListId: {
        type: DataTypes.INTEGER,
        field: 'drawing_list_id',
      },
      dueDate: {
        type: DataTypes.DATE,
        field: 'due_date',
      },
      version: {
        type: DataTypes.INTEGER,
      },
      ...commonAssetModel,
      ...CommonModel,
    },
    {
      modelName: 'ProjectDrawings',
      tableName: 'project_drawing',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
