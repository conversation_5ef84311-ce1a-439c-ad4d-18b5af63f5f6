const associateModels = (models: any) => {
  const {
    FuelType,
    Hull,
    Project,
    ProjectType,
    ProjectHull,
    DrawingsList,
    ProjectDrawing,
    HullClass,
    InspectionObservation,
    InspectionComment,
    Inspection,
  } = models;

  ProjectType.hasMany(Project, {
    foreignKey: 'projectTypeId',
    sourceKey: 'id',
    as: 'projects',
  });

  FuelType.hasMany(Project, {
    foreignKey: 'fuelTypeId',
    sourceKey: 'id',
    as: 'projects',
  });

  Project.belongsTo(ProjectType, {
    foreignKey: 'projectTypeId',
    targetKey: 'id',
    as: 'projectType',
  });

  Project.belongsTo(FuelType, {
    foreignKey: 'fuelTypeId',
    targetKey: 'id',
    as: 'fuelType',
  });

  Project.belongsToMany(Hull, {
    through: ProjectHull,
    foreignKey: 'projectId',
    otherKey: 'hullId',
    as: 'hulls',
  });

  Hull.belongsToMany(Project, {
    through: ProjectHull,
    foreignKey: 'hullId',
    otherKey: 'projectId',
    as: 'projects',
  });

  ProjectHull.belongsTo(Hull, {
    foreignKey: 'hullId',
    targetKey: 'id',
    as: 'hull',
  });

  Project.hasMany(DrawingsList, {
    foreignKey: 'project_id',
    sourceKey: 'id',
    as: 'drawings',
  });

  DrawingsList.belongsTo(Project, {
    foreignKey: 'project_id',
    targetKey: 'id',
    as: 'project',
  });

  DrawingsList.hasMany(ProjectDrawing, {
    foreignKey: 'drawing_list_id',
    sourceKey: 'id',
    as: 'projectDrawings',
  });

  ProjectDrawing.belongsTo(DrawingsList, {
    foreignKey: 'drawing_list_id',
    targetKey: 'id',
    as: 'drawingList',
  });

  Project.hasMany(Inspection, {
    foreignKey: 'projectId',
    sourceKey: 'id',
    as: 'inspections',
  });

  Inspection.belongsTo(Project, {
    foreignKey: 'projectId',
    targetKey: 'id',
    as: 'project',
  });

  Hull.hasMany(Inspection, {
    foreignKey: 'hullId',
    sourceKey: 'id',
    as: 'inspections',
  });

  Inspection.belongsTo(Hull, {
    foreignKey: 'hullId',
    targetKey: 'id',
    as: 'hull',
  });

  Inspection.hasMany(InspectionObservation, {
    foreignKey: 'inspectionId',
    as: 'assets',
  });

  Inspection.hasMany(InspectionComment, {
    foreignKey: 'inspectionId',
    as: 'comment',
  });

  Hull.hasMany(HullClass, {
    foreignKey: 'hullId',
    constraints: false,
    as: 'hullClass',
  });
};

export default associateModels;