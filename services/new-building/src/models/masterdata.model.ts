import {DataTypes} from 'sequelize';
import {BasicStatus} from '../enums';

const MasterDataModel = {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  status: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: BasicStatus.ACTIVE,
    validate: {
      isIn: [[BasicStatus.ACTIVE, BasicStatus.INACTIVE]],
    },
  },
};

export default MasterDataModel;
