import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IInspectionObservationAttributes} from '../types';
import CommonModel from './common.model';

export interface InspectionObservationCreationAttributes
  extends Optional<
    IInspectionObservationAttributes,
    'id' | 'createdAt' | 'updatedAt'
  > {}

export type IInspectionObservationInstance = Model<
  IInspectionObservationAttributes,
  InspectionObservationCreationAttributes
>;

export function defineInspectionObservationModel(sequelize: Sequelize) {
  return sequelize.define<IInspectionObservationInstance>(
    'Assets',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      assetName: {
        type: DataTypes.STRING,
        field: 'asset_name',
        allowNull: false,
      },
      assetType: {
        type: DataTypes.STRING,
        field: 'asset_type',
        allowNull: true,
      },
      assetPath: {
        type: DataTypes.STRING,
        field: 'asset_path',
        allowNull: true,
      },
      version: {
        type: DataTypes.INTEGER,
        field: 'version',
        allowNull: true,
      },
      inspectionId: {
        type: DataTypes.INTEGER,
        field: 'inspection_id',
        allowNull: false,
      },
      ...CommonModel,
    },
    {
      modelName: 'InspectionObservation',
      tableName: 'inspection_observation',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
