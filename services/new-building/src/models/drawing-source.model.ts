import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {IDrawingSourceTypes} from '../types';

export interface DrawingSourceModelAttributes
  extends Optional<IDrawingSourceTypes, 'id' | 'createdAt' | 'updatedAt'> {}

export type IDrawingSourceInstance = Model<
  IDrawingSourceTypes,
  DrawingSourceModelAttributes
>;

export function defineDrawingSourceModel(sequelize: Sequelize) {
  return sequelize.define<IDrawingSourceInstance>(
    'DrawingsSource',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: DataTypes.INTEGER,
        field: 'project_id',
        allowNull: false,
      },
      sourceFilePath: {
        type: DataTypes.INTEGER,
        field: 'source_file_path',
        allowNull: false,
      },
    },
    {
      modelName: 'DrawingsSource',
      tableName: 'project_drawing_list_source_file',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
