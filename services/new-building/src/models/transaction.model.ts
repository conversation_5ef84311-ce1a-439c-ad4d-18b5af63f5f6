import {DataTypes, Model, Optional, Sequelize} from 'sequelize';
import {TransactionStatus, TransactionType} from '../enums';
import CommonModel from './common.model';
import {ITransactionAttributes} from '../types/transaction.types';

export interface TransactionCreationAttributes
  extends Optional<
    ITransactionAttributes,
    'uuid' | 'retry' | 'createdAt' | 'updatedAt'
  > {}

export type TransactionInstance = Model<
  ITransactionAttributes,
  TransactionCreationAttributes
>;

export function defineTransactionModel(sequelize: Sequelize) {
  return sequelize.define<TransactionInstance>(
    'Transaction',
    {
      uuid: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          isIn: [Object.values(TransactionType)],
        },
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          isIn: [Object.values(TransactionStatus)],
        },
      },
      parameters: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      retry: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      result: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      ...CommonModel,
    },
    {
      modelName: 'Transaction',
      tableName: 'transaction',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
