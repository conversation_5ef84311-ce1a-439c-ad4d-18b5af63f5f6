import {Model, Optional, Sequelize} from 'sequelize';
import {IFuelTypeAttributes} from '../types/fuel-type.types';
import MasterDataModel from './masterdata.model';
import CommonModel from './common.model';

export interface FuelTypeCreationAttributes
  extends Optional<IFuelTypeAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export type FuelTypeInstance = Model<
  IFuelTypeAttributes,
  FuelTypeCreationAttributes
>;

export function defineFuelTypeModel(sequelize: Sequelize) {
  return sequelize.define<FuelTypeInstance>(
    'FuelType',
    {
      ...MasterDataModel,
      ...CommonModel,
    },
    {
      modelName: 'FuelType',
      tableName: 'fuel_type',
      schema: 'main',
      timestamps: true,
      underscored: true,
    },
  );
}
