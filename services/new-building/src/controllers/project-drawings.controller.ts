import {
  getRequestContext,
  handleErrors,
  logDebug,
  logError,
  logInfo,
} from '../utils';
import {Request, Response, Router} from 'express';
import {validatorMiddleware, verifyToken} from '../middleware';
import {IFindAllQuery, User} from '../types';
import {projectDrawingService} from '../services';
import {DrawingVersionCreationPermissions} from '../enums';

const router = Router();

/**
 * @swagger
 * /api/project-drawings/{id}:
 *   get:
 *     summary: Get drawing details by ID
 *     description: Retrieve drawing details for a specific drawing by its ID.
 *     tags:
 *       - Project Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the drawing to retrieve.
 *         example: 1
 *     responses:
 *       200:
 *         description: Drawing details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 name:
 *                   type: string
 *                   example: "Drawing A"
 *                 dueDate:
 *                   type: string
 *                   format: date
 *                   example: "2025-05-01"
 *                 assetType:
 *                   type: string
 *                   example: "image"
 *                 assetPath:
 *                   type: string
 *                   example: "folder/drawing-a.png"
 *                 status:
 *                   type: integer
 *                   example: 1
 *       404:
 *         description: Drawing not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Drawing not found.
 *       500:
 *         description: Failed to retrieve drawing details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

const getDrawingById = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for ProjectDrawings Controller ProjectDrawings Action retrieving project drawing for Id ${req.params.id}`,
      method: 'GET',
      params: req.params,
    });
    const context = getRequestContext();
    const drawings = await projectDrawingService.findDrawingsById(
      Number(req.params.id),
      context,
    );
    logDebug(user, {
      message: `Execution Completed for ProjectDrawings Controller ProjectDrawings Action retrieving project drawing for Id ${req.params.id}`,
      method: 'GET',
      response: drawings,
    });
    res.status(200).json(drawings);
  } catch (error) {
    logError(user, {
      message:
        `Error received in ProjectDrawings Controller when retrieving project drawing for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the specific drawing version');
  }
};

/**
 * @swagger
 * /api/project-drawings:
 *   get:
 *     summary: Get all drawings
 *     description: Fetches all drawing lists associated with a specific project ID.
 *     tags:
 *       - Project Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: drawingListId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the Drawing Listing.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page (default is 100)
 *     responses:
 *       200:
 *         description: Drawing details fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     description: The ID of the drawing.
 *                     example: 1
 *                   name:
 *                     type: string
 *                     description: The name of the drawing.
 *                     example: "Mechanical Drawings"
 *                   drawingListId:
 *                     type: integer
 *                     description: The ID of the associated drawing list.
 *                     example: 101
 *                   dueDate:
 *                     type: string
 *                     description: The due date of drawing.
 *                     example: "2025-04-25T10:00:00Z"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: No drawings found for the specified drawing list ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: No drawings found for the specified drawing list ID.
 *       500:
 *         description: Failed to fetch drawing lists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to fetch drawing lists.
 */
const getAllDrawingsByListId = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for ProjectDrawings Controller ProjectDrawings Action retrieving project drawings`,
      method: 'GET',
      query: req.query,
    });
    const context = getRequestContext();
    const drawings = await projectDrawingService.getAllDrawings(
      req.query as IFindAllQuery,
      context,
    );

    logDebug(user, {
      message: `Execution Completed for ProjectDrawings Controller ProjectDrawings Action retrieving project drawings`,
      method: 'GET',
      response: drawings,
    });

    res.status(200).json(drawings);
  } catch (error) {
    logError(user, {
      message:
        `Error received in ProjectDrawings Controller when retrieving project drawings ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the drawing versions');
  }
};

/**
 * @swagger
 * /api/project-drawings:
 *   post:
 *     summary: Save drawing details for a specific drawing list
 *     description: Save drawing details including name, due date, asset type, and asset path for a specific drawing list.
 *     tags:
 *       - Project Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               drawingListId:
 *                 type: integer
 *                 description: The id of the drawing list.
 *                 example: 1
 *               name:
 *                 type: string
 *                 description: The name of the drawing.
 *                 example: "Drawing A"
 *               dueDate:
 *                 type: string
 *                 format: date
 *                 description: The due date for the drawing.
 *                 example: "2025-05-01"
 *               assetType:
 *                 type: string
 *                 description: The type of the asset.
 *                 example: "image"
 *               assetName:
 *                 type: string
 *                 description: The name of the asset.
 *                 example: "image.png"
 *               assetPath:
 *                 type: string
 *                 description: The path to the asset.
 *                 example: "folder/drawing-a.png"
 *               status:
 *                 type: integer
 *                 description: status of the drawing.
 *                 example: 1
 *               hullNoId:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: List of hull number IDs.
 *                 example: [1, 2, 3]
 *     responses:
 *       200:
 *         description: Drawing details saved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Drawing details saved successfully.
 *                 result:
 *                   type: object
 *                   description: The saved drawing details.
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: ""
 *                     dueDate:
 *                       type: string
 *                       format: date
 *                       example: ""
 *                     createdBy:
 *                       type: string
 *                       example: ""
 *                     version:
 *                       type: integer
 *                       example: 1
 *                     status:
 *                       type: integer
 *                       example: 2
 *                     assetType:
 *                       type: string
 *                       example: ""
 *                     assetPath:
 *                       type: string
 *                       example: ""
 *                     assetName:
 *                       type: string
 *                       example: ""
 *                     drawingListId:
 *                       type: integer
 *                       example: 123
 *       400:
 *         description: Invalid input data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Invalid input data.
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to save drawing details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to save drawing details.
 */

const createDrawingVersion = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for ProjectDrawings Controller ProjectDrawings Action create drawing version`,
      method: 'POST',
      body: req.body,
    });
    const {
      drawingListId,
      assetName,
      dueDate,
      assetType,
      assetPath,
      status,
      hullNoId,
    } = req.body;

    const context = getRequestContext();
    const result = await projectDrawingService.createProjectDrawings(
      Number(drawingListId),
      {dueDate, assetName, assetType, assetPath, status, hullNoId},
      user,
      context,
    );

    const response = {
      message: 'Drawing version saved successfully.',
      error: false,
      result,
    };
    logDebug(user, {
      message: `Execution Completed for ProjectDrawings Controller ProjectDrawings Action create drawing version`,
      method: 'POST',
      response: response,
    });
    res.status(200).json(response);
  } catch (error) {
    logError(user, {
      message:
        `Error received in ProjectDrawings Controller when create drawing version ` +
        (error as Error).message,
      method: 'POST',
      error: error as Error,
    });
    handleErrors(
      error as Error,
      res,
      '',
      'An error occurred while saving the drawings',
    );
  }
};

/**
 * @swagger
 * /api/project-drawings/{drawingListId}/{id}:
 *   delete:
 *     summary: Delete a drawing by ID and Drawing List ID
 *     description: Deletes a specific drawing identified by its ID and associated Drawing List ID.
 *     tags:
 *       - Project Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: drawingListId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the drawing list associated with the drawing.
 *         example: 101
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the drawing to delete.
 *         example: 1
 *     responses:
 *       200:
 *         description: Drawing deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Drawing deleted successfully.
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Drawing not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Drawing not found.
 *       409:
 *         description: Drawings already completed/deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Drawings already completed/deleted
 *       500:
 *         description: Failed to delete the drawing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Internal server error.
 */
const deleteDrawingById = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for ProjectDrawings Controller ProjectDrawings Action delete drawing for Id ${req.params.id}`,
      method: 'DELETE',
      params: req.params,
    });
    const context = getRequestContext();
    const deletedDrawings = await projectDrawingService.deleteDrawingsById(
      Number(req.params.drawingListId),
      Number(req.params.id),
      user,
      context,
    );

    logDebug(user, {
      message: `Execution Completed for ProjectDrawings Controller ProjectDrawings Action delete drawing for Id ${req.params.id}`,
      method: 'DELETE',
      response: deletedDrawings,
    });

    res.status(200).json(deletedDrawings);
  } catch (error) {
    logError(user, {
      message:
        `Error received in ProjectDrawings Controller when delete drawing for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'DELETE',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'deleting the project drawing');
  }
};

router.get(
  '/',
  verifyToken([DrawingVersionCreationPermissions.VIEW]),
  getAllDrawingsByListId,
);
router.post(
  '/',
  verifyToken([DrawingVersionCreationPermissions.CREATE]),
  validatorMiddleware('createProjectDrawing'),
  createDrawingVersion,
);

router.get(
  '/:id',
  verifyToken([DrawingVersionCreationPermissions.VIEW]),
  getDrawingById,
);

router.delete(
  '/:drawingListId/:id',
  verifyToken([DrawingVersionCreationPermissions.DELETE]),
  deleteDrawingById,
);

export default router;
