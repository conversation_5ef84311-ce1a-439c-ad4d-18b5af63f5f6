import {Router, Request, Response} from 'express';
import {observationService} from '../services';
import {User} from '../types';
import {
  handleErrors,
  logInfo,
  logDebug,
  logError,
  getRequestContext,
} from '../utils';
import {verifyToken, validatorMiddleware} from '../middleware';
import {ObservationPermissions} from '../enums';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Observations
 *   description: Observations management API
 */

/**
 * @swagger
 * /api/observations:
 *   post:
 *     summary: Create a new observation
 *     tags:
 *       - Observations
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - inspectionId
 *               - assets
 *             properties:
 *               inspectionId:
 *                 type: integer
 *                 description: ID of the inspection to which the observation belongs
 *               assets:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - fileKey
 *                     - assetName
 *                     - assetType
 *                   properties:
 *                     fileKey:
 *                       type: string
 *                       description: The file key for the asset
 *                     assetName:
 *                       type: string
 *                       description: The name of the asset
 *                     assetType:
 *                       type: string
 *                       description: The type of the asset
 *     responses:
 *       201:
 *         description: Observation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Observation created successfully
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: ID of the created asset
 *                       assetName:
 *                         type: string
 *                         description: Name of the created asset
 *                       referenceId:
 *                         type: integer
 *                         description: Reference ID (inspection ID)
 *                       referenceType:
 *                         type: string
 *                         description: Reference type (e.g., INSPECTION)
 *                       assetPath:
 *                         type: string
 *                         description: Path of the asset
 *                       assetType:
 *                         type: string
 *                         description: Type of the asset
 *                       assetVersion:
 *                         type: integer
 *                         description: Version of the asset
 *                       createdBy:
 *                         type: string
 *                         description: User ID of the creator
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid request payload
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Inspection not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Inspection not found.
 *       500:
 *         description: Failed to fetch unique values.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Failed to fetch unique values.
 */
router.post(
  '/',
  verifyToken([ObservationPermissions.CREATE]),
  validatorMiddleware('createObservationDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Observations Controller Observations Action create a observations',
        method: 'POST',
        body: req.body,
      });

      const {inspectionId, assets} = req.body;
      const context = getRequestContext();
      await observationService.createObservation(
        inspectionId,
        assets,
        user,
        context,
      );

      const result = {
        error: false,
        message: 'Observation created successfully',
      };

      logDebug(user, {
        message:
          'Execution Started for Observations Controller Observations Action create a observations',
        method: 'POST',
        response: result,
      });

      res.status(201).json(result);
    } catch (error) {
      logError(user, {
        message:
          'Error received in Observations Controller when create a observations ' +
          (error as Error).message,
        method: 'POST',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'saving the observation');
    }
  },
);

export default router;
