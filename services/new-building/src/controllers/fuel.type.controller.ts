import {Router, Request, Response} from 'express';
import {getAllFuelTypes} from '../services';
import {verifyToken} from '../middleware';
import ErrorMessages from '../utils/error.messages.utils';
import {getRequestContext, logDebug, logError, logInfo} from '../utils';
import {User} from '../types';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     FuelType:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: Diesel
 *         status:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2025-04-08T06:51:57.905Z"
 *         createdBy:
 *           type: string
 *           example: admin
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         updatedBy:
 *           type: string
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         deletedBy:
 *           type: string
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 */

/**
 * @swagger
 * /api/fuel-type:
 *   get:
 *     summary: Retrieve all fuel types
 *     tags: [Fuel Type]
 *     responses:
 *       200:
 *         description: A list of fuel types
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/FuelType'
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Some server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error retrieving fuel types
 *                 error:
 *                   type: object
 *                   description: Detailed error information
 */
router.get('/', verifyToken(), async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for FuelType Controller FuelType Action retrieving fuel type`,
      method: 'GET',
    });
    const context = getRequestContext();
    const fuelType = await getAllFuelTypes(context);

    logDebug(user, {
      message: `Execution Completed for FuelType Controller FuelType Action retrieving fuel type`,
      method: 'GET',
      response: fuelType,
    });

    res.status(200).json(fuelType);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Flags FuelType when retrieving fuel type ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    res.status(500).json({error: ErrorMessages.errorFetching('Fuel types')});
  }
});

export default router;
