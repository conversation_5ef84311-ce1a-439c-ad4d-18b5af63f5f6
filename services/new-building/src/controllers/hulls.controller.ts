import {Router, Request, Response} from 'express';
import {
  createHull,
  getAllHulls,
  findByIdHull,
  updateHull,
  deleteHull,
} from '../services';
import {hullMiddleware, validatorMiddleware, verifyToken} from '../middleware';
import {
  getRequestContext,
  handleErrors,
  logDebug,
  logError,
  logInfo,
} from '../utils';
import ErrorMessages from '../utils/error.messages.utils';
import {User} from '../types/user.types';
import {IFindAllQuery} from '../types';
import {HullPermissions} from '../enums';
const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Hull:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         hullNo:
 *           type: string
 *           example: "HULL-001"
 *         shipName:
 *           type: string
 *           example: "MV Liberty"
 *         imo:
 *           type: integer
 *           example: 9876543
 *         flag:
 *           type: string
 *           example: "USA"
 *         deadWeightGT:
 *           type: number
 *           example: 15500.75
 *         deadWeightNT:
 *           type: number
 *           example: 9200.5
 *         steelCuttingDate:
 *           type: string
 *           format: date
 *           example: "2024-01-15"
 *         keelLaidDate:
 *           type: string
 *           format: date
 *           example: "2024-03-10"
 *         launchDate:
 *           type: string
 *           format: date
 *           example: "2024-05-20"
 *         capacity:
 *           type: number
 *           example: 2500
 *         seaTrialDate:
 *           type: string
 *           format: date
 *           example: "2024-07-01"
 *         deliveryDate:
 *           type: string
 *           format: date
 *           example: "2024-08-10"
 *         status:
 *           type: string
 *           example: "COMPLETED"
 *         deleted:
 *           type: boolean
 *           example: false
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00.000Z"
 *         createdBy:
 *           type: string
 *           nullable: true
 *           example: "admin"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: "2024-04-01T08:45:00.000Z"
 *         updatedBy:
 *           type: string
 *           nullable: true
 *           example: "admin"
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: null
 *         deletedBy:
 *           type: string
 *           nullable: true
 *           example: null
 */

/**
 * @swagger
 * /api/hulls:
 *   post:
 *     summary: Create a new hull for a given project
 *     tags:
 *       - Hulls
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *               - hullNo
 *               - keelLaidDate
 *               - steelCuttingDate
 *               - launchDate
 *               - seaTrialDate
 *               - flag
 *               - status
 *               - deliveryDate
 *               - deadWeightGT
 *               - capacity
 *               - vesselClasses
 *               - shipName
 *               - assetName
 *               - assetType
 *               - assetPath
 *             properties:
 *               projectId:
 *                 type: integer
 *                 example: 30
 *               hullNo:
 *                 type: string
 *                 example: HUL-100005
 *               keelLaidDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               steelCuttingDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               launchDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               seaTrialDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               flag:
 *                 type: string
 *                 example: Hong Kong
 *               deadWeightGT:
 *                 type: number
 *                 example: 123456
 *               capacity:
 *                 type: number
 *                 example: 15
 *               status:
 *                 type: integer
 *                 example: 1
 *               deliveryDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               imo:
 *                 type: integer
 *                 example: 1213232
 *               vesselClasses:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["BV"]
 *               shipName:
 *                 type: string
 *                 example: Indiana Jones
 *               assetName:
 *                 type: string
 *                 example: 1/projects/ship1.jpeg
 *               assetType:
 *                 type: string
 *                 example: image/jpeg
 *               assetPath:
 *                 type: string
 *                 example: 1/projects/bff167a1-272f-4f68-8c2d-0242fcfd04f7.jpeg
 *     responses:
 *       201:
 *         description: Hull created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 19
 *                 hullNo:
 *                   type: string
 *                   example: HUL-100005
 *                 keelLaidDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 steelCuttingDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 launchDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 seaTrialDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 flag:
 *                   type: string
 *                   example: Hong Kong
 *                 deadWeightGT:
 *                   type: string
 *                   example: "123456"
 *                 capacity:
 *                   type: string
 *                   example: "15"
 *                 status:
 *                   type: integer
 *                   example: 1
 *                 deliveryDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 imo:
 *                   type: integer
 *                   example: 1213232
 *                 shipName:
 *                   type: string
 *                   example: Indiana Jones
 *                 assetName:
 *                   type: string
 *                   example: 1/projects/ship1.jpeg
 *                 assetType:
 *                   type: string
 *                   example: image/jpeg
 *                 assetPath:
 *                   type: string
 *                   example: 1/projects/bff167a1-272f-4f68-8c2d-0242fcfd04f7.jpeg
 *                 createdBy:
 *                   type: string
 *                   format: uuid
 *                   example: 19544d8c-0c30-4a73-bf58-9b40a1132882
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-06-02T06:01:14.436Z
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-06-02T06:01:14.436Z
 *                 deadWeightNT:
 *                   type: number
 *                   nullable: true
 *                   example: null
 *                 updatedBy:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 deletedAt:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 deletedBy:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.post(
  '/',
  verifyToken([HullPermissions.CREATE]),
  validatorMiddleware('createHullDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Hulls Controller Hull Action create a hull`,
        method: 'POST',
        body: req.body,
      });
      const hullData = {
        ...req.body,
        tenantId: user?.['tenantId'],
        createdBy: user?.user_id,
      };
      const projectId = req.body['projectId'];
      const context = getRequestContext();
      const hull = await createHull(projectId, hullData, context);
      logDebug(user, {
        message: `Execution Completed for Hulls Controller Hull Action create a hull`,
        method: 'POST',
        response: hull,
      });
      res.status(201).json(hull);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Hulls Controller when create a comment ` +
          (error as Error).message,
        method: 'POST',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'creating hull');
    }
  },
);

/**
 * @swagger
 * /api/hulls:
 *   get:
 *     summary: Get all hulls
 *     tags:
 *       - Hulls
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter hulls
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page (default is 10)
 *     responses:
 *       200:
 *         description: A list of hulls
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Hull'
 *                 total:
 *                   type: integer
 *                   description: Total number of hulls
 *                 page:
 *                   type: integer
 *                   description: Current page number
 *                 limit:
 *                   type: integer
 *                   description: Number of items per page
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Error retrieving hulls for selected filter
  */
router.get(
  '/',
  verifyToken([HullPermissions.VIEW]),
  validatorMiddleware('getHullDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Hulls Controller Hull Action retrieving hulls`,
        method: 'GET',
        query: req.query,
      });
      const context = getRequestContext();
      const response = await getAllHulls(req.query as IFindAllQuery, context);
      logDebug(user, {
        message: `Execution Completed for Hulls Controller Hull Action retrieving hulls`,
        method: 'GET',
        response: response,
      });
      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Hulls Controller when retrieving hulls ` +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      res.status(500).json({
        error: ErrorMessages.errorFetching('hulls for selected filter'),
      });
    }
  },
);

/**
 * @swagger
 * /api/hulls/{id}:
 *   get:
 *     summary: Get a specific hull by ID
 *     description: Retrieves the details of a hull by its ID.
 *     tags:
 *       - Hulls
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: The ID of the hull to retrieve
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Hull retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Hull'
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Hull not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Hull not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.get(
  '/:id',
  verifyToken([HullPermissions.VIEW]),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Hulls Controller Hull Action retrieving hull for Id ${req.params.id}`,
        method: 'GET',
        params: req.params,
      });
      const context = getRequestContext();
      const response = await findByIdHull(Number(req.params.id), context);
      logDebug(user, {
        message: `Execution Completed for Hulls Controller Hull Action retrieving hull for Id ${req.params.id}`,
        method: 'GET',
        response: response,
      });
      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Hulls Controller when retrieving hull for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'listing the hulls');
    }
  },
);

/**
 * @swagger
 * /api/hulls/{id}:
 *   put:
 *     summary: Update a hull by its ID
 *     tags:
 *       - Hulls
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: ID of the hull to update
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               hullNo:
 *                 type: string
 *                 example: HUL-100005
 *               keelLaidDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               steelCuttingDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               launchDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               seaTrialDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               flag:
 *                 type: string
 *                 example: Hong Kong
 *               deadWeightGT:
 *                 type: number
 *                 example: 123456
 *               deadWeightNT:
 *                 type: number
 *                 nullable: true
 *                 example: null
 *               capacity:
 *                 type: number
 *                 example: 15
 *               status:
 *                 type: integer
 *                 example: 1
 *               deliveryDate:
 *                 type: string
 *                 format: date
 *                 example: 2025-06-02
 *               imo:
 *                 type: integer
 *                 example: 1213232
 *               vesselClasses:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["BV"]
 *               shipName:
 *                 type: string
 *                 example: Indiana Jones II
 *               projectId:
 *                 type: integer
 *                 example: 30
 *     responses:
 *       200:
 *         description: Hull updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 19
 *                 hullNo:
 *                   type: string
 *                   example: HUL-100005
 *                 shipName:
 *                   type: string
 *                   example: Indiana Jones II
 *                 imo:
 *                   type: integer
 *                   example: 1213232
 *                 flag:
 *                   type: string
 *                   example: Hong Kong
 *                 deadWeightGT:
 *                   type: string
 *                   example: "123456"
 *                 deadWeightNT:
 *                   type: number
 *                   nullable: true
 *                   example: null
 *                 steelCuttingDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 keelLaidDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 launchDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 capacity:
 *                   type: string
 *                   example: "15"
 *                 seaTrialDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 deliveryDate:
 *                   type: string
 *                   format: date
 *                   example: 2025-06-02
 *                 status:
 *                   type: integer
 *                   example: 1
 *                 assetName:
 *                   type: string
 *                   example: 1/projects/ship1.jpeg
 *                 assetType:
 *                   type: string
 *                   example: image/jpeg
 *                 assetPath:
 *                   type: string
 *                   example: 1/projects/bff167a1-272f-4f68-8c2d-0242fcfd04f7.jpeg
 *                 createdBy:
 *                   type: string
 *                   format: uuid
 *                   example: 19544d8c-0c30-4a73-bf58-9b40a1132882
 *                 updatedBy:
 *                   type: string
 *                   format: uuid
 *                   example: 19544d8c-0c30-4a73-bf58-9b40a1132882
 *                 deletedAt:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 deletedBy:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-06-02T06:01:14.436Z
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-06-02T06:10:46.768Z
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Hull not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Hull not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.put(
  '/:id',
  verifyToken([HullPermissions.EDIT]),
  hullMiddleware(),
  validatorMiddleware('updateHullDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Hulls Controller Hull Action edit hull for Id ${req.params.id}`,
        method: 'PUT',
        params: req.params,
        body: req.body,
      });
      const {id} = req.params;
      const {projectId, ...hull} = req.body;
      const hullData = {
        ...hull,
        updatedBy: user?.user_id,
      };
      const context = getRequestContext();
      const updatedHull = await updateHull(
        Number(id),
        projectId,
        hullData,
        user,
        context,
      );

      logDebug(user, {
        message: `Execution Completed for Hulls Controller Hull Action edit hull for Id ${req.params.id}`,
        method: 'PUT',
        response: updatedHull,
      });
      res.status(200).json(updatedHull);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Hulls Controller when edit hull for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'PUT',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'updating the hull');
    }
  },
);

/**
 * @swagger
 * /api/hulls/{id}:
 *   delete:
 *     summary: Soft delete a hull by ID
 *     description: Marks the hull as deleted (soft delete) using its ID.
 *     tags:
 *       - Hulls
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: ID of the hull to delete
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Hull deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Hull deleted successfully
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Hull not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Hull not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.delete(
  '/:id',
  verifyToken([HullPermissions.DELETE]),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Hulls Controller Hull Action delete hull for Id ${req.params.id}`,
        method: 'DELETE',
        params: req.params,
      });
      const context = getRequestContext();
      await deleteHull(Number(req.params.id), user, context);
      const response = {message: 'Hull deleted successfully'};
      logDebug(user, {
        message: `Execution Completed for Hulls Controller Hull Action delete hull for Id ${req.params.id}`,
        method: 'DELETE',
        response: response,
      });
      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Hulls Controller when delete hull for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'DELETE',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'deleting hull');
    }
  },
);

export default router;
