import { Router, Request, Response } from 'express';
import { verifyToken } from '../middleware';
import { cronService, snsHelperService } from '../services';
import { CustomError, getRequestContext, logger, logDebug, logInfo } from '../utils';
import { User } from '../types';

const router = Router();

/**
 * @swagger
 * /api/crons/image-thumbnail:
 *   post:
 *     summary: Generate image thumbnails
 *     description: Triggers the generation of image thumbnails.
 *     tags:
 *       - Image Thumbnails
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Thumbnails generated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Thumbnails generated successfully."
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  '/image-thumbnail',
  verifyToken(),
  (req: Request, res: Response) => {
    const user = (req as Request & { user: User }).user;

    logInfo(user, {
      message: `Execution Started for Crons Controller Crons Action process thumbnail`,
      method: 'POST',
      body: req.body,
    });
    const context = getRequestContext();
    cronService.processThumbnailTransactions(context).catch(error => {
      console.error('Error generating thumbnail:', error);
    });

    logDebug(user, {
      message: `Execution Completed for Crons Controller Crons Action process thumbnail`,
      method: 'POST',
    });
    res.status(200).json();
  },
);

router.post('/tenant-migrate', async (req: Request, res: Response) => {
  await checkAndExecuteCron(
    String(process.env.SNS_TOPIC_TENANT_MIGRATE),
    req,
    res,
    () => cronService.processTenantMigrateTransactions(),
  );
});

async function checkAndExecuteCron(
  topicName: string,
  req: Request,
  res: Response,
  cb: () => Promise<unknown>,
) {
  const body = JSON.parse(req.body);
  logger.info(`Received request on execute cron: ${JSON.stringify(body)}`);
  try {
    const subscriptionCheck =
      await snsHelperService.confirmSnsSubscription(body);
    if (subscriptionCheck) {
      checkCronHeaders(topicName, req);
      const result = await cb();
      return result;
    }
  } catch (error) {
    logger.error('Error processing cron request:', error);
    throw new CustomError('Error processing cron request', 500);
  }
  res.status(200).json();
}

/* The `checkCronHeaders` method is a private method within the `CronTaskController` class. It is
  responsible for verifying the headers of the incoming request to ensure that the request is
  authorized to access the specific SNS topic based on the topic name provided. */
function checkCronHeaders(topicName: string, request: Request) {
  const headers = request.headers;
  const awsEnv = process.env.CRON_AWS_ENV ?? '';
  let snsTopicARN = `arn:aws:sns:${process.env.CRON_AWS_REGION ?? ''}:${process.env.CRON_AWS_ACCOUNT ?? ''
    }:${topicName}`;
  snsTopicARN = snsTopicARN.replace(/{{ENV}}/g, awsEnv);
  if (
    !(
      headers['x-amz-sns-topic-arn'] === snsTopicARN ||
      awsEnv.toLowerCase() === 'testenv'
    )
  ) {
    throw new CustomError(`Unauthorized access to topic: ${topicName}`, 403);
  }
}

export default router;
