import {
  getRequestContext,
  handleErrors,
  logDebug,
  logError,
  logInfo,
} from '../utils';
import {Request, Response, Router} from 'express';
import {validatorMiddleware, verifyToken} from '../middleware';
import {drawingsService} from '../services';
import {IFindAllQuery, User} from '../types';
import {DrawingPlanApprovalPermissions} from '../enums';

const router = Router();

/**
 * @swagger
 * /api/drawing-lists:
 *   post:
 *     summary: Save drawings from an Excel file
 *     description: Processes an Excel file and saves the drawings data into the database.
 *     tags:
 *       - Drawings
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileKey:
 *                 type: string
 *                 description: The unique key/path of the Excel file in S3.
 *                 example: folder/drawings.xlsx
 *               projectId:
 *                 type: number
 *                 description: Project ID.
 *                 example: 1
 *     responses:
 *       200:
 *         description: Drawings saved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Drawings saved successfully.
 *                 result:
 *                   type: object
 *                   description: The saved drawings data.
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: There is some error when saving the drawings
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       415:
 *         description: Unsupported Media Type
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: The file is empty or contains invalid data.
 *       500:
 *         description: Failed to save drawings.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to save drawings.
 */
const saveDrawings = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message:
        'Execution Started for DrawingsList Controller DrawingsList Action add drawingslist from excel',
      method: 'POST',
      body: req.body,
    });
    const context = getRequestContext();
    await drawingsService.createDrawingFromExcel(req.body, user, context);
    const response = {
      error: false,
      message: 'Drawings saved successfully',
    };

    logDebug(user, {
      message:
        'Execution Completed for DrawingsList Controller DrawingsList Action add drawingslist from excel',
      method: 'POST',
      response: response,
    });
    res.json(response);
  } catch (error) {
    logError(user, {
      message:
        'Error received in DrawingsList Controller when add drawingslist from excel ' +
        (error as Error).message,
      method: 'POST',
      error: error as Error,
    });
    handleErrors(
      error as Error,
      res,
      '',
      'An error occurred while saving the drawings',
    );
  }
};

/**
 * @swagger
 * /api/drawing-lists/{id}:
 *   get:
 *     summary: Get drawing details by ID
 *     description: Fetches the details of a specific drawing list by its ID.
 *     tags:
 *       - Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the drawing list to fetch.
 *     responses:
 *       200:
 *         description: Drawing details fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: The ID of the drawing list.
 *                   example: 1
 *                 name:
 *                   type: string
 *                   description: The name of the drawing list.
 *                   example: "Mechanical Drawings"
 *                 projectId:
 *                   type: integer
 *                   description: The ID of the associated project.
 *                   example: 101
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   description: The creation timestamp of the drawing list.
 *                   example: "2025-04-25T10:00:00Z"
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   description: The last update timestamp of the drawing list.
 *                   example: "2025-04-26T12:00:00Z"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Drawing list not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Drawing list not found.
 *       500:
 *         description: Failed to fetch drawing details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to fetch drawing details.
 */
const getDrawingListById = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action retrieving drawinglist for Id ${req.params.id}`,
      method: 'GET',
      params: req.params,
    });
    const context = getRequestContext();
    const responseDrawingList = await drawingsService.findDrawingsById(
      Number(req.params.id),
      user,
      context,
    );

    logDebug(user, {
      message: `Execution Completed for DrawingsList Controller DrawingsList Action retrieving drawinglist for Id ${req.params.id}`,
      method: 'GET',
      response: responseDrawingList,
    });

    res.status(200).json(responseDrawingList);
  } catch (error) {
    logError(user, {
      message:
        `Error received in DrawingsList Controller when retrieving drawinglist for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the drawings');
  }
};

/**
 * @swagger
 * /api/drawing-lists:
 *   get:
 *     summary: Get all drawings
 *     description: Fetches a paginated list of drawings based on query parameters, including sorting and filtering options.
 *     tags:
 *       - Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: integer
 *           minimum: 1
 *         required: true
 *         description: The ID of the project to fetch drawing lists for.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (default is 1).
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page (default is 100).
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, name, discipline, status, id]
 *         description: Field to sort the results by (default is "createdAt").
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order for the results (default is "DESC").
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter drawings by name, discipline, or drawing number.
 *       - in: query
 *         name: createdAt[startDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering drawings by creation date.
 *       - in: query
 *         name: createdAt[endDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering drawings by creation date.
 *       - in: query
 *         name: dueDate[startDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering drawings by due date.
 *       - in: query
 *         name: dueDate[endDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering drawings by due date.
 *       - in: query
 *         name: discipline
 *         schema:
 *           type: string
 *         description: Filter drawings by discipline.
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter drawings by status.
 *     responses:
 *       200:
 *         description: Drawing lists fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: The ID of the drawing.
 *                         example: 1
 *                       name:
 *                         type: string
 *                         description: The name of the drawing.
 *                         example: "Mechanical Drawing"
 *                       discipline:
 *                         type: string
 *                         description: The discipline of the drawing.
 *                         example: "Mechanical"
 *                       drawingNo:
 *                         type: string
 *                         description: The unique number of the drawing.
 *                         example: "D12345"
 *                       status:
 *                         type: integer
 *                         enum: [1, 2]
 *                         description: Filter drawings by status (1 for ONGOING, 2 for COMPLETED).
 *                         example: "Active"
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         description: The creation timestamp of the drawing.
 *                         example: "2025-04-25T10:00:00Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         description: The last update timestamp of the drawing.
 *                         example: "2025-04-26T12:00:00Z"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       description: Total number of drawing records.
 *                       example: 100
 *                     totalPages:
 *                       type: integer
 *                       description: Total number of pages.
 *                       example: 10
 *                     page:
 *                       type: integer
 *                       description: Current page number.
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       description: Number of items per page.
 *                       example: 10
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       400:
 *         description: Invalid query parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: "Validation error: Invalid query parameters."
 *       500:
 *         description: Failed to fetch drawing lists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: "Failed to fetch drawing lists."
 */
const getAllDrawingLists = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action retrieving drawinglist`,
      method: 'GET',
      query: req.query,
    });
    const context = getRequestContext();
    const drawings = await drawingsService.getAllDrawings(
      req.query as IFindAllQuery,
      user,
      context,
    );
    logDebug(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action retrieving drawinglist`,
      method: 'GET',
      response: drawings,
    });

    res.status(200).json(drawings);
  } catch (error) {
    logError(user, {
      message:
        `Error received in DrawingsList Controller when retrieving drawinglist ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the drawings');
  }
};

/**
 * @swagger
 * /api/drawing-lists/{id}:
 *   delete:
 *     summary: Delete a drawing list by ID
 *     description: Deletes a specific drawing list by its ID.
 *     tags:
 *       - Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the drawing list to delete.
 *     responses:
 *       200:
 *         description: Drawing list deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Drawing list deleted successfully.
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
const deleteDrawingListById = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action delete drawinglist for Id ${req.params.id}`,
      method: 'DELETE',
      params: req.params,
    });

    const context = getRequestContext();
    const drawingList = await drawingsService.deleteDrawingsById(
      Number(req.params.id),
      user,
      context,
    );

    logDebug(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action delete drawinglist for Id ${req.params.id}`,
      method: 'DELETE',
      response: drawingList,
    });

    res.status(200).json(drawingList);
  } catch (error) {
    logError(user, {
      message:
        `Error received in DrawingsList Controller when delete drawinglist for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'DELETE',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'deleting the drawings');
  }
};

/**
 * @swagger
 * /api/drawing-lists/options/{key}:
 *   get:
 *     summary: Get unique values for a specific key
 *     description: Fetches all unique values for a given key (attribute) from the Inspection table.
 *     tags:
 *       - Drawings
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Unique values fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unique values for key 'discipline' fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "Mechanical"
 *       400:
 *         description: Invalid key provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to fetch unique values.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Failed to fetch unique values.
 */
const getDataForUniqueKey = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action retrieving drawinglist for uniquekey ${req.params.key}`,
      method: 'GET',
      params: req.params,
    });
    const {key} = req.params;
    const {projectId} = req.query;
    const context = getRequestContext();
    const result = await drawingsService.getOptionsByKey(
      key,
      Number(projectId),
      context,
    );
    logDebug(user, {
      message: `Execution Started for DrawingsList Controller DrawingsList Action retrieving drawinglist for uniquekey ${req.params.key}`,
      method: 'GET',
      response: result,
    });
    res.status(200).json(result);
  } catch (error) {
    logError(user, {
      message:
        `Error received in DrawingsList Controller when retrieving drawinglist for uniquekey ${req.params.key} ` +
        (error as Error).message,
      method: 'POST',
      error: error as Error,
    });
    handleErrors(
      error as Error,
      res,
      `listing the ${JSON.stringify(req.params)}`,
    );
  }
};

router.get(
  '/',
  verifyToken([DrawingPlanApprovalPermissions.VIEW]),
  validatorMiddleware('getDrawingListDto'),
  getAllDrawingLists,
);

router.post(
  '/',
  verifyToken([DrawingPlanApprovalPermissions.CREATE]),
  validatorMiddleware('crateDrawingListDto'),
  saveDrawings,
);

router.get(
  '/:id',
  verifyToken([DrawingPlanApprovalPermissions.VIEW]),
  getDrawingListById,
);

router.delete(
  '/:id',
  verifyToken([DrawingPlanApprovalPermissions.DELETE]),
  deleteDrawingListById,
);

router.get(
  '/options/:key',
  verifyToken([DrawingPlanApprovalPermissions.VIEW]),
  getDataForUniqueKey,
);

export default router;
