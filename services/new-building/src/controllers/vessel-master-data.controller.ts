import {Router, Response} from 'express';
import {verifyToken} from '../middleware';
import {AuthRequest, User} from '../types';
import {vesselMasterDataService} from '../services';
import {logDebug, logError, logInfo} from '../utils';

const router = Router();
/**
 * @swagger
 * /api/vessel/master-data:
 *   get:
 *     summary: Fetch vessel master data
 *     tags:
 *       - Paris2
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully fetched vessel master data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     miscCurrencys:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             description: Timestamp in milliseconds
 *                             example: "1468420410104"
 *                     owners:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             example: "1627621439672"
 *                     vesselTypes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             example: "1395056825000"
 *                     miscEngines:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             example: "1614704132794"
 *                     flags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             example: "1616151213225"
 *                     vesselClasss:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           value:
 *                             type: string
 *                           updated_at:
 *                             type: string
 *                             example: "1627621439672"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *                   example: "Internal server error"
 */
router.get(
  '/master-data',
  verifyToken(),
  async (req: AuthRequest, res: Response) => {
    const user = (req as AuthRequest & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for VesselMasterData Controller VesselMasterData Action retrieving vessel data`,
        method: 'GET',
      });
      const result = await vesselMasterDataService.getVesselMasterData();
      logDebug(user, {
        message: `Execution Completed for VesselMasterData Controller VesselMasterData Action retrieving vessel data`,
        method: 'GET',
        response: result,
      });
      res.status(200).json(result);
    } catch (error) {
      logError(user, {
        message:
          `Error received in VesselMasterData Controller when retrieving vessel data ` +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      res.status(500).json({message: 'Internal server error'});
    }
  },
);

/**
 * @swagger
 * /api/vessel/user-details:
 *   get:
 *     summary: Fetch user details by user IDs
 *     tags:
 *       - Paris2
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: Repeated query parameter for user IDs to fetch details for
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             format: uuid
 *           example: ["f47ac10b-58cc-4372-a567-0e02b2c3d479", "f47ac10b-58cc-4372-a567-0e02b2c3d480"]
 *         style: form
 *         explode: true
 *     responses:
 *       200:
 *         description: Successfully fetched user details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                         description: User ID
 *                         example: "70f823a2-92cc-441f-ba6b-50349ba83176"
 *                       username:
 *                         type: string
 *                         description: Username
 *                         example: "<EMAIL>"
 *                       first_name:
 *                         type: string
 *                         description: First name of the user
 *                         example: "Arun"
 *                       last_name:
 *                         type: string
 *                         description: Last name of the user
 *                         example: "Sharma"
 *                       full_name:
 *                         type: string
 *                         description: Full name of the user
 *                         example: "Arun Sharma"
 *                       email:
 *                         type: string
 *                         description: User email
 *                         example: "<EMAIL>"
 *                       attributes:
 *                         type: object
 *                         description: Additional user attributes
 *                         example: {}
 *       400:
 *         description: Invalid request (e.g., missing or invalid userId query params)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *                   example: "Invalid or missing userId parameter(s)"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *                   example: "Internal server error"
 */
router.get(
  '/user-details',
  verifyToken(),
  async (req: AuthRequest, res: Response) => {
    const user = (req as AuthRequest & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for VesselMasterData Controller VesselMasterData Action retrieving user details`,
        method: 'GET',
      });
      const userIds = req.query.userId;
      const userIdArray = Array.isArray(userIds) ? userIds : [userIds];
      const userDetails = await vesselMasterDataService.getUserDetails(
        userIdArray as string[],
      );

      logDebug(user, {
        message: `Execution Started for VesselMasterData Controller VesselMasterData Action retrieving user details`,
        method: 'GET',
        response: userDetails,
      });

      res.status(200).json(userDetails);
    } catch (error) {
      logError(user, {
        message:
          `Error received in VesselMasterData Controller when retrieving user details ` +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      res.status(500).json({message: 'Internal server error'});
    }
  },
);

export default router;
