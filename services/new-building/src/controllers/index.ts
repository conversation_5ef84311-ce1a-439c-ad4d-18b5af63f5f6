import ProjectsController from './projects.controller';
import AssetsController from './assets.controller';
import FuelTypeController from './fuel.type.controller';
import ProjectTypeController from './project.type.controller';
import HullController from './hulls.controller';
import <PERSON>rons<PERSON>ontroller from './crons.controller';
import DrawingsController from './drawings-list.controller';
import ProjectDrawingsController from './project-drawings.controller';
import CommentsController from './comments.controller';
import InspectionController from './inspection.controller';
import ObservationsController from './observations.controller';
import VesselMasterDataController from './vessel-master-data.controller';

export {
  ProjectsController,
  FuelTypeController,
  ProjectTypeController,
  AssetsController,
  HullController,
  CronsController,
  DrawingsController,
  ProjectDrawingsController,
  CommentsController,
  InspectionController,
  ObservationsController,
  VesselMasterDataController,
  // Add other controllers here as needed
};
