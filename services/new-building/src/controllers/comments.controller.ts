import {Router, Request, Response} from 'express';
import {verifyToken} from '../middleware';
import {
  createComment,
  getAllComments,
  resolveAllComments,
  updateComment,
} from '../services/comments.services';
import {ICommentQuery, User} from '../types';
import {
  handleErrors,
  logInfo,
  logDebug,
  logError,
  getRequestContext,
} from '../utils';
import Validator from '../middleware/validation.middleware';
import {CommentsResolvePermissions, DrawingCommentsPermissions} from '../enums';
const router = Router();

/**
 * @swagger
 * /api/comments:
 *   post:
 *     summary: Create a new comment
 *     tags:
 *       - Comments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentType:
 *                 type: number
 *                 enum: [1]
 *                 description: Type of the comment (1 = Drawing)
 *                 example: 1
 *               drawingListId:
 *                 type: number
 *                 description: ID of the referenced drawing (required for drawing comments)
 *                 example: 123
 *               comments:
 *                 type: string
 *                 description: The content of the comment
 *                 example: "This is a sample comment"
 *               commentId:
 *                 type: string
 *                 description: Optional parent comment ID for replies
 *                 example: "abc123"
 *             required:
 *               - commentType
 *               - drawingListId
 *               - comments
 *     responses:
 *       201:
 *         description: Comment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Comment created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Comment'
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  '/',
  verifyToken([DrawingCommentsPermissions.CREATE]),
  Validator('createCommentDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Comments Controller Comment Action add a comment',
        method: 'POST',
        body: req.body,
      });

      const context = getRequestContext();
      const response = await createComment(req.body, user, context);

      const result = {message: 'Comment created successfully', data: response};

      logDebug(user, {
        message:
          'Execution Completed for Comments Controller Comment Action add a comment',
        method: 'POST',
        response: result,
      });

      res.status(201).json(result);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Comments Controller when add a comment ` +
          (error as Error).message,
        method: 'POST',
        error: error as Error,
      });

      handleErrors(error as Error, res, 'adding a comment');
    }
  },
);

/**
 * @swagger
 * /api/comments:
 *   get:
 *     summary: Get all comments by type and reference ID
 *     tags:
 *       - Comments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: commentType
 *         required: true
 *         schema:
 *           type: integer
 *           enum: [1, 2]
 *         description: Type of the comment (1 = Drawing, 2 = Inspection)
 *       - in: query
 *         name: drawingListId
 *         required: false
 *         schema:
 *           type: integer
 *         description: ID of the referenced drawing (required for drawing comments)
 *       - in: query
 *         name: inspectionId
 *         required: false
 *         schema:
 *           type: integer
 *         description: ID of the referenced inspection (required for inspection comments)
 *       - in: query
 *         name: sortBy
 *         required: false
 *         schema:
 *           type: string
 *         description: Field to sort by (e.g., createdAt)
 *       - in: query
 *         name: sortOrder
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         required: false
 *         schema:
 *           type: integer
 *           enum: [1, 2]
 *         description: Filter comments by status (1 = Open, 2 = Closed)
 *     responses:
 *       200:
 *         description: List of comments matching the criteria
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Comment'
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get(
  '/',
  verifyToken([DrawingCommentsPermissions.VIEW]),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Comments Controller Comment Action retrieving comments',
        method: 'GET',
        query: req.query,
      });
      const query = req.query as unknown as ICommentQuery;
      const context = getRequestContext();
      const response = await getAllComments(query, context);

      logDebug(user, {
        message:
          'Execution Completed for Comments Controller Comment Action retrieving comments',
        method: 'GET',
        response,
      });

      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Comments Controller when retrieving comments ` +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'listing comments');
    }
  },
);

/**
 * @swagger
 * /api/comments/resolve-all:
 *   put:
 *     summary: Resolve all comments for a specific reference
 *     tags:
 *       - Comments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: entityId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the referenced entity (e.g., drawing or inspection)
 *       - in: query
 *         name: commentType
 *         required: true
 *         schema:
 *           type: integer
 *           enum: [1, 2]
 *         description: Type of the reference (1 = Drawing, 2 = Inspection)
 *     responses:
 *       200:
 *         description: All comments resolved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Comment has been resolved successfully"
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Reference not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Reference not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.put(
  '/resolve-all',
  verifyToken([
    CommentsResolvePermissions.COMMENT_All,
    CommentsResolvePermissions.COMMENT_SELF,
  ]),
  Validator('resolveAllCommentDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Comments Controller Comment Action resolve-all comment',
        method: 'PUT',
        query: req.query,
      });
      const {entityId, commentType} = req.query;
      const context = getRequestContext();
      const response = await resolveAllComments(
        Number(entityId),
        Number(commentType),
        user,
        context,
      );

      logDebug(user, {
        message:
          'Execution Completed for Comments Controller Comment Action resolve-all comment',
        method: 'PUT',
        response: response,
      });

      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Comments Controller when resolve-all comment ` +
          (error as Error).message,
        method: 'PUT',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'closing comments');
    }
  },
);

/**
 * @swagger
 * /api/comments/{id}:
 *   put:
 *     summary: Update a comment's text or status
 *     tags:
 *       - Comments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the comment to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentType:
 *                 type: number
 *                 enum: [1, 2]
 *                 description: Type of the comment (1 = Drawing, 2 = Inspection)
 *                 example: 1
 *               status:
 *                 type: number
 *                 enum: [2]
 *                 description: Status of the comment (2 = Closed)
 *                 example: 2
 *             required:
 *               - commentType
 *     responses:
 *       200:
 *         description: Comment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Comment updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Comment'
 *       400:
 *         description: Invalid request payload
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The basic validation failed for the action performed"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Comment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Comment not found"
 *       409:
 *         description: Comment already closed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Comment already closed"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.put(
  '/:id',
  verifyToken([
    CommentsResolvePermissions.COMMENT_SELF,
    CommentsResolvePermissions.COMMENT_All,
  ]),
  Validator('updateCommentDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Comments Controller Comment Action edit a comment',
        method: 'PUT',
        body: req.body,
        params: req.params,
      });
      const {id} = req.params;
      const context = getRequestContext();
      const response = await updateComment(Number(id), req.body, user, context);
      const result = {message: 'Comment updated successfully', data: response};
      logDebug(user, {
        message:
          'Execution Completed for Comments Controller Comment Action edit comment',
        method: 'PUT',
        response: result,
      });
      res.status(200).json(result);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Comments Controller when edit a comment ` +
          (error as Error).message,
        method: 'PUT',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'updating comments');
    }
  },
);

export default router;
