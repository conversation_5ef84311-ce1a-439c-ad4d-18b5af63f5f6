import {Request, Response, Router} from 'express';
import {S3HelperService, modifyExistingAsset} from '../services';
import {getRequestContext, logDebug, logError, logInfo} from '../utils';
import {verifyToken} from '../middleware';
import ErrorMessages from '../utils/error.messages.utils';
import {User} from '../types';

const router = Router();

/**
 * @swagger
 * /api/assets/presigned-upload-url:
 *   post:
 *     summary: Generate a presigned URL for file upload
 *     description: Returns a presigned URL that can be used to upload a file directly to S3.
 *     tags:
 *       - Assets
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileName:
 *                 type: string
 *                 description: The name of the file to be uploaded.
 *                 example: example.txt
 *               contentType:
 *                 type: string
 *                 description: The MIME type of the file.
 *                 example: text/plain
 *     responses:
 *       201:
 *         description: Presigned URL generated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   example: https://paris2-new-building-dev2.s3.ap-southeast-1.amazonaws.com/
 *                 fields:
 *                   type: object
 *                   properties:
 *                     acl:
 *                       type: string
 *                       example: private
 *                     Content-Type:
 *                       type: string
 *                       example: text/plain
 *                     bucket:
 *                       type: string
 *                       example: paris2-new-building-dev2
 *                     X-Amz-Algorithm:
 *                       type: string
 *                       example: AWS4-HMAC-SHA256
 *                     X-Amz-Credential:
 *                       type: string
 *                       example: <AWS_ACCESS_KEY_ID>/20250530/<REGION>/s3/aws4_request
 *                     X-Amz-Date:
 *                       type: string
 *                       example: 20250530T155213Z
 *                     key:
 *                       type: string
 *                       example: uploads/5205b193-1472-4b92-9b4a-8263f4dc0cc2.txt
 *                     Policy:
 *                       type: string
 *                       example: eyJleHBpcmF0aW9uIjoi...
 *                     X-Amz-Signature:
 *                       type: string
 *                       example: dd9967899ac3402c296dd53f5d61e52933414134799b49f8490348b8444af8a7
 *                 fileKey:
 *                   type: string
 *                   example: uploads/5205b193-1472-4b92-9b4a-8263f4dc0cc2.txt
 *                 bucketName:
 *                   type: string
 *                   example: paris2-new-building-dev2
 *                 expiresIn:
 *                   type: integer
 *                   example: 900
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to generate presigned URL.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to generate presigned URL.
 */
const getPresignedUrl = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message:
        'Execution Started for Assets Controller Assets Action generate Presigned URl',
      method: 'POST',
      body: req.body,
    });
    const {fileName, contentType} = req.body;
    const result = await S3HelperService.generatePresignedPost({
      fileName,
      contentType,
    });
    logDebug(user, {
      message:
        'Execution Completed for Assets Controller Assets Action generate Presigned URl',
      method: 'POST',
      response: result,
    });
    res.status(201).json(result);
  } catch (error) {
    logError(user, {
      message:
        'Error received in Assets Controller when generate Presigned URl ' +
        (error as Error).message,
      method: 'POST',
      error: error as Error,
    });
    res
      .status(500)
      .json({error: ErrorMessages.failed('generate presigned URL')});
  }
};

/**
 * @swagger
 * /api/assets/{type}/{id}:
 *   put:
 *     summary: Update an existing asset
 *     tags: [Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [project, hull]
 *         description: The type of asset to update (project or hull)
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The asset ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               assetName:
 *                 type: string
 *                 example: "Updated Asset Name"
 *               assetPath:
 *                 type: string
 *                 example: "updated/path/to/asset"
 *               assetType:
 *                 type: string
 *                 example: "image/png"
 *     responses:
 *       200:
 *         description: Asset updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Asset updated successfully
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Asset not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Asset not found"
 *       500:
 *         description: Error updating asset
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed while updating asset"
 */

const updateAssetData = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Assets Controller Assets Action edit Asset for Id ${req.params.id}`,
      method: 'PUT',
      body: req.body,
      param: req.params,
    });
    const {id, type} = req.params;
    const context = getRequestContext();

    const updatedAsset = await modifyExistingAsset(
      type,
      Number(id),
      req.body,
      context,
    );
    logDebug(user, {
      message: `Execution Completed for Assets Controller Assets Action edit Asset for Id ${req.params.id}`,
      method: 'PUT',
      response: updatedAsset,
    });
    res.status(200).json(updatedAsset);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Assets Controller when edit Asset for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'PUT',
      error: error as Error,
    });
    res.status(500).json({error: ErrorMessages.failed('updating asset')});
  }
};
router.post('/presigned-upload-url', verifyToken(), getPresignedUrl);
router.put('/:type/:id', verifyToken(), updateAssetData);
export default router;
