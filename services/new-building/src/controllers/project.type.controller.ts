import {Router, Request, Response} from 'express';
import {getAllProjectTypes} from '../services';
import {verifyToken} from '../middleware';
import {getRequestContext, logDebug, logError, logInfo} from '../utils';
import ErrorMessages from '../utils/error.messages.utils';
import {User} from '../types';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     ProjectType:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: Ship Contruction
 *         status:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2025-04-08T06:51:57.905Z"
 *         createdBy:
 *           type: string
 *           example: admin
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         updatedBy:
 *           type: string
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 *         deletedBy:
 *           type: string
 *           nullable: true
 *           example: 2025-04-08T06:51:57.905Z
 */

/**
 * @swagger
 * /api/project-type:
 *   get:
 *     summary: Retrieve all Project types
 *     tags: [Project Type]
 *     responses:
 *       200:
 *         description: A list of Project types
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ProjectType'
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Some server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error retrieving Project types
 *                 error:
 *                   type: object
 *                   description: Detailed error information
 */
router.get('/', verifyToken(), async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for ProjectType Controller ProjectType Action retrieving project type`,
      method: 'GET',
    });
    const context = getRequestContext();
    const projectType = await getAllProjectTypes(context);
    logDebug(user, {
      message: `Execution Completed for ProjectType Controller ProjectType Action retrieving project type`,
      method: 'GET',
      response: projectType,
    });
    res.status(200).json(projectType);
  } catch (error) {
    logError(user, {
      message:
        `Error received in ProjectType Controller when retrieving project type ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    res.status(500).json({error: ErrorMessages.errorFetching('Project types')});
  }
});

export default router;
