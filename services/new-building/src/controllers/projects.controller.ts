import {Router, Request, Response} from 'express';
import {
  createProject,
  getAllProjects,
  findByIdProject,
  updateProject,
  deleteProject,
  countProjectsByStatus,
  completeProject,
} from '../services';
import {validatorMiddleware, verifyToken} from '../middleware';
import Validator from '../middleware/validation.middleware';
import {AuthRequest, IFindAllQuery} from '../types';
import {
  getRequestContext,
  handleErrors,
  logDebug,
  logError,
  logger,
  logInfo,
} from '../utils';
import ErrorMessages from '../utils/error.messages.utils';
import {ProjectPermissions} from '../enums';
import {User} from '../types/user.types';
const router = Router();

/**
 * @swagger
 * tags:
 *   name: Projects
 *   description: Project management API
 */
/**
 * @swagger
 * components:
 *   schemas:
 *     Project:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         owner:
 *           type: string
 *         shipPartyId:
 *           type: integer
 *         shipType:
 *           type: string
 *         fuelTypeId:
 *           type: integer
 *         engineType:
 *           type: string
 *         projectTypeId:
 *           type: integer
 *         projectDescription:
 *           type: string
 *         totalTimeTaken:
 *           type: integer
 *         contractBudget:
 *           type: integer
 *         expenses:
 *           type: integer
 *         currency:
 *           type: string
 *         completionDate:
 *           type: string
 *           format: date-time
 *         status:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         createdBy:
 *           type: string
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         updatedBy:
 *           type: string
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         deletedBy:
 *           type: string
 */
/**
 * @swagger
 * /api/projects:
 *   post:
 *     summary: Create a new project with optional hull information
 *     tags:
 *       - Projects
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - owner
 *               - shipType
 *               - fuelTypeId
 *               - engineType
 *               - projectTypeId
 *               - projectDescription
 *               - status
 *             properties:
 *               name:
 *                 type: string
 *                 example: Text Tile Shipment
 *               owner:
 *                 type: string
 *                 example: AKS
 *               shipType:
 *                 type: string
 *                 example: "Cargo"
 *               isShipTypeCustom:
 *                 type: boolean
 *                 description: Indicates if the ship type is custom provided by user
 *                 example: false
 *               fuelTypeId:
 *                 type: integer
 *                 example: 1
 *               engineType:
 *                 type: string
 *                 example: "Diesel"
 *               isEngineTypeCustom:
 *                 type: boolean
 *                 description: Indicates if the engine type is custom provided by user
 *                 example: false
 *               projectTypeId:
 *                 type: integer
 *                 example: 1
 *               projectDescription:
 *                 type: string
 *                 example: Cargo for Text tile shipment
 *               status:
 *                 type: integer
 *                 enum: [1, 2]
 *                 example: 1
 *               currency:
 *                 type: string
 *                 example: INR
 *               contractBudget:
 *                 type: number
 *                 example: 4000000
 *               expenses:
 *                 type: number
 *                 example: 3500000
 *               totalTimeTaken:
 *                 type: number
 *                 example: 0
 *               assetName:
 *                 type: string
 *                 example: ship1.jpeg
 *               assetType:
 *                 type: string
 *                 example: image/jpeg
 *               assetPath:
 *                 type: string
 *                 example: f2a6113c-5684-41e0-97b2-0434d90c543c.jpeg
 *               hulls:
 *                 type: array
 *                 description: Optional list of hulls
 *                 items:
 *                   type: object
 *                   required:
 *                     - hullNo
 *                     - flag
 *                     - deadWeightGT
 *                     - steelCuttingDate
 *                     - keelLaidDate
 *                     - launchDate
 *                     - seaTrialDate
 *                     - deliveryDate
 *                     - vesselClasses
 *                     - capacity
 *                     - assetName
 *                     - assetType
 *                     - assetPath
 *                   properties:
 *                     hullNo:
 *                       type: string
 *                       example: HUL-1000003
 *                     flag:
 *                       type: string
 *                       example: Hong Kong
 *                     deadWeightGT:
 *                       type: number
 *                       example: 50000
 *                     deadWeightNT:
 *                       type: number
 *                       example: 15000
 *                     steelCuttingDate:
 *                       type: string
 *                       format: date
 *                       example: 2025-05-31
 *                     keelLaidDate:
 *                       type: string
 *                       format: date
 *                       example: 2025-05-31
 *                     launchDate:
 *                       type: string
 *                       format: date
 *                       example: 2025-05-31
 *                     seaTrialDate:
 *                       type: string
 *                       format: date
 *                       example: 2025-05-31
 *                     deliveryDate:
 *                       type: string
 *                       format: date
 *                       example: 2025-05-31
 *                     vesselClasses:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: [ "BV" ]
 *                     shipName:
 *                       type: string
 *                       example: Indiana
 *                     imo:
 *                       type: integer
 *                       example: 1122334
 *                     capacity:
 *                       type: number
 *                       example: 1234
 *                     assetName:
 *                       type: string
 *                       example: ship1.jpeg
 *                     assetType:
 *                       type: string
 *                       example: image/jpeg
 *                     assetPath:
 *                       type: string
 *                       example: f104ac07-0450-401a-9b4b-752043d2d4d9.jpeg
 *     responses:
 *       201:
 *         description: Project created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 11
 *                 name:
 *                   type: string
 *                   example: Text Tile Shipment
 *                 engineType:
 *                   type: string
 *                   example: HYUNDAI-MAN B&W 6G50ME-C9.6-LGIM (Tier II)
 *                 projectTypeId:
 *                   type: integer
 *                   example: 1
 *                 shipType:
 *                   type: string
 *                   example: General Cargo
 *                 fuelTypeId:
 *                   type: integer
 *                   example: 1
 *                 owner:
 *                   type: string
 *                   example: AKS
 *                 status:
 *                   type: integer
 *                   example: 1
 *                 currency:
 *                   type: string
 *                   example: INR
 *                 contractBudget:
 *                   type: string
 *                   example: "4000000"
 *                 expenses:
 *                   type: string
 *                   example: "3500000"
 *                 projectDescription:
 *                   type: string
 *                   example: Cargo for Text tile shipment
 *                 totalTimeTaken:
 *                   type: string
 *                   example: "0"
 *                 assetName:
 *                   type: string
 *                   example: 1/projects/ship1.jpeg
 *                 assetType:
 *                   type: string
 *                   example: image/jpeg
 *                 assetPath:
 *                   type: string
 *                   example: 1/projects/f2a6113c-5684-41e0-97b2-0434d90c543c.jpeg
 *                 tenantId:
 *                   type: integer
 *                   example: 1
 *                 createdBy:
 *                   type: string
 *                   example: 19544d8c-0c30-4a73-bf58-9b40a1132882
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-05-30T13:14:34.434Z
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   example: 2025-05-30T13:14:34.434Z
 *                 completionDate:
 *                   type: string
 *                   format: date-time
 *                   nullable: true
 *                   example: null
 *                 updatedBy:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 deletedAt:
 *                   type: string
 *                   format: date-time
 *                   nullable: true
 *                   example: null
 *                 deletedBy:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Bad request
 *       403:
 *         description: Invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.post(
  '/',
  verifyToken([ProjectPermissions.CREATE]),
  validatorMiddleware('createProjectDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Project Controller Project Action create a project',
        method: 'POST',
        body: req.body,
      });

      const projectData = {
        ...req.body,
        tenantId: user?.['tenantId'],
        createdBy: user?.user_id,
        ...(req.body.hulls && {
          hulls: req.body.hulls.map((hull: Record<string, any>) => ({
            ...hull,
            tenantId: user?.['tenantId'],
            createdBy: user?.user_id,
          })),
        }),
      };

      const context = getRequestContext();
      const project = await createProject(projectData, user, context);
      logDebug(user, {
        message:
          'Execution Completed for Project Controller Project Action create a project',
        method: 'POST',
        response: project,
      });
      res.status(201).json(project);
    } catch (error) {
      logError(user, {
        message:
          'Error received in Project Controller when create project ' +
          (error as Error).message,
        method: 'POST',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'creating project');
    }
  },
);
/**
 * @swagger
 * /api/projects/count:
 *   get:
 *     summary: Get the count of projects by status
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Count of projects by status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 onGoing:
 *                   type: integer
 *                   example: 10
 *                 closed:
 *                   type: integer
 *                   example: 5
 *       400:
 *         description: Bad request/Invalid field provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid field provided
 *       403:
 *         description: Invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Error retrieving Project counts
 */

router.get(
  '/count',
  verifyToken([ProjectPermissions.VIEW]),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Project Controller Project Action retrieving Project count',
        method: 'GET',
      });
      const context = getRequestContext();
      const counts = await countProjectsByStatus(context);
      logDebug(user, {
        message:
          'Execution Completed for Project Controller Project Action retrieving Project count',
        method: 'GET',
        response: counts,
      });
      res.status(200).json(counts);
    } catch (error) {
      logError(user, {
        message:
          'Error received in Project Controller when retrieving Project count ' +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      res
        .status(500)
        .json({error: ErrorMessages.errorFetching('Project counts')});
    }
  },
);

/**
 * @swagger
 * /api/projects:
 *   get:
 *     summary: Get a list of projects
 *     description: Retrieve projects with selected fields and pagination
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: fields
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *         description: Fields to include in the response (repeatable, e.g., ?fields=name&fields=owner)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Project Status (1 for ON_GOING, 2 for CLOSED)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: Page number (starts from 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: A list of projects
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Bad request
 *                 details:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       message:
 *                         type: string
 *                         example: "specific field is required"
 *       403:
 *         description: Invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Error retrieving projects for selected filter
 */
router.get(
  '/',
  verifyToken([ProjectPermissions.VIEW]),
  Validator('getProjectDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message:
          'Execution Started for Project Controller Project Action retrieving Projects',
        method: 'GET',
        query: req.query,
      });
      const query = req.query;
      const context = getRequestContext();
      const response = await getAllProjects(query as IFindAllQuery, context);
      logDebug(user, {
        message:
          'Execution Completed for Project Controller Project Action retrieving Projects',
        method: 'GET',
        response: response,
      });
      res.status(200).json(response);
    } catch (error) {
      logError(user, {
        message:
          'Error received in Project Controller when retrieving Projects ' +
          (error as Error).message,
        method: 'GET',
        error: error as Error,
      });
      res.status(500).json({
        error: ErrorMessages.errorFetching('projects for selected filter'),
      });
    }
  },
);
/**
 * @swagger
 * /api/projects/{id}:
 *   get:
 *     summary: Retrieve a project by ID
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *           format: number
 *         required: true
 *         description: The project ID
 *     responses:
 *       200:
 *         description: The project data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 id: 8
 *                 tenantId: 1
 *                 name: Second Shipment
 *                 owner: Arun
 *                 shipType: Car Carrier
 *                 fuelTypeId: 1
 *                 engineType: B&W 5S60MC-C
 *                 projectTypeId: 1
 *                 projectDescription: Second shipment to Australia
 *                 totalTimeTaken: "0"
 *                 contractBudget: "*********"
 *                 expenses: "*********"
 *                 currency: INR
 *                 completionDate: null
 *                 status: 1
 *                 assetName: 1/projects/ship1.jpeg
 *                 assetType: image/jpeg
 *                 assetPath: path/to/file.pdf
 *                 createdBy: 70f823a2-92cc-441f-ba6b-50349ba83176
 *                 updatedBy: null
 *                 deletedAt: null
 *                 deletedBy: null
 *                 createdAt: 2025-05-30T07:28:55.563Z
 *                 updatedAt: 2025-05-30T07:28:55.563Z
 *                 fuelType:
 *                   id: 1
 *                   name: LNG
 *                   status: 1
 *                 projectType:
 *                   id: 1
 *                   name: New Building
 *                   status: 1
 *                 hulls:
 *                   - id: 5
 *                     hullNo: HUL-100002
 *                     shipName: Indiana Jones
 *                     imo: null
 *                     flag: Commonwealth of Bahamas
 *                     deadWeightGT: "50050"
 *                     deadWeightNT: null
 *                     steelCuttingDate: 2025-05-30
 *                     keelLaidDate: 2025-05-30
 *                     launchDate: 2025-05-31
 *                     capacity: "10"
 *                     seaTrialDate: 2025-05-31
 *                     deliveryDate: 2025-05-31
 *                     status: 1
 *                     assetName: 1/projects/ship1.jpeg
 *                     assetType: image/jpeg
 *                     assetPath: path/to/file.pdf
 *                     hullClass:
 *                       - id: 8
 *                         vesselClass: BV
 *                         hullId: 5
 *                       - id: 9
 *                         vesselClass: LR
 *                         hullId: 5
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Bad request
 *                 details:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       message:
 *                         type: string
 *                         example: "specific field is required"
 *       403:
 *         description: Invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.get(
  '/:id',
  verifyToken([ProjectPermissions.VIEW]),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Project Controller Project Action retrieving Project for Id ${req.params.id}`,
        method: 'GET',
        params: req.params,
      });
      const context = getRequestContext();
      const project = await findByIdProject(
        Number(req.params.id),
        user,
        context,
      );
      logDebug(user, {
        message: `Execution Completed for Project Controller Project Action retrieving Project for Id ${req.params.id}`,
        method: 'GET',
        response: project,
      });
      res.status(200).json(project);
    } catch (err) {
      logError(user, {
        message:
          `Error received in Project Controller when retrieving Project for Id ${req.params.id} ` +
          (err as Error).message,
        method: 'GET',
        error: err as Error,
      });

      handleErrors(err as Error, res, 'listing a specific project');
    }
  },
);

/**
 * @swagger
 * /api/projects/{id}:
 *   put:
 *     summary: Update an existing project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Updated Project Name"
 *               shipType:
 *                 type: string
 *                 example: "Cargo"
 *               isShipTypeCustom:
 *                 type: boolean
 *                 description: Indicates if the ship type is custom provided by user
 *                 example: false
 *               fuelTypeId:
 *                 type: integer
 *                 example: 3
 *               engineType:
 *                 type: string
 *                 example: "Diesel"
 *               isEngineTypeCustom:
 *                 type: boolean
 *                 description: Indicates if the engine type is custom provided by user
 *                 example: false
 *               projectTypeId:
 *                 type: integer
 *                 example: 1
 *               projectDescription:
 *                 type: string
 *                 example: "Updated project description."
 *               status:
 *                 type: integer
 *                 enum: [1, 2]
 *                 example: 1
 *               owner:
 *                 type: string
 *                 example: "Updated Owner Name"
 *               contractBudget:
 *                 type: integer
 *                 example: 10000
 *               expenses:
 *                 type: integer
 *                 example: 1000
 *     responses:
 *       200:
 *         description: Project updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 id: 4
 *                 tenantId: 1
 *                 name: Sunrise Shipyard - Vessel Alpha
 *                 owner: Marine Logistics Ltd.
 *                 shipPartyId: null
 *                 shipType: Car Carrier
 *                 fuelTypeId: 3
 *                 engineType: B&W
 *                 projectTypeId: 1
 *                 projectDescription: Refitting of bulk carrier with hybrid propulsion system.
 *                 totalTimeTaken: null
 *                 contractBudget: "10000"
 *                 expenses: "1000"
 *                 currency: null
 *                 completionDate: null
 *                 status: 1
 *                 assetName: File Name
 *                 assetType: image/pdf
 *                 assetPath: path/to/file.pdf
 *                 createdBy: 70f823a2-92cc-441f-ba6b-50349ba83176
 *                 updatedBy: 19544d8c-0c30-4a73-bf58-9b40a1132882
 *                 deletedAt: null
 *                 deletedBy: null
 *                 createdAt: 2025-05-30T02:36:46.403Z
 *                 updatedAt: 2025-05-30T09:20:00.276Z
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Something went wrong
 *       403:
 *         description: Invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Project not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: projects not found for the given id
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Failed to updating project
 */
router.put(
  '/:id',
  verifyToken([ProjectPermissions.EDIT]),
  Validator('updateProjectDto'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      const {id} = req.params;
      logInfo(user, {
        message: `Execution Started for Project Controller Project Action edit Project for Id ${req.params.id}`,
        method: 'PUT',
        params: req.params,
        body: req.body,
      });

      const projectData = {
        ...req.body,
        updatedBy: user?.user_id,
      };
      const context = getRequestContext();
      const updatedProject = await updateProject(
        Number(id),
        projectData,
        user,
        context,
      );
      if (!updatedProject) {
        logger.error(`[ProjectsController] Project with ID ${id} not found`);
        res.status(404).json({error: ErrorMessages.notFound('projects')});
        return;
      }
      logDebug(user, {
        message: `Execution Completed for Project Controller Project Action edit Project for Id ${req.params.id}`,
        method: 'PUT',
        response: updatedProject,
      });
      res.status(200).json(updatedProject);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Project Controller when edit Project for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'PUT',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'updating the project');
    }
  },
);

/**
 * @swagger
 * /api/projects/{id}/complete:
 *   put:
 *     summary: Complete a project by ID
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               completionDate:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-10-01T00:00:00Z"
 *               expenses:
 *                 type: number
 *                 example: 5000
 *               currency:
 *                 type: string
 *                 example: "INR"
 *     responses:
 *       200:
 *         description: Project marked as completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Updated project object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Bad request
 *       403:
 *         description: Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Project not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Project not found
 *       409:
 *         description: Project already completed/deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Project already completed/deleted
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
router.put(
  '/:id/complete',
  verifyToken([ProjectPermissions.EDIT]),
  Validator('completeProjectValidationSchema'),
  async (req: Request, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Project Controller Project Action complete Project for Id ${req.params.id}`,
        method: 'PUT',
        params: req.params,
        body: req.body,
      });
      const {id} = req.params;
      const projectData = {
        ...req.body,
        updatedBy: user?.user_id,
      };
      const context = getRequestContext();
      const updatedProject = await completeProject(
        Number(id),
        projectData,
        context,
      );
      logDebug(user, {
        message: `Execution Completed for Project Controller Project Action complete Project for Id ${req.params.id}`,
        method: 'PUT',
        response: updatedProject,
      });
      res.status(200).json(updatedProject);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Project Controller when complete Project for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'PUT',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'marking the project as complete');
    }
  },
);

/**
 * @swagger
 * /api/projects/{id}:
 *   delete:
 *     summary: Delete a project by ID
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *           format: number
 *         required: true
 *         description: The project ID
 *     responses:
 *       200:
 *         description: Deleted message response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Project deleted successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Bad request
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Project not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Project not found
 *       409:
 *         description: Project already deleted/completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Project already deleted/completed
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error retrieving project
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */

router.delete(
  '/:id',
  verifyToken([ProjectPermissions.DELETE]),
  async (req: AuthRequest, res: Response) => {
    const user = (req as Request & {user: User}).user;
    try {
      logInfo(user, {
        message: `Execution Started for Project Controller Project Action delete Project for Id ${req.params.id}`,
        method: 'DELETE',
        params: req.params,
      });
      const context = getRequestContext();
      await deleteProject(Number(req.params.id), user, context);
      const result = {message: 'Project deleted successfully'};
      logDebug(user, {
        message: `Execution Completed for Project Controller Project Action delete Project for Id ${req.params.id}`,
        method: 'DELETE',
        response: result,
      });
      res.status(200).json(result);
    } catch (error) {
      logError(user, {
        message:
          `Error received in Project Controller when delete Project for Id ${req.params.id} ` +
          (error as Error).message,
        method: 'DELETE',
        error: error as Error,
      });
      handleErrors(error as Error, res, 'deleting the project');
    }
  },
);

export default router;
