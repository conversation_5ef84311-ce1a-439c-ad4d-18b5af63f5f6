import {
  getRequestContext,
  handleErrors,
  logDebug,
  logError,
  logInfo,
} from '../utils';
import {Request, Response, Router} from 'express';
import {validatorMiddleware, verifyToken} from '../middleware';
import {inspectionsService} from '../services';
import {IFindAllQuery, User} from '../types';
import {InspectionPermissions} from '../enums';

const router = Router();

/**
 * @swagger
 * /api/inspections:
 *   post:
 *     summary: Save inspection from an Excel file
 *     description: Processes an Excel file and saves the inspection data into the database.
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileKey:
 *                 type: string
 *                 description: The unique key/path of the Excel file in S3.
 *                 example: folder/inspection.xlsx
 *               projectId:
 *                 type: number
 *                 description: Project ID.
 *                 example: 1
 *               hullId:
 *                 type: number
 *                 description: Hull ID.
 *                 example: 1
 *     responses:
 *       200:
 *         description: Inspection saved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Inspection saved successfully.
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to save Inspections.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to save drawings.
 */
const createInspection = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action add a inspection`,
      method: 'POST',
      body: req.body,
    });
    const context = getRequestContext();
    await inspectionsService.createInspectionFromExcel(req.body, user, context);
    const result = {error: false, message: 'Inspections saved successfully'};
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action add a inspection`,
      method: 'POST',
      response: result,
    });
    res.status(200).json(result);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when add a inspection ` +
        (error as Error).message,
      method: 'POST',
      error: error as Error,
    });
    handleErrors(
      error as Error,
      res,
      '',
      'The uploaded inspection logs already exist. Please verify and upload the correct data',
    );
  }
};

/**
 * @swagger
 * /api/inspections:
 *   get:
 *     summary: Get all Inspections
 *     description: Fetches a paginated list of inspections based on query parameters, including sorting and filtering options.
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (default is 1).
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page (default is 100).
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, submissionDate, dueDate, time, discipline, description, project.name, hull.hullNo, id]
 *         description: Field to sort the results by (default is "createdAt").
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order for the results (default is "DESC").
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter inspections by description, discipline, project name, or hull number.
 *       - in: query
 *         name: createdAt[startDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering inspections by creation date.
 *       - in: query
 *         name: createdAt[endDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering inspections by creation date.
 *       - in: query
 *         name: dueDate[startDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering inspections by due date.
 *       - in: query
 *         name: dueDate[endDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering inspections by due date.
 *       - in: query
 *         name: submissionDate[startDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering inspections by submission date.
 *       - in: query
 *         name: submissionDate[endDate]
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering inspections by submission date.
 *       - in: query
 *         name: discipline
 *         schema:
 *           type: string
 *         description: Filter inspections by discipline.
 *       - in: query
 *         name: status
 *         schema:
 *           type: number
 *           enum: [1, 2]
 *         description: Filter inspections by status (1 for ONGOING, 2 for CLOSED).
 *       - in: query
 *         name: hullId
 *         schema:
 *           type: integer
 *         description: Filter inspections by hull Id.
 *       - in: query
 *         name: commentStatus
 *         schema:
 *           type: integer
 *           enum: [1, 2]
 *         description: Filter inspections by comment status.
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: integer
 *         description: Filter inspections by project Id.
 *     responses:
 *       200:
 *         description: Inspection lists fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: The ID of the inspection.
 *                         example: 1
 *                       description:
 *                         type: string
 *                         description: The description of the inspection.
 *                         example: "Inspection 1"
 *                       discipline:
 *                         type: string
 *                         description: The discipline of the inspection.
 *                         example: "Mechanical"
 *                       submissionDate:
 *                         type: string
 *                         format: date
 *                         description: The submission date of the inspection.
 *                         example: "2025-05-01"
 *                       dueDate:
 *                         type: string
 *                         format: date
 *                         description: The due date of the inspection.
 *                         example: "2025-05-10"
 *                       time:
 *                         type: string
 *                         format: time
 *                         description: The time of the inspection.
 *                         example: "10:00:00"
 *                       forOwner:
 *                         type: boolean
 *                         description: Indicates if the inspection is for the owner.
 *                         example: true
 *                       forClass:
 *                         type: boolean
 *                         description: Indicates if the inspection is for the class.
 *                         example: false
 *                       remark:
 *                         type: string
 *                         description: Additional remarks for the inspection.
 *                         example: "Test remark"
 *                       status:
 *                         type: integer
 *                         description: The status of the inspection (1 for ONGOING, 2 for CLOSED).
 *                         example: 1
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         description: The creation timestamp of the inspection.
 *                         example: "2025-04-25T10:00:00Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         description: The last update timestamp of the inspection.
 *                         example: "2025-04-26T12:00:00Z"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       description: Total number of inspection records.
 *                       example: 100
 *                     totalPages:
 *                       type: integer
 *                       description: Total number of pages.
 *                       example: 10
 *                     page:
 *                       type: integer
 *                       description: Current page number.
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       description: Number of items per page.
 *                       example: 10
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: There is some error when listing the inspections
  *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to fetch inspection lists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: "Failed to fetch inspection lists."
 */
const getAllInspectionLists = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action retrieving inspections`,
      method: 'GET',
      query: req.query,
    });
    const context = getRequestContext();
    const inspection = await inspectionsService.getAllInspections(
      req.query as IFindAllQuery,
      user,
      context,
    );
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action retrieving inspections`,
      method: 'GET',
      response: inspection,
    });
    res.status(200).json(inspection);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when retrieving inspections ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the inspections');
  }
};

/**
 * @swagger
 * /api/inspections/{id}:
 *   delete:
 *     summary: Delete an Inspection
 *     description: Deletes an inspection by its ID.
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the inspection to delete.
 *         example: 1
 *     responses:
 *       200:
 *         description: Inspection deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *                   example: Inspection deleted successfully.
 *                 result:
 *                   type: object
 *                   description: Details of the deleted inspection.
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: The ID of the deleted inspection.
 *                       example: 1
 *                     description:
 *                       type: string
 *                       description: The description of the deleted inspection.
 *                       example: "Inspection 1"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       404:
 *         description: Inspection not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Inspection not found.
 *       500:
 *         description: Failed to delete the inspection.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to delete the inspection.
 */
const deleteInspection = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action delete inspection for Id ${req.params.id}`,
      method: 'DELETE',
      params: req.params,
    });
    const context = getRequestContext();
    const deletedInspection = await inspectionsService.deleteInspection(
      Number(req.params.id),
      context,
    );
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action delete inspection for Id ${req.params.id}`,
      method: 'DELETE',
      response: deletedInspection,
    });
    res.status(200).json(deletedInspection);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when delete inspection for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'DELETE',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'deleting the inspection');
  }
};

/**
 * @swagger
 * /api/inspections/count:
 *   get:
 *     summary: Get Inspection Count by Status
 *     description: Retrieves the count of inspections grouped by their status (e.g., ONGOING, CLOSED).
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Inspection count retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 ongoing:
 *                   type: integer
 *                   description: The count of inspections with status ONGOING.
 *                   example: 10
 *                 closed:
 *                   type: integer
 *                   description: The count of inspections with status CLOSED.
 *                   example: 5
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to retrieve inspection count.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message.
 *                   example: Failed to retrieve inspection count.
 */
const getInspectionCount = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action retrieving inspection count`,
      method: 'GET',
    });
    const context = getRequestContext();
    const inspection =
      await inspectionsService.countInspectionByStatus(context);
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action retrieving inspection count`,
      method: 'GET',
      response: inspection,
    });
    res.status(200).json(inspection);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when retrieving inspection count ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'getting the counts of inspection');
  }
};

/**
 * @swagger
 * /api/inspections/{id}:
 *   get:
 *     summary: Get Inspection by ID
 *     description: Fetches a specific Inspection by its ID.
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the Inspection to fetch.
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Inspection fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: The ID of the Inspection.
 *                   example: 1
 *                 name:
 *                   type: string
 *                   description: The name of the Inspection.
 *                   example: "Mechanical"
 *                 projectId:
 *                   type: integer
 *                   description: The ID of the associated project.
 *                   example: 101
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error
 */
const getInspectionById = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action retrieving inspection for Id ${req.params.id}`,
      method: 'GET',
      params: req.params,
    });
    const context = getRequestContext();
    const inspection = await inspectionsService.findInspectionById(
      Number(req.params.id),
      user,
      context,
    );
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action retrieving inspection for Id ${req.params.id}`,
      method: 'GET',
      response: inspection,
    });
    res.status(200).json(inspection);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when retrieving inspection for Id ${req.params.id} ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(error as Error, res, 'listing the specific inspection');
  }
};

/**
 * @swagger
 * /api/inspections/options/{key}:
 *   get:
 *     summary: Get unique values for a specific key
 *     description: Fetches all unique values for a given key (attribute) from the Inspection table.
 *     tags:
 *       - Inspections
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Unique values fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unique values for key 'discipline' fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "Mechanical"
 *       400:
 *         description: Invalid key provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: No token provided
 *       403:
 *         description: Forbidden - Invalid token or insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Failed to fetch unique values.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Failed to fetch unique values.
 */
const getDataForUniqueKey = async (req: Request, res: Response) => {
  const user = (req as Request & {user: User}).user;
  try {
    const {key} = req.params;
    logInfo(user, {
      message: `Execution Started for Inspection Controller Inspection Action retrieving data for unique key ${req.params.key}`,
      method: 'GET',
      params: req.params,
    });

    const context = getRequestContext();
    const resultOptionKey = await inspectionsService.getOptionsByKey(
      key,
      context,
    );
    logDebug(user, {
      message: `Execution Completed for Inspection Controller Inspection Action retrieving data for unique key ${req.params.key}`,
      method: 'GET',
      response: resultOptionKey,
    });
    res.status(200).json(resultOptionKey);
  } catch (error) {
    logError(user, {
      message:
        `Error received in Inspection Controller when retrieving data for unique key ${req.params.key} ` +
        (error as Error).message,
      method: 'GET',
      error: error as Error,
    });
    handleErrors(
      error as Error,
      res,
      `listing the ${JSON.stringify(req.params)}`,
    );
  }
};

router.post(
  '/',
  verifyToken([InspectionPermissions.CREATE]),
  validatorMiddleware('createInspectionDto'),
  createInspection,
);

router.get(
  '/',
  verifyToken([InspectionPermissions.VIEW]),
  getAllInspectionLists,
);

router.delete(
  '/:id',
  verifyToken([InspectionPermissions.DELETE]),
  deleteInspection,
);

router.get(
  '/count',
  verifyToken([InspectionPermissions.VIEW]),
  getInspectionCount,
);

router.get(
  '/:id',
  verifyToken([InspectionPermissions.VIEW]),
  getInspectionById,
);

router.get(
  '/options/:key',
  verifyToken([InspectionPermissions.VIEW]),
  getDataForUniqueKey,
);

export default router;
