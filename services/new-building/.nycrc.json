{"extends": "@istanbuljs/nyc-config-typescript", "all": true, "check-coverage": true, "reporter": ["html", "text", "text-summary", "lcov"], "report-dir": "./coverage", "include": ["src/**/*.ts"], "exclude": ["__tests__/**", "**/*.d.ts", "src/models/**", "src/index.ts", "src/routes/**", "src/configs/**", "src/__tests__/**", "src/types/**", "src/enums/**", "src/utils/env-config.utils.ts"], "branches": 0, "lines": 0, "functions": 0, "statements": 0}