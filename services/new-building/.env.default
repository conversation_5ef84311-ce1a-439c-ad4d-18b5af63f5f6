
PORT=
SERVER_URL=
DEBUG=

# Database configuration
DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_NAME=
DB_MIN_CONNECTIONS=
DB_MAX_CONNECTIONS=
# AWS S3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
AWS_S3_BUCKET_NAME=
AWS_S3_SIGNED_URL_EXPIRY=
AWS_S3_LOGS_EXPIRY_DAYS=
NODE_ENV=
# JWT configuration
JWT_PUBLIC_KEY=
JWT_ISSUER=
JWT_SECRET=

### DEV
PARIS_BASE_URL=
PARIS_AUTH_BASE_URL=
OPEN_ID_USERNAME=
OPEN_ID_PASSWORD=
OPEN_ID_CLIENT_SECRET=
OPEN_ID_GRANT_TYPE=
OPEN_ID_CLIENT_ID=
### Logger Environment
LOG_LEVEL=