# Path to sources
sonar.projectKey=paris2-api-new-building
sonar.projectName=paris2-api-new-building
sonar.sources=.
sonar.exclusions=packages/migrations/**,**/dist/**,**/Docker*,webhook.js
#sonar.inclusions=packages/**/src/**

# Path to tests
sonar.tests=.
sonar.test.exclusions=packages/**,**/models/**,**/datasources/**
sonar.test.inclusions=**/src/__tests__/**

sonar.coverage.exclusions=packages/**,**/src/__tests__/**,**/src/configs/**,**/models/**,commitlint.config.js,**/routes/**,services/new-building/src/index.ts,services/new-building/src/utils/env-config.ts
# Source encoding
sonar.sourceEncoding=UTF-8

# Exclusions for copy-paste detection
#sonar.cpd.exclusions=

sonar.typescript.tsconfigPath=.
sonar.javascript.lcov.reportPaths=./services/new-building/coverage/lcov.info
sonar.javascript.junit.reportPaths=./junit.xml