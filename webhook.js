const {
  EventBridgeClient,
  PutEventsCommand,
} = require("@aws-sdk/client-eventbridge");

async function callWebhook() {
  function makeCall(name) {
    console.log("make call", name);
    return new Promise(async (resolve, reject) => {
      const eventBridgeClient = new EventBridgeClient({
        region: process.env.region,
      });

      const eventDetail = { ...process.env };
      console.log({ eventDetail }, "event detail");

      const params = {
        Entries: [
          {
            Source: "saas.tenant.provisioning.jenkins",
            DetailType:
              process.env.JENKINS_JOB_STAGE_STATUS === "0" &&
              process.env.JENKINS_JOB_FINAL_STATUS === "0"
                ? "TENANT_PROVISIONING_SUCCESS"
                : "TENANT_PROVISIONING_FAILURE",
            Detail: JSON.stringify({
              ...eventDetail,
              environment: process.env.ENV ?? process.env.stageName,
            }),
            EventRuleName: process.env.eventRuleName || "default",
            Time: new Date(),
          },
        ],
      };

      try {
        if (
          process.env.triggerWebhook === "true" ||
          process.env.triggerWebhook === true
        ) {
          const command = new PutEventsCommand(params);
          const response = await eventBridgeClient.send(command);
          console.log("Event sent successfully:", response);
        }
      } catch (error) {
        console.error("Failed to send event:", error);
        throw error;
      }
    });
  }

  console.log(process.env.JENKINS_JOB_STAGE_STATUS, "stage status");
  console.log(process.env.JENKINS_JOB_FINAL_STATUS, "final status");
  if (
    process.env.JENKINS_JOB_STAGE_STATUS === "0" &&
    process.env.JENKINS_JOB_FINAL_STATUS === "0"
  ) {
    await makeCall("webhook");
  } else if (process.env.JENKINS_JOB_STAGE_STATUS === "1") {
    await makeCall("webhook");
  } else {
    console.log("No call made.");
  }
}

if (require.main === module) {
  callWebhook().catch((e) => console.log(e));
}

module.exports = { callWebhook };
