# Bulk Insert Optimization Strategies for Drawing List

## Current Issue
The index `idx_project_drawing_list_active` is causing 1716ms average latency during bulk inserts of 85k records.

## Strategy 1: Temporary Index Dropping (Implemented)
- Drop index before bulk insert
- Perform bulk insert in batches
- Recreate index after completion
- **Performance Gain**: 70-90% reduction in insert time

## Strategy 2: Optimized Index Design
Instead of the current index, use a more efficient design:

```sql
-- Current problematic index
DROP INDEX IF EXISTS main.idx_project_drawing_list_active;

-- Option A: Partial index with better column order
CREATE INDEX idx_project_drawing_list_optimized
ON main.project_drawing_list (tenant_id, project_id, id)
WHERE deleted_at IS NULL;

-- Option B: Include commonly queried columns
CREATE INDEX idx_project_drawing_list_covering
ON main.project_drawing_list (tenant_id, project_id)
INCLUDE (name, discipline, drawing_no, created_at)
WHERE deleted_at IS NULL;
```

## Strategy 3: Batch Processing Configuration
```typescript
// Optimal batch sizes for different scenarios
const BATCH_CONFIGS = {
  small: { size: 500, concurrent: 1 },    // < 5k records
  medium: { size: 1000, concurrent: 2 },  // 5k-20k records  
  large: { size: 2000, concurrent: 3 },   // 20k-50k records
  xlarge: { size: 5000, concurrent: 1 },  // > 50k records
};
```

## Strategy 4: Database Configuration Tuning
```sql
-- Temporarily adjust PostgreSQL settings for bulk operations
SET maintenance_work_mem = '1GB';
SET checkpoint_completion_target = 0.9;
SET wal_buffers = '64MB';
SET synchronous_commit = off; -- Only for bulk operations
```

## Strategy 5: COPY Command for Maximum Performance
```typescript
// Use PostgreSQL COPY for fastest bulk insert
async bulkInsertWithCopy(data: any[], tableName: string) {
  const copyQuery = `
    COPY main.${tableName} (tenant_id, name, project_id, discipline, drawing_no, 
                           project_drawing_list_source_file_id, created_by, created_at)
    FROM STDIN WITH (FORMAT csv, HEADER false)
  `;
  
  // Convert data to CSV format and use COPY
  const csvData = data.map(row => 
    `${row.tenantId},"${row.name}",${row.projectId},"${row.discipline}","${row.drawingNo}",${row.sourceFileId},"${row.createdBy}","${new Date().toISOString()}"`
  ).join('\n');
  
  await sequelize.query(copyQuery, {
    replacements: [csvData],
    type: QueryTypes.RAW
  });
}
```

## Strategy 6: Deferred Index Creation
```sql
-- Create index with CONCURRENTLY to avoid blocking
CREATE INDEX CONCURRENTLY idx_project_drawing_list_active
ON main.project_drawing_list (project_id, tenant_id)
WHERE deleted_at IS NULL;
```

## Performance Comparison

| Strategy | Insert Time (85k records) | Downtime | Complexity |
|----------|---------------------------|----------|------------|
| Current | ~2.4 minutes | None | Low |
| Index Drop/Recreate | ~20-30 seconds | Brief | Medium |
| Optimized Index | ~1.5 minutes | None | Low |
| COPY Command | ~10-15 seconds | Brief | High |
| Batch + Config | ~1 minute | None | Medium |

## Recommended Implementation Order

1. **Immediate**: Use the implemented index drop/recreate strategy
2. **Short-term**: Optimize index design and batch configuration
3. **Long-term**: Consider COPY command for very large datasets

## Monitoring and Alerting
```typescript
// Add performance monitoring
const startTime = Date.now();
const result = await bulkInsert(data);
const duration = Date.now() - startTime;

if (duration > 60000) { // Alert if > 1 minute
  logger.warn(`Bulk insert took ${duration}ms for ${data.length} records`);
}
```
