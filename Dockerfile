# Build stage
FROM node:22-alpine AS builder

# Define build argument to specify which service to build
ARG SERVICE_NAME=new-building

WORKDIR /app

# Add build dependencies
RUN apk add --no-cache python3 make g++

# First, copy the root package files for workspace setup
COPY package.json package-lock.json ./

# Copy the shared package (always needed)
COPY services/shared/package.json ./services/shared/

# Copy the target service package files using the build arg
COPY services/${SERVICE_NAME}/package.json ./services/${SERVICE_NAME}/

# Copy the migrations package
COPY packages/migrations ./packages/migrations

# Install all dependencies
RUN npm ci

# Copy TypeScript configs
COPY tsconfig*.json ./
COPY services/shared/tsconfig*.json ./services/shared/
COPY services/${SERVICE_NAME}/tsconfig*.json ./services/${SERVICE_NAME}/

# Copy source code
COPY services/shared/src ./services/shared/src
COPY services/${SERVICE_NAME}/src ./services/${SERVICE_NAME}/src

# Build the shared package first, then the target service
RUN npm run build --workspace=services/shared && \
    npm run build --workspace=services/${SERVICE_NAME} 

# Production stage
FROM node:22-alpine AS production

# Pass the service name to the production stage
ARG SERVICE_NAME=new-building

WORKDIR /app

# Copy package files needed for production
COPY package.json package-lock.json ./
COPY services/shared/package.json ./services/shared/
COPY services/${SERVICE_NAME}/package.json ./services/${SERVICE_NAME}/
COPY services/${SERVICE_NAME}/public ./services/${SERVICE_NAME}/public
COPY packages/migrations ./packages/migrations

# Copy environment files
COPY services/${SERVICE_NAME}/.env* ./services/${SERVICE_NAME}/

# Copy the built files from builder stage
COPY --from=builder /app/services/shared/dist ./services/shared/dist
COPY --from=builder /app/services/${SERVICE_NAME}/dist ./services/${SERVICE_NAME}/dist
# COPY --from=builder /app/packages/migrations/dist ./packages/migrations/dist
# Install only production dependencies
# Added --ignore-scripts flag to skip the prepare script that tries to install husky
ENV NODE_ENV=dev2
RUN npm ci --omit=dev --ignore-scripts

# Create a non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Set ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Start in production mode using the correct path
CMD ["node", "./services/new-building/dist/index.js"]