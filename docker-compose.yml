services:
  new-building:
    build:
      context: .
      dockerfile: Dockerfile.dev
    network_mode: "host"
    ports:
      - "${NEW_BUILDING_PORT}:${NEW_BUILDING_PORT}"
    environment:
      - PORT=${NEW_BUILDING_PORT}
    depends_on:
      - postgres
    volumes:
      - ./:/app
      - /app/node_modules
      - /app/services/new-building/node_modules
      - /app/services/shared/node_modules
    # Use the npm dev script directly
    command: bash -c "cd services/new-building && npm run dev"

  postgres:
    image: postgres:14
    ports:
      - "${DB_PORT}:5432"
    environment:
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME}
    volumes:
      - ${POSTGRES_DATA_PATH}:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
