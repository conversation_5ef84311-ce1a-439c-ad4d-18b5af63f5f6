final ENV_GIT_BRANCH    = env.BRANCH
final ENV_STAGE_NAME    = env.stageName
final ENV_SKIP_TESTS    = env.skipTests
final ENV_SKIP_BUILD    = env.skipBuild
final ENV_SOURCE_BRANCH = env.sourcebranch
final ENV_TARGET_BRANCH = env.targetbranch
final ENV_STATE         = env.state
final ENV_REGION        = env.region
final ENV_GIT_REPO      = env.gitRepo
final ENV_HELM_BRANCH = env.HELMBRANCH
final ENV_HELM_REPO = env.helmRepo
ENV_ACCOUNT_ID    = env.account

final ENV_CONFIG_BRANCH = env.configBranch

def stageName           = ENV_STAGE_NAME
def region              = ENV_REGION
accountId           = ENV_ACCOUNT_ID


def nodeLabel = env.nodeLabel ?: 'fleet-slave-node22'
def dockerImage = env.dockerImage ?: '************.dkr.ecr.ap-southeast-1.amazonaws.com/jenkins-build-agent:node22'
def testCommand = env.testCommand ?: ""
def codeAnalysisCommand = env.codeAnalysisCommand ?: "./code_analysis.sh"
def deployCommand = "ENV=${stageName} REGION=${region} ACCOUNT=${accountId} ./deploy.sh"
def loadEnvCommand = "ENV=${stageName} REGION=${region} ACCOUNT=${accountId} MAKE_ENV_FILE=true ./load-env-vars.sh"
def timeZone = env.timeZone ?: 'Asia/Hong_Kong'
def shouldPullDockerImage = env.shouldPullDockerImage == 'true'
def shouldBuild =  env.shouldSkipBuild != 'true'
def shouldRunCodeAnalysis = env.shouldRunCodeAnalysis == 'true'
def configBranch = env.configBranch ?: 'master'
def releaseVersionId = env.releaseVersionId


def shouldRelease = false
def shouldTest = ENV_SKIP_TESTS != 'true'
echo "ENV_GIT_BRANCH = ${ENV_GIT_BRANCH}"
echo 'pull request info:'
echo "ENV_SOURCE_BRANCH = ${ENV_SOURCE_BRANCH}"
echo "ENV_TARGET_BRANCH = ${ENV_TARGET_BRANCH}"
echo "ENV_STATE = ${ENV_STATE}"
echo "ENV_RELEASE_VERSION_ID = ${releaseVersionId}"

def ssh_private_key = ''

// Test action condition
if (ENV_SOURCE_BRANCH == 'develop' && ENV_TARGET_BRANCH == 'master') {
  shouldTest = false
}

echo "shouldTest = ${shouldTest}"

// Release action condition
if (stageName) {
  echo "Triggered to release to stage: ${stageName}"
  shouldRelease = true
  if (stageName == 'stage') {
    nodeLabel = 'fleet-slave'
  }
} else if (ENV_STATE == 'MERGED' && ENV_TARGET_BRANCH == 'develop') {
  echo "Triggered by pull request merged event, will be released to DEV"
  stageName = 'dev2'
  shouldRelease = true
} else {
  stageName = 'dev2'
  shouldTest = true
  echo "No build will be released"
}

echo "stageName = ${stageName}"
echo "shouldRelease = ${shouldRelease}"

def aws(handle) {
    if (stageName == "live") {
        withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role:'paris2-live-cross-account-access'){
            handle()
        }
    } else {
        withCredentials([[
            $class: 'AmazonWebServicesCredentialsBinding',
            credentialsId: "paris2_jenkins",
            accessKeyVariable: 'AWS_ACCESS_KEY_ID',
            secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
        ]]){
            handle()
        }
    }
}

def notifyBitbucket(stateName, onStage) {
    bitbucketStatusNotify(
        buildState: 'INPROGRESS',
        buildKey: stateName,
        buildName: stateName
    )

    try {
        onStage()
        bitbucketStatusNotify(
        buildState: 'SUCCESSFUL',
        buildKey: stateName,
        buildName: stateName
        )
    } catch (exc) {
        bitbucketStatusNotify(
        buildState: 'FAILED',
        buildKey: stateName,
        buildName: stateName,
        buildDescription: "${stateName} failed!!"
        )
        /* Rethrow to fail the Pipeline properly */
        throw exc
    }
}

def silent_sh(cmd) {
    sh('#!/bin/sh -e\n' + cmd)
}

pipeline {
    agent {
        label "${nodeLabel}"
    }

    parameters{
        string( defaultValue: 'critical', name: 'SNYK_SEVERITY_THRESHOLD', trim: true, description: 'can be critical,high or medium')
        booleanParam(name: 'MigrationJob', defaultValue: true, description: 'select Migration Job to build')
        booleanParam(name: 'triggerWebhook', defaultValue: false)
        string(name: 'tenant', defaultValue: '', description: 'Tenant information (JSON format expected)')
        string(name: 'eventRuleName', defaultValue: '', description: 'Eventbridge Rule Name')
    } 

    environment{
        IS_PR_WEBHOOK = false // Default to false
        SNYK_TOKEN = credentials('sf-snyk-token')
        IMAGE_TAG_VERSION="${stageName}-${env.BUILD_NUMBER}"
        NR_ENABLED_VALUE=0
        GIT_PREVIOUS_SUCCESSFUL_COMMIT = sh(script: 'git rev-parse HEAD~1', returnStdout: true).trim()
        BUILD_ENV="${stageName}"
        IMAGE_REPO_NAME="${accountId}.dkr.ecr.${region}.amazonaws.com/paris2-new-building-${stageName}"
        DOCKER_BUILDKIT="1"
    }

    stages {
        stage('Check if Triggered by PR Webhook') {
            steps {
                script {
                // Detect if the pipeline was triggered by a PR webhook
                if (ENV_STATE == 'OPEN' && ENV_SOURCE_BRANCH && ENV_TARGET_BRANCH) {
                    echo "Pipeline triggered by a PR webhook."
                    IS_PR_WEBHOOK = true
                    shouldTest = true
                    shouldRunCodeAnalysis = true
                    shouldBuild = true
                    shouldRelease = false
                } else {
                    echo "Pipeline was not triggered by a PR webhook."
                }
                }
            }
        }

        stage('Check Branch Name') {
            steps {
                timeout(time: 3, unit: 'MINUTES'){
                script {
                    if (stageName == "live") {
                        // Check if ENV_GIT_BRANCH starts with refs/tags/
                        if (!ENV_GIT_BRANCH.startsWith('refs/tags/')) {
                            error("Deployment can only be triggered with tags. Please use tags for deployment.")
                            }
                        }
                    }
                }
            }
        }

        stage('Checkout') {
            steps {
                notifyBitbucket('Checkout') {
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "${ENV_GIT_BRANCH}"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], submoduleCfg: [],
                        userRemoteConfigs: [[
                        name: 'github',
                        credentialsId: 'fleet_devops',
                        url: "https://<EMAIL>/fleetshipteam/paris2-api-new-building.git"
                        ]]
                    ])
                }
            }
        }


        stage('Check Tag on Branch') {
            steps {
                script {
                    if (stageName == "live") {
                        sh "sudo git checkout master"
                        def branch = 'master'
                        
                        def result = sh(script: "git branch --contains \$(git rev-parse ${ENV_GIT_BRANCH}) | grep '^\\* ${branch}\$'", returnStatus: true)
                        
                        if (result == 0) {
                            echo "${ENV_GIT_BRANCH} is contained within ${branch}"
                        } else {
                            error("${ENV_GIT_BRANCH} is not contained within ${branch}")
                        }
                    } else {
                        echo "Skipping stage check Tag on Branch for ${stageName} "
                    }
                }
            }
        }


        stage('Install Dependencies') {
            steps{
                timeout(time: 10, unit: 'MINUTES'){
                script {
                        sh "npm run install-all"
                        sh "npm install --platform=linuxmusl --arch=x64"
                    }  
                }   
            }
        }

        stage('Test Analysis'){
            when {
                expression {
                    return shouldTest;
                }
            }
            steps{
                timeout(time: 15, unit: 'MINUTES'){
                    script{
                        dir('services/newbuilding-service'){
                            sh '''
                                cd ../../
                                npm run install-all
                                npm run build
                                cd services/newbuilding-service
                                npm install --platform=linuxmusl --arch=x64
                                npm run test:new-building:coverage
                            '''
                        }
                    }
                }
            }
        }

        stage('Code Analysis') {
          when {
            expression {
              return shouldRunCodeAnalysis;
            }
          }
          steps {
            script {
              echo 'Running Code Analysis stage'

              def runAnalysis = { scriptPath ->
                sh "${scriptPath}"

                // Fetch SonarQube quality gate status
                def SONAR_AUTH_TOKEN = sh(
                  script: "aws ssm get-parameter --name '/paris2-sonar-auth-token/${stageName}' --with-decryption --query 'Parameter.Value' --output text",
                  returnStdout: true
                ).trim()

                echo "Sonar Token fetched: ${SONAR_AUTH_TOKEN}"

                def QUALITY_GATE_STATUS = sh(
                  script: """curl -s -u ${SONAR_AUTH_TOKEN}: \
                  "https://sonar-dev.fleetship.com/api/qualitygates/project_status?projectKey=paris2-api-new-building" \
                  | jq -r '.projectStatus.status'""",
                  returnStdout: true
                ).trim()

                echo "Quality Gate Status: ${QUALITY_GATE_STATUS}"

                if ("${QUALITY_GATE_STATUS}" != "OK") {
                    echo "SonarQube quality gate Failed!"
                    notifyBitbucket('Sonar analysis Passed') {
                    echo "Reporting to Bitbucket..."
                  }
                } else {
                  echo "SonarQube quality gate passed!"
                  notifyBitbucket('Sonar analysis Passed') {
                    echo "Reporting to Bitbucket..."
                  }
                }
              }

              aws {
                withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                  try {
                    if (codeAnalysisCommand == "./code_analysis.sh") {
                      echo 'Checking ./code_analysis.sh'
                      def isCodeAnalysisScriptExist = sh script: 'test -f ./code_analysis.sh', returnStatus: true
                      
                      echo "isCodeAnalysisScriptExist: ${isCodeAnalysisScriptExist}"
                      if (isCodeAnalysisScriptExist == 0) {
                        runAnalysis("./code_analysis.sh")
                      } else {
                        echo 'No code_analysis.sh found, skipping it'
                      }
                    } else {
                      echo 'Running codeAnalysisCommand directly'
                      runAnalysis(codeAnalysisCommand)
                    }
                  } catch (Exception e) {
                    bitbucketStatusNotify(buildState: 'FAILED')
                    echo "SonarQube quality gate failed, but continuing with the pipeline."
                  }
                }
              }
            }
          }
        }

       
        stage('Build Application') {
            steps{
                timeout(time: 15, unit: 'MINUTES'){
                    script{
                        aws{
                            def dbMigration = sh(script: "echo '${tenant}' | jq -r '.setupInfra.dbMigration'", returnStdout: true).trim()
                            def deployServices = sh(script: "echo '${tenant}' | jq -r '.setupInfra.deployServices'", returnStdout: true).trim()
                            sh "aws ecr get-login-password --region ${region} | sudo docker login --username AWS --password-stdin ${accountId}.dkr.ecr.${region}.amazonaws.com"
                            if(!params.MigrationJob){
                                sh "IMAGE_REPO_NAME=$IMAGE_REPO_NAME IMAGE_TAG_VERSION=$IMAGE_TAG_VERSION NR_ENABLED_VALUE=$NR_ENABLED_VALUE npm run docker:build:$BUILD_ENV"
                                sh "IMAGE_REPO_NAME=$IMAGE_REPO_NAME IMAGE_TAG_VERSION=$IMAGE_TAG_VERSION npm run docker:push:$BUILD_ENV"
                            }
                            if (params.MigrationJob && (deployServices == "true" || deployServices == "")){
                                sh "IMAGE_REPO_NAME=$IMAGE_REPO_NAME IMAGE_TAG_VERSION=$IMAGE_TAG_VERSION NR_ENABLED_VALUE=$NR_ENABLED_VALUE npm run docker:build:$BUILD_ENV"
                                sh "IMAGE_REPO_NAME=$IMAGE_REPO_NAME IMAGE_TAG_VERSION=$IMAGE_TAG_VERSION npm run docker:push:$BUILD_ENV"
                                dir('packages/migrations'){
                                    sh "sudo docker build -t $IMAGE_REPO_NAME:newbuilding-migration-$IMAGE_TAG_VERSION ."
                                    sh "sudo docker push $IMAGE_REPO_NAME:newbuilding-migration-$IMAGE_TAG_VERSION"
                                }
                            }
                            if(deployServices == "false" && dbMigration == "true" ){
                                dir('packages/migrations'){
                                    sh "sudo docker build -t $IMAGE_REPO_NAME:newbuilding-migration-$IMAGE_TAG_VERSION ."
                                    sh "sudo docker push $IMAGE_REPO_NAME:newbuilding-migration-$IMAGE_TAG_VERSION"
                                }
                            }
                        }    
                    }
                }
            }
        }
    

        stage('Swagger Upload') {
            steps{
                timeout(time: 3, unit: 'MINUTES'){
                    script{
                        aws{
                            sh './swagger.sh'
                        }
                    }
                }
            }
           
        }

        stage('Clean Workspace') {
            steps{
                cleanWs()
            }
        }

        stage('Checkout Helm repo') {
            steps{
                timeout(time: 3, unit: 'MINUTES'){
                    script{
                        echo "My branch is: ${ENV_HELM_BRANCH}"
                        checkout([
                            $class: 'GitSCM',
                            branches: [
                                [name: "${ENV_HELM_BRANCH}"]
                            ],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [], submoduleCfg: [],
                            userRemoteConfigs: [
                                [
                                name: 'github',
                                credentialsId: 'fleet_devops',
                                url: "${ENV_HELM_REPO}"
                                ]
                            ]
                        ])
                    }
                }
            }
        }

        stage('Update Helm Image Tags') {
            steps{
                timeout(time: 5, unit: 'MINUTES'){
                    script{
                        dir('newbuilding'){
                            def dbMigration = sh(script: "echo '${tenant}' | jq -r '.setupInfra.dbMigration'", returnStdout: true).trim()
                            def deployServices = sh(script: "echo '${tenant}' | jq -r '.setupInfra.deployServices'", returnStdout: true).trim()
                            def newbuildingServiceVersion = "paris2-api-new-building-${stageName}-${env.BUILD_NUMBER}"
                            def migrationJobVersion = "newbuilding-migration-${stageName}-${env.BUILD_NUMBER}"
                            if(params.MigrationJob && (deployServices == "true" || deployServices == "")){
                                sh "ENV=${stageName} NEWBUILDING_SERVICE_IMAGE_VERSION=${newbuildingServiceVersion} MIGRATION_JOB_IMAGE_VERSION=${migrationJobVersion} HELMBRANCH=${HELMBRANCH} ./helm-update.sh"
                            }
                            if(!params.MigrationJob && (dbMigration == "false" || dbMigration == "")){
                                sh "ENV=${stageName} NEWBUILDING_SERVICE_IMAGE_VERSION=${newbuildingServiceVersion} HELMBRANCH=${HELMBRANCH} ./helm-update.sh"
                            }
                            if(params.MigrationJob && deployServices == "false" && dbMigration == "true" ){
                                sh "ENV=${stageName} DEPLOYSERVICES=${deployServices} MIGRATION_JOB_IMAGE_VERSION=${migrationJobVersion} HELMBRANCH=${HELMBRANCH} ./helm-update.sh" 
                            }
                            withCredentials([usernamePassword(credentialsId: 'fleet_devops_write', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]){
                                sh ('''
                                    if output=$(git status --porcelain) && [ -z "$output" ]; then
                                        echo "Nothing to commit in helm repo."
                                    else
                                        sudo git push https://${GIT_USERNAME}:${GIT_PASSWORD}@bitbucket.org/fleetshipteam/paris2-new-building-helm-app.git 
                                    fi
                                ''')
                            }
                        }
                    }   
                }
            }
          
        }

        stage('Clean Helm Workspace') {
            steps{
                always{
                    cleanWs()
                }
            }
        }

        stage('Event Execute'){
            steps{
                checkout([
                        $class: 'GitSCM',
                        branches: [[name: "${ENV_GIT_BRANCH}"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], submoduleCfg: [],
                        userRemoteConfigs: [[
                        name: 'github',
                        credentialsId: 'fleet_devops',
                        url: "https://<EMAIL>/fleetshipteam/paris2-api-new-building.git"
                        ]]
                    ])
                timeout(time: 15, unit: 'MINUTES'){
                script {
                        sh "npm run install-all"
                        sh "npm install --platform=linuxmusl --arch=x64"
                    }  
                }   
            }
           
        }
    }
    post {
        success {
            script {
              if (ENV_STAGE_NAME == "live") {
                withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role: 'paris2-live-cross-account-access') {
                  def stageStatus = '0'
                  def finalStatus = '0'
                  withEnv(["JENKINS_JOB_STAGE_STATUS=${stageStatus}", "JENKINS_JOB_FINAL_STATUS=${finalStatus}"]) {
                          sh 'node webhook.js'
                      }
                }
              } else {
                withCredentials([[
                  $class: 'AmazonWebServicesCredentialsBinding',
                  credentialsId: "paris2_jenkins",
                  accessKeyVariable: 'AWS_ACCESS_KEY_ID',
                  secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
                ]]) {
                  def stageStatus = '0'
                  def finalStatus = '0'
                  withEnv(["JENKINS_JOB_STAGE_STATUS=${stageStatus}", "JENKINS_JOB_FINAL_STATUS=${finalStatus}"]) {
                          sh 'node webhook.js'
                      }
                }
              }
            }
        }
        failure {
          script {
            if (ENV_STAGE_NAME == "live") {
              withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role: 'paris2-live-cross-account-access') {
                def stageStatus = '1'
                def finalStatus = '0'
                withEnv(["JENKINS_JOB_STAGE_STATUS=${stageStatus}", "JENKINS_JOB_FINAL_STATUS=${finalStatus}"]) {
                        sh 'node webhook.js'
                        cleanWs()
                    }
              }
            } else {
              withCredentials([[
                $class: 'AmazonWebServicesCredentialsBinding',
                credentialsId: "paris2_jenkins",
                accessKeyVariable: 'AWS_ACCESS_KEY_ID',
                secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
              ]]) {
                def stageStatus = '1'
                def finalStatus = '0'
                withEnv(["JENKINS_JOB_STAGE_STATUS=${stageStatus}", "JENKINS_JOB_FINAL_STATUS=${finalStatus}"]) {
                        sh 'node webhook.js'
                        cleanWs()
                    }
              }
            }
          }
        }

    }
}

